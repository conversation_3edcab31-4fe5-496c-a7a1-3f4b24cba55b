<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woyaotuanjian.modules.system.mapper.SysConfigMapper">

    <!-- 根据配置键获取配置值 -->
    <select id="getConfigValueByKey" resultType="java.lang.String">
        SELECT config_value 
        FROM sys_config 
        WHERE config_key = #{configKey} 
        AND status = 1
        LIMIT 1
    </select>

</mapper>