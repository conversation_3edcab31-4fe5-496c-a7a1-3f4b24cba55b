<template>
  <div class="poster-template-editor" :style="{ height: editorHeight }">
    <!-- 顶部导航栏 -->
    <div class="editor-header">
      <!-- 面包屑导航 -->
      <div class="header-left">
        <a-breadcrumb>
          <a-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
            <a-icon :type="item.icon" v-if="item.icon" />
            <router-link :to="item.path" v-if="index < breadcrumbItems.length - 1">{{ item.title }}</router-link>
            <span v-else>{{ item.title }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      
      <!-- 中间操作区 -->
      <div class="header-center">
        <div class="template-info">
          <a-input 
            v-model="templateName" 
            placeholder="输入模板名称" 
            class="template-name-input" />
        </div>
        
        <div class="editor-actions">
          <a-button-group size="small">
            <a-button @click="undo" :disabled="!canUndo" title="撤销">
              <a-icon type="undo" />
            </a-button>
            <a-button @click="redo" :disabled="!canRedo" title="重做">
              <a-icon type="redo" />
            </a-button>
          </a-button-group>
          
          <a-button-group size="small" style="margin-left: 12px;">
            <a-button @click="deleteSelected" :disabled="!selectedObject" title="删除">
              <a-icon type="delete" />
            </a-button>
            <a-button @click="clearCanvas" title="清空画布">
              <a-icon type="clear" />
            </a-button>
          </a-button-group>
        </div>
      </div>
      
      <!-- 右侧操作区 -->
      <div class="header-right">
        <a-input 
          v-model="templateDesc" 
          placeholder="模板描述（可选）" 
          class="template-desc-input" />
        <a-button @click="goBack" size="small" style="margin-left: 12px;">
          <a-icon type="arrow-left" />
          返回
        </a-button>
        <a-button @click="saveTemplate" type="primary" size="small" style="margin-left: 8px;">
          <a-icon type="save" />
          保存
        </a-button>
      </div>
    </div>
    
    <!-- 主编辑区域 -->
    <div class="editor-main">
      <!-- 左侧工具栏 -->
      <div class="editor-toolbar">
        <!-- 模式切换按钮 -->
        <div class="mode-switcher">
          <a-button-group size="small">
            <a-button 
              :type="toolbarMode === 'tools' ? 'primary' : 'default'"
              @click="toolbarMode = 'tools'">
              <a-icon type="tool" />
              工具
            </a-button>
            <a-button 
              :type="toolbarMode === 'materials' ? 'primary' : 'default'"
              @click="toolbarMode = 'materials'">
              <a-icon type="picture" />
              素材
            </a-button>
          </a-button-group>
        </div>

        <!-- 工具模式 -->
        <div v-show="toolbarMode === 'tools'" class="tools-panel">
          <!-- 基础工具 -->
          <div class="tool-section">
            <div class="section-header">基础工具</div>
            <div class="tool-grid">
              <div class="tool-item" @click="setActiveTool('select')" :class="{ active: activeTool === 'select' }" title="选择">
                <a-icon type="cursor" />
                <span>选择</span>
              </div>
              <div class="tool-item" @click="addText" title="文本">
                <a-icon type="font-size" />
                <span>文本</span>
              </div>
              <div class="tool-item" @click="addImage" title="图片">
                <a-icon type="picture" />
                <span>图片</span>
              </div>
              <div class="tool-item" @click="addQRCode" title="二维码">
                <a-icon type="qrcode" />
                <span>二维码</span>
              </div>
            </div>
          </div>

          <!-- 形状工具 -->
          <div class="tool-section">
            <div class="section-header">形状工具</div>
            <div class="tool-grid">
              <div class="tool-item" @click="addRect" title="矩形">
                <a-icon type="border" />
                <span>矩形</span>
              </div>
              <div class="tool-item" @click="addCircle" title="圆形">
                <a-icon type="layout" />
                <span>圆形</span>
              </div>
              <div class="tool-item" @click="showCustomSvgModal" title="自定义SVG">
                <a-icon type="file-text" />
                <span>SVG</span>
              </div>
            </div>
          </div>

          <!-- 层级控制 -->
          <div class="tool-section">
            <div class="section-header">层级控制</div>
            <div class="layer-controls">
              <a-button size="small" @click="bringToFront" :disabled="!selectedObject" title="置顶">
                <a-icon type="vertical-align-top" />
              </a-button>
              <a-button size="small" @click="bringForward" :disabled="!selectedObject" title="上移">
                <a-icon type="arrow-up" />
              </a-button>
              <a-button size="small" @click="sendBackwards" :disabled="!selectedObject" title="下移">
                <a-icon type="arrow-down" />
              </a-button>
              <a-button size="small" @click="sendToBack" :disabled="!selectedObject" title="置底">
                <a-icon type="vertical-align-bottom" />
              </a-button>
            </div>
          </div>
        </div>

        <!-- 素材模式 -->
        <div v-show="toolbarMode === 'materials'" class="materials-panel">
          <!-- 素材类型切换 -->
          <div class="material-types">
            <a-radio-group v-model="materialType" size="small" button-style="solid">
              <a-radio-button value="images">图片</a-radio-button>
              <a-radio-button value="unsplash">在线图库</a-radio-button>
              <a-radio-button value="svgs">图标</a-radio-button>
              <a-radio-button value="fonts">字体</a-radio-button>
            </a-radio-group>
          </div>

          <!-- 图片素材 -->
          <div v-show="materialType === 'images'" class="material-content">
            <div class="material-categories">
              <a-select 
                v-model="selectedImageCategory" 
                @change="loadImageMaterials" 
                size="small" 
                style="width: 100%; margin-bottom: 12px;"
                placeholder="选择分类">
                <a-select-option value="">全部分类</a-select-option>
                <a-select-option 
                  v-for="category in imageCategories" 
                  :key="category.id" 
                  :value="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </div>
            <!-- 上传按钮 -->
            <div class="upload-section" style="margin-bottom: 12px;">
              <a-upload
                name="file"
                :multiple="false"
                :show-upload-list="false"
                :before-upload="beforeImageUpload"
                accept="image/*">
                <a-button size="small" type="primary" icon="upload" style="width: 100%;">
                  上传图片素材
                </a-button>
              </a-upload>
            </div>
            <div class="material-grid">
              <div 
                v-for="material in imageMaterials" 
                :key="material.id"
                class="material-item"
                @click="addImageMaterial(material)"
                :title="material.name">
                <img :src="material.thumbnailUrl || material.fileUrl" :alt="material.name" />
                <span class="material-name">{{ material.name }}</span>
              </div>
              <div v-if="imageMaterials.length === 0" class="material-empty">
                暂无图片素材
              </div>
            </div>
          </div>

          <!-- 在线图库素材 -->
          <div v-show="materialType === 'unsplash'" class="material-content">
            <div class="unsplash-search">
              <a-input-search
                v-model="unsplashQuery"
                placeholder="搜索免费图片..."
                @search="searchUnsplashImages"
                size="small"
                style="margin-bottom: 12px;" />
            </div>
            <a-spin :spinning="unsplashLoading">
              <div class="material-grid">
                <div 
                  v-for="photo in unsplashImages" 
                  :key="photo.id"
                  class="material-item unsplash-item"
                  @click="addUnsplashImage(photo)"
                :title="photo.description || photo.alt_description">
                <img :src="photo.urls.small" :alt="photo.alt_description" />
                <span class="material-name">{{ photo.user.name }}</span>
                </div>
                <div v-if="unsplashImages.length === 0 && !unsplashLoading" class="material-empty">
                  搜索免费高质量图片
                </div>
              </div>
            </a-spin>
          </div>

          <!-- 图标素材 -->
          <div v-show="materialType === 'svgs'" class="material-content">
            <div class="material-categories">
              <a-select 
                v-model="selectedSvgCategory" 
                @change="loadSvgMaterials" 
                size="small" 
                style="width: 100%; margin-bottom: 12px;"
                placeholder="选择分类">
                <a-select-option value="">全部分类</a-select-option>
                <a-select-option 
                  v-for="category in svgCategories" 
                  :key="category.id" 
                  :value="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </div>
            <!-- 上传按钮 -->
            <div class="upload-section" style="margin-bottom: 12px;">
              <a-button size="small" @click="showSvgUploadModal" style="width: 100%; margin-bottom: 6px;" icon="plus">
                添加SVG图标
              </a-button>
            </div>
            <div class="material-grid">
              <div 
                v-for="material in svgMaterials" 
                :key="material.id"
                class="material-item svg-item"
                @click="addSvgMaterial(material)"
                :title="material.name">
                <div class="svg-preview" v-html="getSvgPreview(material.svgContent)"></div>
                <span class="material-name">{{ material.name }}</span>
              </div>
              <div v-if="svgMaterials.length === 0" class="material-empty">
                暂无图标素材
              </div>
            </div>
          </div>

          <!-- 字体素材 -->
          <div v-show="materialType === 'fonts'" class="material-content">
            <div class="material-categories">
              <a-select
                v-model="selectedFontCategory"
                @change="loadFontMaterials"
                size="small"
                style="width: 100%; margin-bottom: 12px;"
                placeholder="选择分类">
                <a-select-option value="">全部分类</a-select-option>
                <a-select-option
                  v-for="category in fontCategories"
                  :key="category.id"
                  :value="category.id">
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </div>
            <!-- 上传按钮 -->
            <div class="upload-section" style="margin-bottom: 12px;">
              <a-button size="small" @click="showFontUploadModal" style="width: 100%; margin-bottom: 6px;" icon="plus">
                上传字体文件
              </a-button>
            </div>
            <div class="material-grid font-grid">
              <div
                v-for="material in fontMaterials"
                :key="material.id"
                class="material-item font-item"
                @click="applyFontToSelected(material)"
                :title="material.name">
                <div class="font-preview" :style="{ fontFamily: getFontFamily(material) }">
                  Aa字体
                </div>
                <span class="material-name">{{ material.name }}</span>
              </div>
              <div v-if="fontMaterials.length === 0" class="material-empty">
                暂无字体素材
              </div>
            </div>
          </div>

        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="editor-canvas-area">
        <div class="canvas-toolbar">
          <div class="canvas-controls">
            <span class="zoom-info">{{ Math.round(canvasZoom * 100) }}%</span>
            <a-button-group size="small">
              <a-button @click="zoomOut" title="缩小">
                <a-icon type="zoom-out" />
              </a-button>
              <a-button @click="resetZoom" title="适合窗口">
                <a-icon type="border" />
              </a-button>
              <a-button @click="zoomIn" title="放大">
                <a-icon type="zoom-in" />
              </a-button>
            </a-button-group>
          </div>
        </div>
        
        <div class="canvas-container">
          <div class="canvas-scroll-area" :style="{ 
            width: (canvasW * canvasZoom + 40) + 'px', 
            height: (canvasH * canvasZoom + 40) + 'px',
            minWidth: '100%',
            minHeight: '100%'
          }">
            <div class="canvas-wrapper" :style="{ 
              width: canvasW + 'px', 
              height: canvasH + 'px', 
              transform: `translate(-50%, -50%) scale(${canvasZoom})` 
            }">
              <canvas ref="fabricCanvas" :width="canvasW" :height="canvasH"></canvas>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧属性面板 -->
      <div class="editor-properties">
        <a-tabs>
          <a-tab-pane key="properties" tab="属性">
            <div class="property-panel" v-if="selectedObject">
              <!-- 通用属性 -->
              <div class="property-section">
                <div class="section-title">位置和尺寸</div>
                <div class="form-grid">
                  <div class="form-item">
                    <label>X</label>
                    <a-input-number 
                      :value="selectedObject && selectedObject.left" 
                      @change="updateProperty('left', $event)"
                      size="small">
                    </a-input-number>
                  </div>
                  <div class="form-item">
                    <label>Y</label>
                    <a-input-number 
                      :value="selectedObject && selectedObject.top" 
                      @change="updateProperty('top', $event)"
                      size="small">
                    </a-input-number>
                  </div>
                  <div class="form-item">
                    <label>宽度</label>
                    <a-input-number 
                      :value="selectedObject && selectedObject.width && selectedObject.scaleX ? selectedObject.width * selectedObject.scaleX : 0" 
                      @change="updateSize('width', $event)"
                      size="small">
                    </a-input-number>
                  </div>
                  <div class="form-item">
                    <label>高度</label>
                    <a-input-number 
                      :value="selectedObject && selectedObject.height && selectedObject.scaleY ? selectedObject.height * selectedObject.scaleY : 0" 
                      @change="updateSize('height', $event)"
                      size="small">
                    </a-input-number>
                  </div>
                </div>
              </div>
              
              <div class="property-section">
                <div class="section-title">层级控制</div>
                <div class="button-grid">
                  <a-button size="small" @click="bringForward" :disabled="!selectedObject">上移</a-button>
                  <a-button size="small" @click="sendBackwards" :disabled="!selectedObject">下移</a-button>
                  <a-button size="small" @click="bringToFront" :disabled="!selectedObject">置顶</a-button>
                  <a-button size="small" @click="sendToBack" :disabled="!selectedObject">置底</a-button>
                </div>
              </div>
              
              <!-- 文本属性 -->
              <div class="property-section" v-if="selectedObject && (selectedObject.type === 'text' || selectedObject.type === 'i-text' || selectedObject.type === 'textbox')">
                <div class="section-title">文本属性</div>
                <div class="form-item full-width">
                  <label>文本内容</label>
                  <a-textarea 
                    :value="selectedObject && selectedObject.text" 
                    @change="updateProperty('text', $event.target.value)"
                    :rows="2"
                    size="small"
                    data-property="text"
                    placeholder="双击文本对象或在此编辑内容">
                  </a-textarea>
                </div>
                <div class="form-item full-width">
                  <label>字体</label>
                  <a-select
                    :value="getCurrentFontFamily()"
                    @change="updateProperty('fontFamily', $event)"
                    size="small"
                    style="width: 100%;"
                    placeholder="选择字体">
                    <a-select-opt-group label="系统字体">
                      <a-select-option value="微软雅黑">微软雅黑</a-select-option>
                      <a-select-option value="宋体">宋体</a-select-option>
                      <a-select-option value="黑体">黑体</a-select-option>
                      <a-select-option value="楷体">楷体</a-select-option>
                      <a-select-option value="Arial">Arial</a-select-option>
                      <a-select-option value="Times New Roman">Times New Roman</a-select-option>
                      <a-select-option value="Helvetica">Helvetica</a-select-option>
                      <a-select-option value="Georgia">Georgia</a-select-option>
                    </a-select-opt-group>
                    <a-select-opt-group label="自定义字体" v-if="fontMaterials.filter(f => f.fileUrl).length > 0">
                      <a-select-option
                        v-for="font in fontMaterials.filter(f => f.fileUrl)"
                        :key="font.id"
                        :value="getFontFamily(font)"
                        :title="`${font.name} (ID: ${font.id})`">
                        {{ font.name }}
                      </a-select-option>
                    </a-select-opt-group>
                  </a-select>
                </div>
                <div class="form-grid">
                  <div class="form-item">
                    <label>字体大小</label>
                    <a-input-number
                      :value="selectedObject && selectedObject.fontSize"
                      @change="updateProperty('fontSize', $event)"
                      :min="8"
                      :max="200"
                      size="small">
                    </a-input-number>
                  </div>
                  <div class="form-item">
                    <label>字间距</label>
                    <a-input-number
                      :value="selectedObject && selectedObject.charSpacing"
                      @change="updateProperty('charSpacing', $event)"
                      :min="-100"
                      :max="1000"
                      :step="10"
                      size="small">
                    </a-input-number>
                  </div>
                </div>
                <div class="form-grid">
                  <div class="form-item">
                    <label>字体样式</label>
                    <a-button-group size="small">
                      <a-button 
                        :type="selectedObject && selectedObject.fontWeight === 'bold' ? 'primary' : 'default'"
                        @click="updateProperty('fontWeight', selectedObject && selectedObject.fontWeight === 'bold' ? 'normal' : 'bold')"
                        style="font-weight: bold; width: 32px;"
                        title="加粗">
                        B
                      </a-button>
                      <a-button 
                        :type="selectedObject && selectedObject.fontStyle === 'italic' ? 'primary' : 'default'"
                        @click="updateProperty('fontStyle', selectedObject && selectedObject.fontStyle === 'italic' ? 'normal' : 'italic')"
                        style="font-style: italic; width: 32px;"
                        title="斜体">
                        I
                      </a-button>
                      <a-button 
                        :type="selectedObject && selectedObject.underline ? 'primary' : 'default'"
                        @click="updateProperty('underline', !(selectedObject && selectedObject.underline))"
                        style="text-decoration: underline; width: 32px;"
                        title="下划线">
                        U
                      </a-button>
                      <a-button 
                        :type="selectedObject && selectedObject.linethrough ? 'primary' : 'default'"
                        @click="updateProperty('linethrough', !(selectedObject && selectedObject.linethrough))"
                        style="text-decoration: line-through; width: 32px;"
                        title="删除线">
                        S
                      </a-button>
                    </a-button-group>
                  </div>
                </div>
                <div class="form-item full-width">
                  <label>字体颜色</label>
                  <div class="color-input">
                    <a-input 
                      :value="getColorInputValue(selectedObject && selectedObject.fill)" 
                      @change="updateProperty('fill', $event.target.value)"
                      size="small">
                    </a-input>
                    <div class="color-preview" @click="showColorPicker('font')">
                      <div class="color-swatch" :style="{ backgroundColor: getColorPreviewValue(selectedObject && selectedObject.fill) }"></div>
                    </div>
                  </div>
                </div>
                <div class="form-item full-width">
                  <label>文本对齐</label>
                  <a-select 
                    :value="selectedObject && selectedObject.textAlign" 
                    @change="updateProperty('textAlign', $event)"
                    size="small">
                    <a-select-option value="left">左对齐</a-select-option>
                    <a-select-option value="center">居中</a-select-option>
                    <a-select-option value="right">右对齐</a-select-option>
                  </a-select>
                </div>
                <div class="form-item full-width">
                  <label>透明度 {{ selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100 }}%</label>
                  <a-slider 
                    :value="selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100" 
                    @change="updateProperty('opacity', $event / 100)"
                    :min="0"
                    :max="100"
                    :step="1">
                  </a-slider>
                </div>
                <div class="form-item full-width">
                  <label>变量标识</label>
                  <a-input 
                    :value="selectedObject && selectedObject.variableKey" 
                    @change="updateProperty('variableKey', $event.target.value)"
                    placeholder="如: tripName"
                    size="small">
                  </a-input>
                </div>
              </div>
              
              <!-- 普通图片属性 -->
              <div class="property-section" v-if="selectedObject && (selectedObject.type === 'image' || selectedObject.originalType === 'image')">
                <div class="section-title">图片属性</div>
                <div class="form-item full-width">
                  <label>图片URL</label>
                  <a-input 
                    :value="selectedObject.src || selectedObject.originalSrc" 
                    @change="updateProperty('src', $event.target.value)"
                    size="small"
                    :placeholder="selectedObject.isPlaceholder ? '图片加载失败，请重新输入URL' : '请输入图片URL'">
                  </a-input>
                  <div v-if="selectedObject.isPlaceholder" style="margin-top: 4px;">
                    <small style="color: #ff4d4f;">
                      <a-icon type="exclamation-circle" /> 图片加载失败，显示为占位符
                    </small>
                  </div>
                </div>
                <div class="form-item full-width">
                  <label>透明度 {{ selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100 }}%</label>
                  <a-slider 
                    :value="selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100" 
                    @change="updateProperty('opacity', $event / 100)"
                    :min="0"
                    :max="100"
                    :step="1">
                  </a-slider>
                </div>
                <div class="form-item full-width">
                  <label>变量标识</label>
                  <a-input 
                    :value="selectedObject && selectedObject.variableKey" 
                    @change="updateProperty('variableKey', $event.target.value)"
                    placeholder="如: imgUrl"
                    size="small">
                  </a-input>
                </div>
              </div>
              
              
              <!-- 形状属性（含 path） -->
              <div class="property-section" v-if="selectedObject && (selectedObject.type === 'rect' || selectedObject.type === 'circle' || selectedObject.type === 'path')">
                <div class="section-title">形状属性</div>
                <div class="form-item full-width">
                  <label>填充颜色</label>
                  <div class="color-input">
                    <a-input 
                      :value="getColorInputValue(selectedObject && selectedObject.fill)" 
                      @change="updateProperty('fill', $event.target.value)"
                      size="small">
                    </a-input>
                    <div class="color-preview" @click="showColorPicker('shape-fill')">
                      <div class="color-swatch" :style="{ backgroundColor: getColorPreviewValue(selectedObject && selectedObject.fill) }"></div>
                    </div>
                  </div>
                </div>
                <div class="form-item full-width">
                  <label>透明度 {{ selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100 }}%</label>
                  <a-slider 
                    :value="selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100" 
                    @change="updateProperty('opacity', $event / 100)"
                    :min="0"
                    :max="100"
                    :step="1">
                  </a-slider>
                </div>
                <div class="form-item full-width">
                  <label>边框颜色</label>
                  <div class="color-input">
                    <a-input 
                      :value="getColorInputValue(selectedObject && selectedObject.stroke)" 
                      @change="updateProperty('stroke', $event.target.value)"
                      size="small">
                    </a-input>
                    <div class="color-preview" @click="showColorPicker('shape-stroke')">
                      <div class="color-swatch" :style="{ backgroundColor: getColorPreviewValue(selectedObject && selectedObject.stroke) }"></div>
                    </div>
                  </div>
                </div>
                <div class="form-grid">
                  <div class="form-item">
                    <label>边框宽度</label>
                    <a-input-number 
                      :value="selectedObject && selectedObject.strokeWidth" 
                      @change="updateProperty('strokeWidth', $event)"
                      :min="0"
                      :max="20"
                      size="small">
                    </a-input-number>
                  </div>
                  <!-- 矩形圆角属性 -->
                  <div class="form-item" v-if="selectedObject && selectedObject.type === 'rect'">
                    <label>圆角半径</label>
                    <a-input-number 
                      :value="selectedObject && (selectedObject.rx || 0)" 
                      @change="updateRectRadius($event)"
                      :min="0"
                      :max="100"
                      size="small">
                    </a-input-number>
                  </div>
                </div>
              </div>
              
              <!-- SVG图标属性 -->
              <div class="property-section" v-if="selectedObject && selectedObject.originalSvg">
                <div class="section-title">SVG图标属性</div>
                
                <!-- 多颜色支持 -->
                <div class="form-item full-width" v-if="selectedObject.svgColors && selectedObject.svgColors.length > 0">
                  <label>多颜色设置</label>
                  <div class="multi-color-inputs">
                    <div 
                      v-for="(colorInfo, index) in selectedObject.svgColors" 
                      :key="index"
                      class="color-input-group">
                      <div class="color-label">
                        <span class="color-name">{{ colorInfo.name || `颜色${index + 1}` }}</span>
                        <span class="color-original" v-if="colorInfo.originalColor">
                          (原色: {{ colorInfo.originalColor }})
                        </span>
                      </div>
                      <div class="color-input">
                        <a-input 
                          :value="colorInfo.currentColor" 
                          @change="updateSvgMultiColor(index, $event.target.value)"
                          size="small"
                          placeholder="输入颜色值">
                        </a-input>
                        <div class="color-preview" @click="showColorPicker('svg-multi', index)">
                          <div class="color-swatch" :style="{ backgroundColor: colorInfo.currentColor }"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 单颜色支持（向后兼容） -->
                <div class="form-item full-width" v-if="!selectedObject.svgColors || selectedObject.svgColors.length === 0">
                  <label>图标颜色</label>
                  <div class="color-input">
                    <a-input 
                      :value="getColorInputValue(selectedObject && selectedObject.fill)" 
                      @change="updateSvgColor($event.target.value)"
                      size="small">
                    </a-input>
                    <div class="color-preview" @click="showColorPicker('svg-single')">
                      <div class="color-swatch" :style="{ backgroundColor: getColorPreviewValue(selectedObject && selectedObject.fill) }"></div>
                    </div>
                  </div>
                </div>
                
                <div class="form-item full-width">
                  <label>透明度 {{ selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100 }}%</label>
                  <a-slider 
                    :value="selectedObject ? Math.round((selectedObject.opacity || 1) * 100) : 100" 
                    @change="updateProperty('opacity', $event / 100)"
                    :min="0"
                    :max="100"
                    :step="1">
                  </a-slider>
                </div>
                <div class="form-item full-width">
                  <label>变量标识</label>
                  <a-input 
                    :value="selectedObject && selectedObject.variableKey" 
                    @change="updateProperty('variableKey', $event.target.value)"
                    placeholder="如: iconColor"
                    size="small">
                  </a-input>
                </div>
              </div>
            </div>
            <div v-else class="property-panel">
              <div class="property-section">
                <div class="section-title">画布设置</div>
                <div class="form-grid">
                  <div class="form-item">
                    <label>宽度</label>
                    <a-input-number 
                      :value="canvasW" 
                      @change="val => updateCanvasSize(val, canvasH)"
                      :min="100" :max="4000" size="small" />
                  </div>
                  <div class="form-item">
                    <label>高度</label>
                    <a-input-number 
                      :value="canvasH" 
                      @change="val => updateCanvasSize(canvasW, val)"
                      :min="100" :max="4000" size="small" />
                  </div>
                </div>
                <div class="form-item full-width">
                  <label>背景颜色</label>
                  <div class="color-input">
                    <a-input :value="canvasBg" @change="e => updateCanvasBg(e.target.value)" size="small">
                    </a-input>
                    <div class="color-preview" @click="showColorPicker('canvas-bg')">
                      <div class="color-swatch" :style="{ backgroundColor: canvasBg }"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="variables" tab="变量">
            <div class="variable-panel">
              <div class="property-section">
                <div class="section-title">模板变量</div>
                <div class="variable-list">
                  <div v-for="variable in variables" :key="variable.key" class="variable-item">
                    <div class="variable-info">
                      <div class="variable-key">{{ variable.key }}</div>
                      <div class="variable-name">{{ variable.name }}</div>
                    </div>
                    <a-button size="small" type="text" danger @click="removeVariable(variable.key)">
                      <a-icon type="delete" />
                    </a-button>
                  </div>
                  <div v-if="variables.length === 0" class="empty-state">
                    暂无变量，点击下方按钮添加
                  </div>
                </div>
                <a-button @click="showAddVariableModal" type="dashed" block size="small" style="margin-top: 8px;">
                  <a-icon type="plus" /> 添加变量
                </a-button>
              </div>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
    
    <!-- 隐藏的图片选择器 -->
    <input 
      ref="imageInput" 
      type="file" 
      accept="image/*" 
      style="display:none" 
      @change="handleImageFileChange" />
    
    <!-- 添加变量弹窗 -->
    <a-modal
      title="添加变量"
      :visible="addVariableVisible"
      @ok="handleAddVariable"
      @cancel="addVariableVisible = false">
      <a-form :form="variableForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="变量标识">
          <a-input v-decorator="['key', { rules: [{ required: true, message: '请输入变量标识' }] }]" placeholder="如: tripName"></a-input>
        </a-form-item>
        <a-form-item label="变量名称">
          <a-input v-decorator="['name', { rules: [{ required: true, message: '请输入变量名称' }] }]" placeholder="如: 行程名称"></a-input>
        </a-form-item>
        <a-form-item label="变量类型">
          <a-select v-decorator="['type', { rules: [{ required: true, message: '请选择变量类型' }] }]">
            <a-select-option value="text">文本</a-select-option>
            <a-select-option value="image">图片</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="默认值">
          <a-input v-decorator="['defaultValue']" placeholder="默认值"></a-input>
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-decorator="['description']" :rows="3" placeholder="变量描述"></a-textarea>
        </a-form-item>
      </a-form>
    </a-modal>
    
    
    <!-- 颜色选择器弹窗 -->
    <a-modal
      title="选择颜色"
      :visible="colorPickerVisible"
      @ok="applyCustomColor"
      @cancel="colorPickerVisible = false"
      :width="280"
      :footer="null"
      centered>
      <div class="color-picker-modal">
        <!-- 调色板 -->
        <div class="sketch-picker-wrapper">
          <sketch-picker v-model="currentColor" @input="onColorChange" />
        </div>
        
        <!-- 当前颜色预览 -->
        <div class="current-color-preview">
          <div class="color-preview-label">当前颜色:</div>
          <div class="color-preview-box">
            <div 
              class="color-preview-sample" 
              :style="{ backgroundColor: currentColor.hex || '#000000' }">
            </div>
            <span class="color-preview-text">{{ currentColor.hex || '#000000' }}</span>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="color-picker-actions">
          <a-button @click="applyCustomColor" type="primary" block>
            应用颜色
          </a-button>
        </div>
      </div>
    </a-modal>
    
    <!-- SVG素材上传弹窗 -->
    <a-modal
      title="添加SVG图标素材"
      :visible="svgUploadVisible"
      @ok="uploadSvgMaterial"
      @cancel="svgUploadVisible = false"
      :confirmLoading="uploading"
      :width="600">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="素材名称" required>
          <a-input v-model="uploadForm.name" placeholder="请输入素材名称" />
        </a-form-item>
        <a-form-item label="分类">
          <a-select v-model="uploadForm.categoryId" placeholder="选择分类（可选）" allowClear>
            <a-select-option 
              v-for="category in svgCategories" 
              :key="category.id" 
              :value="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="标签">
          <a-input v-model="uploadForm.tags" placeholder="请输入标签，多个标签用逗号分隔" />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model="uploadForm.description" placeholder="请输入素材描述（可选）" :rows="2" />
        </a-form-item>
        <a-form-item label="SVG内容" required>
          <a-textarea 
            v-model="uploadForm.svgContent" 
            placeholder="请输入SVG代码" 
            :rows="8" 
            style="font-family: monospace;" />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 自定义SVG弹窗 -->
    <a-modal
      title="添加自定义SVG"
      :visible="customSvgVisible"
      @ok="addCustomSvg"
      @cancel="customSvgVisible = false"
      :confirmLoading="false"
      :width="600">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="SVG代码" required>
          <a-textarea 
            v-model="customSvgCode" 
            placeholder="请粘贴SVG代码，例如：&lt;svg...&gt;&lt;/svg&gt;" 
            :rows="12" 
            style="font-family: monospace;" />
        </a-form-item>
        <a-form-item v-if="customSvgCode">
          <div style="text-align: center; padding: 10px; border: 1px dashed #d9d9d9; border-radius: 4px;">
            <div style="font-size: 12px; color: #666; margin-bottom: 8px;">预览：</div>
            <div v-html="getSvgPreview(customSvgCode)" style="max-height: 100px; overflow: hidden;"></div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 字体上传弹窗 -->
    <a-modal
      title="上传字体文件"
      :visible="fontUploadVisible"
      @ok="uploadFontMaterial"
      @cancel="cancelFontUpload"
      :confirmLoading="uploading"
      :width="600">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="字体文件" required>
          <a-upload
            :beforeUpload="beforeFontUpload"
            :fileList="[]"
            accept=".ttf,.otf,.woff,.woff2">
            <a-button icon="upload">选择字体文件</a-button>
          </a-upload>
          <div v-if="selectedFontFile" style="margin-top: 8px; color: #52c41a; font-size: 12px;">
            已选择: {{ selectedFontFile.name }} ({{ (selectedFontFile.size / 1024 / 1024).toFixed(2) }}MB)
          </div>
          <div style="margin-top: 8px; color: #666; font-size: 12px;">
            支持格式：.ttf、.otf、.woff、.woff2
          </div>
        </a-form-item>
        <a-form-item label="字体名称" required>
          <a-input v-model="fontUploadForm.name" placeholder="请输入字体名称" />
        </a-form-item>
        <a-form-item label="分类">
          <a-select v-model="fontUploadForm.categoryId" placeholder="选择分类（可选）" allowClear>
            <a-select-option
              v-for="category in fontCategories"
              :key="category.id"
              :value="category.id">
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="标签">
          <a-input v-model="fontUploadForm.tags" placeholder="请输入标签，多个标签用逗号分隔" />
        </a-form-item>
        <a-form-item label="描述">
          <a-textarea v-model="fontUploadForm.description" placeholder="请输入字体描述" :rows="3" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 二维码弹窗 -->
    <a-modal
      title="添加二维码"
      :visible="qrcodeVisible"
      @ok="handleAddQRCode"
      @cancel="qrcodeVisible = false">
      <a-form :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="二维码内容">
          <a-textarea v-model="qrcodeText" placeholder="请输入二维码内容" :rows="3"></a-textarea>
        </a-form-item>
        <a-form-item label="尺寸">
          <a-input-number v-model="qrcodeSize" :min="50" :max="500" placeholder="二维码尺寸"></a-input-number>
        </a-form-item>
        <a-form-item label="前景色">
          <div class="color-input">
            <a-input v-model="qrcodeForeground" placeholder="前景色"></a-input>
            <div class="color-preview" @click="showColorPicker('qrcode-fg')">
              <div class="color-swatch" :style="{ backgroundColor: qrcodeForeground }"></div>
            </div>
          </div>
        </a-form-item>
        <a-form-item label="背景色">
          <div class="color-input">
            <a-input v-model="qrcodeBackground" placeholder="背景色"></a-input>
            <div class="color-preview" @click="showColorPicker('qrcode-bg')">
              <div class="color-swatch" :style="{ backgroundColor: qrcodeBackground }"></div>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
// 优化后的导入 - 使用Sketch调色板
import { Sketch as SketchPicker } from 'vue-color'
import QRCode from 'qrcode'
import { createApi } from 'unsplash-js'
// fabric-history 需要在 fabric 加载后动态导入

// 初始化 Unsplash API
const unsplash = createApi({
  accessKey: '*******************************************'
})

export default {
  name: 'PosterTemplateEditor',
  components: {
    SketchPicker
  },
  props: {
    templateData: {
      type: Object,
      default: null
    },
    width: {
      type: Number,
      default: 750
    },
    height: {
      type: Number,
      default: 1334
    }
  },
  data() {
    return {
      canvas: null,
      fabric: null,
      pageTitle: '海报模板编辑',
      selectedObject: null,
      canUndo: false,
      canRedo: false,
      variables: [],
      addVariableVisible: false,
      variableForm: this.$form.createForm(this),
      canvasW: 794,
      canvasH: 1123,
      canvasBg: '#ffffff',
      templateId: null,
      templateName: '',
      templateDesc: '',
      sortOrder: 0,
      saving: false,
      isInitializing: false,
      // 工具栏模式控制
      toolbarMode: 'tools', // 'tools' | 'materials'
      materialType: 'images', // 'images' | 'unsplash' | 'svgs' | 'fonts'
      lastEnterKey: '',
      activeTool: 'select',
      canvasZoom: 1,
      editorHeight: 'auto',
      
      // 素材相关数据
      materialCategories: [],
      imageCategories: [],
      svgCategories: [],
      fontCategories: [],
      imageMaterials: [],
      svgMaterials: [],
      fontMaterials: [],
      selectedImageCategory: '',
      selectedSvgCategory: '',
      selectedFontCategory: '',
      
      // Unsplash 相关
      unsplashImages: [],
      unsplashQuery: '',
      unsplashLoading: false,
      
      // 二维码相关
      qrcodeVisible: false,
      qrcodeText: '',
      qrcodeSize: 200,
      qrcodeForeground: '#000000',
      qrcodeBackground: '#ffffff',
      
      // 素材上传
      svgUploadVisible: false,
      uploadForm: {
        name: '',
        categoryId: null,
        tags: '',
        description: '',
        svgContent: ''
      },
      uploading: false,

      // 字体上传相关
      fontUploadVisible: false,
      fontUploadForm: {
        name: '',
        categoryId: null,
        tags: '',
        description: ''
      },
      selectedFontFile: null,
      
      // 自定义SVG
      customSvgVisible: false,
      customSvgCode: '',
      
      // 键盘事件处理器引用
      _keyboardHandler: null,
      
      // 常用颜色 - 更流行的分组配色
      commonColors: {
        // 基础色
        basic: ['#000000', '#FFFFFF', '#808080', '#C0C0C0'],
        // 红色系
        reds: ['#FF5722', '#F44336', '#E91E63', '#FF1744'],
        // 橙色系
        oranges: ['#FF9800', '#FF6F00', '#FF8A65', '#FFB74D'],
        // 黄色系
        yellows: ['#FFEB3B', '#FFC107', '#FFD54F', '#FFF176'],
        // 绿色系
        greens: ['#4CAF50', '#8BC34A', '#00E676', '#69F0AE'],
        // 蓝色系
        blues: ['#2196F3', '#03A9F4', '#00BCD4', '#40C4FF'],
        // 紫色系
        purples: ['#9C27B0', '#673AB7', '#3F51B5', '#7C4DFF'],
        // 粉色系
        pinks: ['#E91E63', '#F06292', '#FFB6C1', '#FF69B4']
      },
      
      // 颜色选择器弹窗
      colorPickerVisible: false,
      currentColorType: '',
      currentColorIndex: null,
      currentColor: { hex: '#000000' }
    }
  },
  computed: {
    breadcrumbItems() {
      return [
        { path: '/', title: '首页', icon: 'home' },
        { path: '/poster/template', title: '海报模板管理' },
        { path: this.$route.fullPath, title: '海报模板编辑' }
      ]
    }
  },
  async mounted() {
    await this.onEnter(this.$route)
    this.calculateEditorHeight()
    window.addEventListener('resize', this.calculateEditorHeight)
    // 添加键盘事件监听器
    this.addKeyboardListeners()
    await this.loadMaterialCategories()
    await this.loadMaterials()
    await this.loadFontCategories()
  },
  activated() {
    this.onEnter(this.$route)
  },
  beforeRouteUpdate(to, from, next) {
    this.onEnter(to).then(() => next())
  },
  beforeDestroy() {
    // 组件销毁时确保清理所有资源
    this.cleanupResources()
    window.removeEventListener('resize', this.calculateEditorHeight)
    // 移除键盘事件监听器
    this.removeKeyboardListeners()
  },
  deactivated() {
    // keep-alive组件失活时也清理资源
    this.cleanupResources()
    // 移除键盘事件监听器
    this.removeKeyboardListeners()
  },
  methods: {
    // 将可能为对象的颜色（如 Fabric Color）转为字符串，防止传给 AInput 报错
    getColorInputValue(val) {
      if (!val) return ''
      if (typeof val === 'string' || typeof val === 'number') return val
      // Fabric 可能返回 Color 对象或其他结构，尝试常见字段
      if (val && typeof val.toHex === 'function') {
        try { return `#${val.toHex()}` } catch (e) {}
      }
      if (val && typeof val.toRgb === 'function') {
        try { return val.toRgb() } catch (e) {}
      }
      if (val && typeof val.toString === 'function') {
        try { return String(val.toString()) } catch (e) {}
      }
      return ''
    },
    getColorPreviewValue(val) {
      const v = this.getColorInputValue(val)
      return v || '#000000'
    },
    // 获取颜色分组标签
    getGroupLabel(groupName) {
      const labels = {
        basic: '基础色',
        reds: '红色系',
        oranges: '橙色系', 
        yellows: '黄色系',
        greens: '绿色系',
        blues: '蓝色系',
        purples: '紫色系',
        pinks: '粉色系'
      }
      return labels[groupName] || groupName
    },
    
    cleanupResources() {
      // 专门的资源清理方法
      if (this.canvas && this._fabricHandlers) {
        try {
          Object.keys(this._fabricHandlers).forEach(evt => {
            try {
              this.canvas.off(evt, this._fabricHandlers[evt])
            } catch (e) {
              console.warn(`清理事件监听器失败: ${evt}`, e)
            }
          })
        } catch (e) {
          console.warn('清理Canvas事件监听器时出错:', e)
        }
      }
      
      // 清理Canvas实例
      if (this.canvas) {
        try {
          if (typeof this.canvas.dispose === 'function') {
            this.canvas.dispose()
          } else {
            this.canvas.clear()
          }
        } catch (e) {
          console.warn('清理Canvas实例时出错:', e)
        }
      }
      
      // 清理引用
      this.canvas = null
      this.fabric = null
      this._fabricHandlers = null
      this._keyboardHandler = null
    },
    
    // 添加键盘事件监听器
    addKeyboardListeners() {
      // 监听键盘事件
      this._keyboardHandler = this.handleKeyboardEvent.bind(this)
      document.addEventListener('keydown', this._keyboardHandler)
    },
    
    // 移除键盘事件监听器
    removeKeyboardListeners() {
      if (this._keyboardHandler) {
        document.removeEventListener('keydown', this._keyboardHandler)
        this._keyboardHandler = null
      }
    },
    
    // 处理键盘事件
    handleKeyboardEvent(event) {
      // 如果当前有文本编辑，不处理键盘事件
      if (this.canvas && this.canvas.getActiveObject && this.canvas.getActiveObject()) {
        const activeObj = this.canvas.getActiveObject()
        if (activeObj.type === 'i-text' && activeObj.isEditing) {
          return
        }
      }

      // ESC 退出 SVG 隔离编辑并回组
      if (event.key === 'Escape') {
        if (this.exitSvgIsolation && this.canvas && this.canvas.__svgIsolation) {
          event.preventDefault()
          this.exitSvgIsolation()
          return
        }
      }

      // 处理Delete键删除
      if (event.key === 'Delete') {
        event.preventDefault()
        this.deleteSelected()
      }

      // 处理Ctrl+Z撤销
      if (event.ctrlKey && event.key === 'z') {
        event.preventDefault()
        this.undo()
      }

      // 处理Ctrl+Y重做
      if (event.ctrlKey && event.key === 'y') {
        event.preventDefault()
        this.redo()
      }
    },

    resetAll() {
      // 使用专门的清理方法
      this.cleanupResources()
      this.pageTitle = '海报模板编辑'
      this.selectedObject = null
      this.canUndo = false
      this.canRedo = false
      this.variables = []
      this.addVariableVisible = false
      this.variableForm && this.variableForm.resetFields && this.variableForm.resetFields()
      // 清除历史记录
      if (this.canvas && this.canvas.clearHistory) {
        this.canvas.clearHistory()
      }
      this.canvasW = 794
      this.canvasH = 1123
      this.canvasBg = '#ffffff'
      this.templateId = null
      this.templateName = ''
      this.templateDesc = ''
      this.sortOrder = 0
      this.saving = false
      // 重置键盘事件处理器引用
      this._keyboardHandler = null
    },
    async onEnter(route) {
      const q = (route && route.query) || {}
      const enterKey = JSON.stringify(q || {})
      if (this.isInitializing) return
      // 若已初始化且路由查询未变化，则不重复初始化
      if (this.canvas && this.lastEnterKey === enterKey) {
        this.initPageTitle()
        return
      }
      this.isInitializing = true
      try {
        const { id } = q
        // 重置状态，避免旧数据残留
        this.resetAll()
        // 重置后再根据路由设置标题
        this.initPageTitle()
        if (id) this.templateId = id
        await this.initCanvas()
        if (id) {
          await this.loadTemplateById(id)
        }
      } finally {
        this.isInitializing = false
        this.lastEnterKey = enterKey
      }
    },
    initPageTitle() {
      // 根据路由参数设置页面标题
      const { action, mode, title } = this.$route.query
      if (title) {
        this.pageTitle = title
      } else if (action === 'add' || mode === 'add') {
        this.pageTitle = '新增海报模板'
      } else if (action === 'edit' || mode === 'edit') {
        this.pageTitle = '编辑海报模板'
      }
      
      // 设置浏览器标题
      document.title = `${this.pageTitle} - 海报管理系统`
    },
    
    goBack() {
      // 返回模板列表页面
      this.$router.push('/poster/template')
    },
    
    async saveTemplate() {
      if (!this.templateName || !this.templateName.trim()) {
        this.$message.warning('请填写模板名称')
        return
      }
      const templateData = await this.buildTemplateData()
      if (!templateData) {
        this.$message.error('请先设计模板内容')
        return
      }
      const payload = {
        id: this.templateId,
        templateName: this.templateName.trim(),
        templateDesc: this.templateDesc || '',
        sortOrder: this.sortOrder || 0,
        width: this.canvasW,
        height: this.canvasH,
        status: 1,
        templateData: JSON.stringify(templateData)
      }
      this.saving = true
      const isEdit = !!this.templateId
      const req = isEdit
        ? this.$http.put('/biz/posterTemplate/edit', payload)
        : this.$http.post('/biz/posterTemplate/add', payload)
      req.then(res => {
        if (res && res.success) {
          this.$message.success('模板保存成功')
          if (!this.templateId && res.result && res.result.id) {
            this.templateId = res.result.id
          }
          // 返回列表
          this.$router.push('/poster/template')
        } else {
          this.$message.error((res && res.message) || '模板保存失败')
        }
      }).catch(err => {
        this.$message.error('模板保存失败：' + (err && err.message ? err.message : '未知错误'))
      }).finally(() => {
        this.saving = false
      })
    },

    async loadTemplateById(id) {
      try {
        const resp = await this.$http.get('/biz/posterTemplate/queryById', { params: { id } })
        if (resp && resp.success && resp.result) {
          const t = resp.result
          this.templateName = t.templateName || ''
          this.templateDesc = t.templateDesc || ''
          this.sortOrder = t.sortOrder || 0
          const w = t.width || this.canvasW
          const h = t.height || this.canvasH
          this.updateCanvasSize(w, h)
          // 解析模板数据并加载
          try {
            const data = typeof t.templateData === 'string' ? JSON.parse(t.templateData) : (t.templateData || {})
            if (data.canvas && data.canvas.backgroundColor) {
              this.updateCanvasBg(data.canvas.backgroundColor)
            }
            this.loadTemplateData(data)
          } catch (e) {
            // 忽略解析错误，保留空白画布
          }
        } else {
          this.$message.error((resp && resp.message) || '加载模板失败')
        }
      } catch (e) {
        this.$message.error('加载模板失败')
      }
    },
    
    async initCanvas() {
      try {
        // 动态加载 fabric，兼容多种导出格式
        let mod = null
        try {
          mod = await import('fabric')
        } catch (e) {
          try {
            // 兼容某些构建环境
            mod = require('fabric')
          } catch (e2) {}
        }
        this.fabric = (mod && (mod.fabric || mod.default || mod)) || (typeof window !== 'undefined' ? window.fabric : null)
        if (!this.fabric) {
          throw new Error('fabric 未正确加载')
        }

        // 在 fabric 加载完成后，动态导入 fabric-history
        if (typeof window !== 'undefined') {
          window.fabric = this.fabric
        }
        await import('fabric-history')

        this.canvas = new this.fabric.Canvas(this.$refs.fabricCanvas, {
          preserveObjectStacking: true,
          selection: true
        })
        // 允许命中分组内的子元素
        this.canvas.subTargetCheck = true
        this.canvas.perPixelTargetFind = true
        // 初始大小使用内部状态，确保和 UI 同步
        this.canvas.setWidth(this.canvasW || this.width)
        this.canvas.setHeight(this.canvasH || this.height)
        // Fabric v6: 使用属性赋值而非 setBackgroundColor
        this.canvas.backgroundColor = '#ffffff'
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
        // 初始化面板状态
        this.canvasW = this.canvas.getWidth()
        this.canvasH = this.canvas.getHeight()
        this.canvasBg = this.canvas.backgroundColor || '#ffffff'

        // 事件绑定
        this._fabricHandlers = {
          'selection:created': (e) => this.onSelectionCreated(e),
          'selection:updated': (e) => this.onSelectionUpdated(e),
          'selection:cleared': (e) => this.onSelectionCleared(e),
          'object:modified': (e) => this.onObjectModified(e),
          'mouse:down': (e) => this.onMouseDown(e),
          'mouse:dblclick': (e) => this.onDoubleClick(e),
          'text:editing:entered': (e) => this.onTextEditingEntered(e),
          'text:editing:exited': (e) => this.onTextEditingExited(e)
        }
        Object.keys(this._fabricHandlers).forEach(evt => {
          this.canvas.on(evt, this._fabricHandlers[evt])
        })

        // 初始化历史功能
        this.initHistory()

        // 新建模板时保持空白画布
        // 移除默认示例对象，让用户从空白画布开始
      } catch (e) {
        console.error('Fabric 初始化失败:', e)
        this.$message.error('画布初始化失败，请刷新重试')
      }
    },
    
    // 优化后的模板数据加载 - 使用 Fabric.js 原生 API
    async loadTemplateData(data) {
      if (!data || !this.canvas || !this.fabric) return
      
      // 使用原生 loadFromJSON 方法
      try {
        // 设置背景色和画布尺寸
        if (data.canvas) {
          if (data.canvas.backgroundColor) {
            this.canvas.backgroundColor = data.canvas.backgroundColor
            this.canvasBg = data.canvas.backgroundColor
          }
          if (data.canvas.width || data.canvas.height) {
            const w = data.canvas.width || this.canvasW
            const h = data.canvas.height || this.canvasH
            this.updateCanvasSize(w, h)
          }
        }
        
        // 使用 Fabric.js 原生方法加载对象
        if (data.objects && data.objects.length > 0) {
          // 过滤掉无效的对象类型
          const validObjects = data.objects.filter(obj => {
            if (!obj || !obj.type) {
              console.warn('跳过无效对象:', obj)
              return false
            }
            
            // 检查fabric是否支持该对象类型
            const typeName = obj.type === 'i-text' ? 'IText' : 
                           obj.type.charAt(0).toUpperCase() + obj.type.slice(1)
            const fabricClass = this.fabric[typeName]
            if (!fabricClass || typeof fabricClass.fromObject !== 'function') {
              console.warn('跳过不支持的对象类型:', obj.type, 'fabricClass:', typeName)
              return false
            }
            
            return true
          })
          
          if (validObjects.length > 0) {
            this.isLoadingHistory = true
            this.canvas.loadFromJSON({ objects: validObjects }, () => {
              this.canvas.renderAll()
              this.isLoadingHistory = false
              this.saveHistoryState()
            })
          }
        }
        
        // 加载变量
        if (data.variables) {
          this.variables = [...data.variables]
        }
      } catch (err) {
        console.error('加载模板数据失败:', err)
        this.$message.error('加载模板数据失败')
      }
    },
    
    // 优化后的模板数据获取 - 使用 Fabric.js 原生 API
    getTemplateData() {
      if (!this.canvas) return null

      // 使用原生 toJSON 方法，包含自定义属性和文本属性
      const canvasData = this.canvas.toJSON(['variableKey', 'originalSvg', 'svgColors', 'charSpacing'])

      return {
        canvas: {
          width: this.canvas.getWidth(),
          height: this.canvas.getHeight(),
          backgroundColor: this.canvas.backgroundColor || '#ffffff'
        },
        objects: canvasData.objects || [],
        variables: this.variables
      }
    },

    
    // UI控制方法
    setActiveTool(tool) {
      this.activeTool = tool
      if (this.canvas) {
        this.canvas.isDrawingMode = false
        this.canvas.selection = tool === 'select'
      }
    },
    
    // 缩放控制
    zoomIn() {
      this.canvasZoom = Math.min(this.canvasZoom * 1.2, 3)
    },
    
    zoomOut() {
      this.canvasZoom = Math.max(this.canvasZoom / 1.2, 0.1)
    },
    
    resetZoom() {
      this.canvasZoom = 1
    },
    
    // 动态计算编辑器高度
    calculateEditorHeight() {
      this.$nextTick(() => {
        const viewportHeight = window.innerHeight
        // 减去可能的外部容器高度（导航栏、标签页等）
        const availableHeight = viewportHeight - 112 // 根据实际情况调整这个值
        this.editorHeight = Math.max(availableHeight, 600) + 'px'
      })
    },

    addText() {
      this.setActiveTool('select')
      if (!this.canvas || !this.fabric) return
      const text = new this.fabric.IText('双击编辑文本', {
        left: 100,
        top: 100,
        fontSize: 20,
        fill: '#000000',
        fontWeight: 'normal',
        fontStyle: 'normal',
        underline: false,
        linethrough: false,
        charSpacing: 0, // 默认字间距
        opacity: 1, // 默认透明度
        editable: true,  // 确保文本可编辑
        selectable: true,
        evented: true
      })
      this.canvas.add(text)
      this.canvas.setActiveObject(text)
      this.selectedObject = text
      this.canvas.renderAll()
    },
    
    addImage() {
      this.setActiveTool('select')
      const input = this.$refs.imageInput
      if (input) input.click()
    },

    async handleImageFileChange(e) {
      const file = e && e.target && e.target.files && e.target.files[0]
      if (!file || !this.fabric || !this.canvas) return
      try {
        // 直接上传到服务端，拿到 COS 地址，避免 base64 入库
        const form = new FormData()
        form.append('file', file, file.name)
        const uploadRes = await this.$http.post('/file/upload', form)
        const url = (uploadRes && (uploadRes.result || (uploadRes.data && uploadRes.data.result))) || ''
        if (!url) {
          this.$message.error('图片上传失败')
          return
        }

        // 使用远程 URL 加载图片
        const img = await this.imageFromURLCompat(url, { crossOrigin: 'anonymous' })

        // 限制初始大小，最多不超过画布宽度的 80%
        const canvasWidth = (this.canvas && this.canvas.getWidth) ? this.canvas.getWidth() : this.width
        const maxWidth = canvasWidth * 0.8
        if (img && img.width && img.width > maxWidth && img.scaleToWidth) {
          img.scaleToWidth(maxWidth)
        }
        if (img) {
          img.set({ left: 100, top: 100, opacity: 1, variableKey: '' }) // 默认透明度
          this.canvas.add(img)
          this.canvas.setActiveObject(img)
          this.selectedObject = img
        }
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
      } catch (err) {
        console.error('加载图片失败:', err)
        this.$message.error('加载图片失败，请更换图片再试')
      } finally {
        // 重置 input，便于连续选择同一文件
        if (e && e.target) e.target.value = ''
      }
    },

    updateCanvasSize(w, h) {
      const newW = Number(w) || this.canvasW
      const newH = Number(h) || this.canvasH
      this.canvasW = newW
      this.canvasH = newH
      if (this.canvas) {
        this.canvas.setWidth(newW)
        this.canvas.setHeight(newH)
        if (typeof this.canvas.calcOffset === 'function') {
          this.canvas.calcOffset()
        }
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
      }
    },

    updateCanvasBg(color) {
      this.canvasBg = color || '#ffffff'
      if (this.canvas) {
        this.canvas.backgroundColor = this.canvasBg
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
      }
    },

    
    addRect() {
      this.setActiveTool('select')
      if (!this.canvas || !this.fabric) return
      const rect = new this.fabric.Rect({
        left: 120,
        top: 120,
        width: 140,
        height: 80,
        fill: '#ff7875',
        opacity: 1,  // 初始透明度
        rx: 0,  // 初始圆角半径
        ry: 0   // 初始圆角半径
      })
      this.canvas.add(rect)
      this.canvas.setActiveObject(rect)
      this.selectedObject = rect
      this.canvas.renderAll()
    },
    
    addCircle() {
      this.setActiveTool('select')
      if (!this.canvas || !this.fabric) return
      const circle = new this.fabric.Circle({
        left: 160,
        top: 160,
        radius: 40,
        fill: '#36cfc9',
        opacity: 1  // 初始透明度
      })
      this.canvas.add(circle)
      this.canvas.setActiveObject(circle)
      this.selectedObject = circle
      this.canvas.renderAll()
    },
    
    // 显示自定义SVG弹窗
    showCustomSvgModal() {
      this.customSvgCode = ''
      this.customSvgVisible = true
    },
    
    // 添加自定义SVG到画布
    addCustomSvg() {
      if (!this.customSvgCode.trim()) {
        this.$message.error('请输入SVG代码')
        return
      }
      
      this.setActiveTool('select')
      if (!this.canvas || !this.fabric) return
      
      try {
        // 检测SVG中的颜色信息
        const detectedColors = this.detectSvgColors(this.customSvgCode)
        const colorPlaceholders = this.parseSvgColorPlaceholders(this.customSvgCode)
        
        // 合并检测到的颜色和占位符
        let svgColors = []
        
        if (detectedColors.length > 0) {
          // 使用检测到的颜色
          svgColors = detectedColors
        } else if (colorPlaceholders.length > 0) {
          // 使用占位符
          svgColors = colorPlaceholders
        }
        
        // 处理SVG内容，替换颜色占位符
        let svgContent = this.customSvgCode
        if (svgContent.includes('{{color}}')) {
          svgContent = svgContent.replace(/\{\{color\}\}/g, '#000000')
        }
        
        // 加载SVG到画布
        this.fabric.loadSVGFromString(svgContent, (objects, options) => {
          const svgObject = this.fabric.util.groupSVGElements(objects, options)
          svgObject.set({
            left: 0,
            top: 0,
            scaleX: 1,
            scaleY: 1,
            opacity: 1,
            originalSvg: this.customSvgCode, // 保存原始SVG用于颜色编辑
            svgColors: svgColors, // 保存颜色信息
            fill: '#000000' // 保持向后兼容
          })
          svgObject.subTargetCheck = true
          
          this.canvas.add(svgObject)
          this.canvas.setActiveObject(svgObject)
          this.selectedObject = svgObject
          this.canvas.renderAll()
          
          // 关闭弹窗
          this.customSvgVisible = false
          this.$message.success('SVG添加成功')
        })
      } catch (err) {
        console.error('添加自定义SVG失败:', err)
        this.$message.error('SVG格式不正确，请检查代码')
      }
    },
    
    deleteSelected() {
      if (!this.canvas) return
      const activeObjects = this.canvas.getActiveObjects()
      if (activeObjects && activeObjects.length > 0) {
        activeObjects.forEach(obj => this.canvas.remove(obj))
        this.canvas.discardActiveObject()
        this.selectedObject = null
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
      }
    },
    
    // 层级控制
    bringForward() {
      if (!this.canvas) return
      const activeList = (this.canvas.getActiveObjects && this.canvas.getActiveObjects()) || []
      if (activeList.length > 1) {
        try {
          activeList.forEach(obj => {
            if (typeof obj.bringForward === 'function') obj.bringForward()
            else if (typeof this.canvas.bringForward === 'function') this.canvas.bringForward(obj)
          })
        } finally {
          ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
          this.saveState()
          this.$emit('change', this.getTemplateData())
        }
        return
      }
      const target = (this.canvas.getActiveObject && this.canvas.getActiveObject()) || this.selectedObject
      if (!target) return
      try {
        if (typeof target.bringForward === 'function') {
          target.bringForward()
        } else if (typeof this.canvas.bringForward === 'function') {
          this.canvas.bringForward(target)
        }
      } finally {
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
        this.saveState()
        this.$emit('change', this.getTemplateData())
      }
    },
    
    sendBackwards() {
      if (!this.canvas) return
      const activeList = (this.canvas.getActiveObjects && this.canvas.getActiveObjects()) || []
      if (activeList.length > 1) {
        try {
          for (let i = activeList.length - 1; i >= 0; i--) {
            const obj = activeList[i]
            if (typeof obj.sendBackwards === 'function') obj.sendBackwards()
            else if (typeof this.canvas.sendBackwards === 'function') this.canvas.sendBackwards(obj)
          }
        } finally {
          ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
          this.saveState()
          this.$emit('change', this.getTemplateData())
        }
        return
      }
      const target = (this.canvas.getActiveObject && this.canvas.getActiveObject()) || this.selectedObject
      if (!target) return
      try {
        if (typeof target.sendBackwards === 'function') {
          target.sendBackwards()
        } else if (typeof this.canvas.sendBackwards === 'function') {
          this.canvas.sendBackwards(target)
        }
      } finally {
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
        this.saveState()
        this.$emit('change', this.getTemplateData())
      }
    },
    
    bringToFront() {
      if (!this.canvas) return
      const activeList = (this.canvas.getActiveObjects && this.canvas.getActiveObjects()) || []
      if (activeList.length > 1) {
        try {
          activeList.forEach(obj => {
            if (typeof obj.bringToFront === 'function') obj.bringToFront()
            else if (typeof this.canvas.bringToFront === 'function') this.canvas.bringToFront(obj)
          })
        } finally {
          ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
          this.saveState()
          this.$emit('change', this.getTemplateData())
        }
        return
      }
      const target = (this.canvas.getActiveObject && this.canvas.getActiveObject()) || this.selectedObject
      if (!target) return
      try {
        if (typeof target.bringToFront === 'function') {
          target.bringToFront()
        } else if (typeof this.canvas.bringToFront === 'function') {
          this.canvas.bringToFront(target)
        }
      } finally {
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
        this.saveState()
        this.$emit('change', this.getTemplateData())
      }
    },
    
    sendToBack() {
      if (!this.canvas) return
      const activeList = (this.canvas.getActiveObjects && this.canvas.getActiveObjects()) || []
      if (activeList.length > 1) {
        try {
          for (let i = activeList.length - 1; i >= 0; i--) {
            const obj = activeList[i]
            if (typeof obj.sendToBack === 'function') obj.sendToBack()
            else if (typeof this.canvas.sendToBack === 'function') this.canvas.sendToBack(obj)
          }
        } finally {
          ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
          this.saveState()
          this.$emit('change', this.getTemplateData())
        }
        return
      }
      const target = (this.canvas.getActiveObject && this.canvas.getActiveObject()) || this.selectedObject
      if (!target) return
      try {
        if (typeof target.sendToBack === 'function') {
          target.sendToBack()
        } else if (typeof this.canvas.sendToBack === 'function') {
          this.canvas.sendToBack(target)
        }
      } finally {
        ;(this.canvas.requestRenderAll || this.canvas.renderAll).call(this.canvas)
        this.saveState()
        this.$emit('change', this.getTemplateData())
      }
    },
    
    clearCanvas() {
      if (this.canvas) {
        this.canvas.clear()
        this.selectedObject = null
      }
    },


    
    // 撤销操作
    undo() {
      if (this.canvas && typeof this.canvas.canUndo === 'function' && this.canvas.canUndo()) {
        this.canvas.undo()
        this.updateHistoryButtons()
        this.$message.success('已撤销')
      } else if (!this.canvas || typeof this.canvas.undo !== 'function') {
        this.$message.warning('撤销功能尚未初始化')
      }
    },
    
    // 重做操作
    redo() {
      if (this.canvas && typeof this.canvas.canRedo === 'function' && this.canvas.canRedo()) {
        this.canvas.redo()
        this.updateHistoryButtons()
        this.$message.success('已重做')
      } else if (!this.canvas || typeof this.canvas.redo !== 'function') {
        this.$message.warning('重做功能尚未初始化')
      }
    },

    // 保存历史状态（fabric-history 自动处理）
    saveHistoryState() {
      // fabric-history 会自动监听对象变化并保存状态
      this.updateHistoryButtons()
    },

    // 更新历史按钮状态
    updateHistoryButtons() {
      if (this.canvas && typeof this.canvas.canUndo === 'function') {
        this.canUndo = this.canvas.canUndo()
        this.canRedo = this.canvas.canRedo()
      } else {
        this.canUndo = false
        this.canRedo = false
      }
    },

    // 初始化历史功能
    initHistory() {
      if (!this.canvas) return
      
      try {
        // 检查 fabric-history 是否已经扩展了 Canvas 原型
        if (typeof this.canvas.undo !== 'function') {
          console.warn('fabric-history 尚未加载，撤销重做功能不可用')
          return
        }
        
        // 监听历史状态变化事件
        this.canvas.on('history:append', () => {
          this.updateHistoryButtons()
        })
        
        this.canvas.on('history:undo', () => {
          this.updateHistoryButtons()
        })
        
        this.canvas.on('history:redo', () => {
          this.updateHistoryButtons()
        })
        
        this.canvas.on('history:clear', () => {
          this.updateHistoryButtons()
        })
        
        // 延迟初始化，确保画布完全就绪
        this.$nextTick(() => {
          // 初始保存一个状态
          if (this.canvas.onHistory) {
            this.canvas.onHistory()
          }
          this.updateHistoryButtons()
        })
        
        console.log('fabric-history 初始化成功')
      } catch (err) {
        console.error('初始化fabric-history失败:', err)
      }
    },
    
    onSelectionCreated(e) {
      this.selectedObject = (this.canvas && this.canvas.getActiveObject && this.canvas.getActiveObject()) || (e && e.selected && e.selected[0]) || null
    },
    
    onSelectionUpdated(e) {
      this.selectedObject = (this.canvas && this.canvas.getActiveObject && this.canvas.getActiveObject()) || (e && e.selected && e.selected[0]) || null
    },
    
    onSelectionCleared() {
      this.selectedObject = null
    },

    onMouseDown(e) {
      // 保障某些情况下 selection 事件未触发时仍能取到选中对象
      // 若命中分组内的子元素，优先设置该子元素为选中对象
      if (e && e.subTargets && e.subTargets.length > 0) {
        const sub = e.subTargets[e.subTargets.length - 1] || e.subTargets[0]
        this.selectedObject = sub || null
        try {
          if (this.canvas && this.canvas.setActiveObject && sub) {
            this.canvas.setActiveObject(sub)
          }
        } catch (err) {}
        return
      }
      if (this.canvas && this.canvas.getActiveObject) {
        this.selectedObject = this.canvas.getActiveObject() || null
      }
    },

    onDoubleClick(e) {
      // 双击事件处理 - Fabric.js 5.3.0 稳定版本
      const target = e && e.target
      const isTextLike = target && (target.type === 'text' || target.type === 'i-text' || target.type === 'textbox')
      if (isTextLike) {
        try {
          // 确保文本对象被选中
          this.canvas.setActiveObject(target)
          this.selectedObject = target
          
          // Fabric.js 5.3.0 中进入文本编辑模式
          if (typeof target.enterEditing === 'function') {
            target.enterEditing()
            if (typeof target.selectAll === 'function') target.selectAll()
          } else {
            throw new Error('enterEditing 不可用')
          }
          this.canvas.renderAll()
          return
        } catch (error) {
          console.warn('进入文本编辑模式失败:', error)
          // 降级处理：选中文本对象并自动焦点到属性面板的文本输入框
          this.canvas.setActiveObject(target)
          this.selectedObject = target
          this.$message.info('请在右侧属性面板中编辑文本内容')
          
          // 尝试自动聚焦到文本输入框
          this.$nextTick(() => {
            const textInput = this.$el.querySelector('textarea[data-property="text"]') || 
                            this.$el.querySelector('.property-panel textarea')
            if (textInput) {
              textInput.focus()
              textInput.select()
            }
          })
          return
        }
      }

      // 双击分组进入隔离编辑（将分组合为可编辑的多选）
      if (target && target.type === 'group' && target.originalSvg && typeof target.toActiveSelection === 'function') {
        this.enterSvgIsolation(target)
        return
      }

      // 双击多选回组（恢复为单个分组对象）
      if (target && target.type === 'activeSelection' && target.parentSvgMeta && typeof target.toGroup === 'function') {
        try {
          target.toGroup()
          const newGroup = this.canvas.getActiveObject()
          if (newGroup) {
            newGroup.originalSvg = target.parentSvgMeta.originalSvg
            newGroup.svgColors = target.parentSvgMeta.svgColors
            newGroup.materialId = target.parentSvgMeta.materialId
            newGroup.subTargetCheck = true
          }
          this.canvas.renderAll()
          return
        } catch (err) {}
      }

      // 双击空白处：若处于隔离编辑，执行回组
      if (!target && this.canvas && this.canvas.__svgIsolation) {
        this.exitSvgIsolation()
        return
      }
    },

    // 进入 SVG 隔离编辑：把组转为多选，并为子元素打上分组标记
    enterSvgIsolation(group) {
      if (!this.canvas || !group || typeof group.toActiveSelection !== 'function') return
      try {
        const key = `svg_iso_${Date.now()}_${Math.random().toString(36).slice(2)}`
        group.toActiveSelection(this.canvas)
        const activeSel = this.canvas.getActiveObject()
        if (activeSel) {
          activeSel.parentSvgMeta = {
            originalSvg: group.originalSvg,
            svgColors: group.svgColors,
            materialId: group.materialId
          }
          // 标记所有子元素属于同一临时组
          if (typeof activeSel.getObjects === 'function') {
            const children = activeSel.getObjects()
            children.forEach(child => { child.__svgGroupKey = key })
          }
          this.canvas.__svgIsolation = { key, meta: activeSel.parentSvgMeta }
        }
        this.canvas.renderAll()
      } catch (err) {}
    },

    // 退出 SVG 隔离编辑：将标记的子元素重新回组成一个组
    exitSvgIsolation() {
      if (!this.canvas || !this.fabric || !this.canvas.__svgIsolation) return
      const { key, meta } = this.canvas.__svgIsolation
      try {
        // 收集所有带有标记的对象
        const all = this.canvas.getObjects ? this.canvas.getObjects() : []
        const parts = all.filter(o => o && o.__svgGroupKey === key)
        if (!parts.length) {
          delete this.canvas.__svgIsolation
          return
        }
        // 先清除当前选择
        if (this.canvas.discardActiveObject) this.canvas.discardActiveObject()
        // 创建一个 ActiveSelection 并回组
        const selection = new this.fabric.ActiveSelection(parts, { canvas: this.canvas })
        this.canvas.setActiveObject(selection)
        const group = selection.toGroup()
        if (group) {
          group.originalSvg = meta.originalSvg
          group.svgColors = meta.svgColors
          group.materialId = meta.materialId
          group.subTargetCheck = true
        }
        // 清理子元素标记
        parts.forEach(o => { try { delete o.__svgGroupKey } catch (e) {} })
        delete this.canvas.__svgIsolation
        this.canvas.renderAll()
      } catch (err) {
        // 兜底：即使失败也尝试刷新
        try { delete this.canvas.__svgIsolation } catch (e) {}
        this.canvas.renderAll()
      }
    },

    async imageFromURLCompat(url, options = {}) {
      // 兼容 Fabric v4/v5 (回调) 和 v6 (Promise)
      return new Promise((resolve, reject) => {
        try {
          // 检查是否为WebP格式，如果是则使用代理加载
          if (url.toLowerCase().includes('.webp')) {
            this.loadImageWithProxy(url).then(img => {
              // 将HTML Image转换为Fabric Image
              const fabricImg = new this.fabric.Image(img, options)
              resolve(fabricImg)
            }).catch(err => {
              reject(err)
            })
            return
          }

          // 对于其他格式，使用原有方法
          this.fabric.Image.fromURL(url, (img) => {
            if (img && img.getElement && img.getElement()) {
              resolve(img)
            } else {
              reject(new Error('图片加载失败'))
            }
          }, options)
        } catch (e) {
          reject(e)
        }
      })
    },

    onTextEditingEntered(e) {
      // 文本进入编辑模式
      console.log('文本进入编辑模式')
    },

    onTextEditingExited(e) {
      // 文本退出编辑模式
      console.log('文本退出编辑模式')
      if (e.target) {
        // 更新选中对象信息，确保属性面板显示最新内容
        this.selectedObject = e.target
        // 触发变更事件
        this.$emit('change', this.getTemplateData())
      }
    },
    
    onObjectModified() {
      this.saveState()
      this.$emit('change', this.getTemplateData())
    },
    
    async updateProperty(key, value) {
      if (this.selectedObject && this.canvas) {

        // 处理占位符图片的URL更新
        if (key === 'src' && this.selectedObject.isPlaceholder && this.selectedObject.originalType === 'image') {
          this.replaceImagePlaceholder(value)
          return
        }

        // 拦截图片 src 的 dataURL，转存到 COS 后再替换
        if (
          key === 'src' &&
          this.selectedObject.type === 'image' &&
          typeof value === 'string' &&
          value.indexOf('data:') === 0
        ) {
          this.uploadDataUrlAndSetImage(value)
          return
        }

        // 处理字体切换，需要先加载自定义字体
        if (key === 'fontFamily' && typeof value === 'string' && value.startsWith('custom-font-')) {
          try {
            // 从字体族名称中提取字体ID
            const fontId = value.replace('custom-font-', '')
            const fontMaterial = this.fontMaterials.find(f => f.id == fontId)

            if (fontMaterial && fontMaterial.fileUrl) {
              // 先加载字体
              await this.loadCustomFont(fontMaterial)
              console.log('字体加载成功:', fontMaterial.name)
            }
          } catch (error) {
            console.error('加载字体失败:', error)
            this.$message.error('字体加载失败，请重试')
            return
          }
        }

        // 处理角度值，确保在合理范围内
        if (key === 'angle') {
          value = Number(value) || 0
          // 将角度规范化到 -180 到 180 度之间，使用数学运算替代while循环
          value = ((value + 180) % 360) - 180
          if (value <= -180) value = 180
        }

        this.selectedObject.set(key, value)
        this.canvas.renderAll()
        this.$emit('change', this.getTemplateData())

        // 强制更新Vue的响应式数据，确保UI同步
        this.$forceUpdate()
      }
    },
    
    updateSize(dimension, value) {
      if (this.selectedObject && this.canvas && value > 0) {
        if (dimension === 'width') {
          const originalWidth = this.selectedObject.width || 1
          if (originalWidth > 0) {
            const scale = value / originalWidth
            this.selectedObject.set('scaleX', scale)
          }
        } else if (dimension === 'height') {
          const originalHeight = this.selectedObject.height || 1
          if (originalHeight > 0) {
            const scale = value / originalHeight
            this.selectedObject.set('scaleY', scale)
          }
        }
        this.canvas.renderAll()
        this.$emit('change', this.getTemplateData())
      }
    },
    
    saveState() {
      // 保存历史状态用于撤销/重做
      console.log('保存状态')
    },

    // 将 dataURL 转为 Blob
    dataURLToBlob(dataURL) {
      try {
        const parts = String(dataURL).split(',')
        const mimeMatch = parts[0].match(/:(.*?);/)
        const mime = (mimeMatch && mimeMatch[1]) || 'image/png'
        const binaryStr = atob(parts[1] || '')
        const len = binaryStr.length
        const u8arr = new Uint8Array(len)
        for (let i = 0; i < len; i++) {
          u8arr[i] = binaryStr.charCodeAt(i)
        }
        return new Blob([u8arr], { type: mime })
      } catch (e) {
        return new Blob([], { type: 'application/octet-stream' })
      }
    },

    async uploadBlobAndGetUrl(blob, filename = 'image.png') {
      const form = new FormData()
      form.append('file', blob, filename)
      const res = await this.$http.post('/file/upload', form)
      const url = (res && (res.result || (res.data && res.data.result))) || ''
      if (!url) throw new Error('上传失败')
      return url
    },

    async uploadDataUrlAndSetImage(dataUrl) {
      try {
        const blob = this.dataURLToBlob(dataUrl)
        const url = await this.uploadBlobAndGetUrl(blob, `image_${Date.now()}.png`)
        const imgObj = this.selectedObject
        if (!imgObj) return
        if (typeof imgObj.setSrc === 'function') {
          imgObj.setSrc(url, () => {
            imgObj.set('dirty', true)
            this.canvas.renderAll()
            this.$emit('change', this.getTemplateData())
          }, { crossOrigin: 'anonymous' })
          imgObj.variableKey = imgObj.variableKey || ''
        } else {
          const newImg = await this.imageFromURLCompat(url, { crossOrigin: 'anonymous' })
          if (newImg) {
            newImg.set({
              left: imgObj.left || 0,
              top: imgObj.top || 0,
              scaleX: imgObj.scaleX || 1,
              scaleY: imgObj.scaleY || 1,
              variableKey: imgObj.variableKey || ''
            })
            this.canvas.remove(imgObj)
            this.canvas.add(newImg)
            this.canvas.setActiveObject(newImg)
            this.selectedObject = newImg
            this.canvas.renderAll()
            this.$emit('change', this.getTemplateData())
          }
        }
      } catch (e) {
        this.$message.error('图片上传失败，请更换图片再试')
      }
    },

    async buildTemplateData() {
      // 导出模板数据前，确保所有图片都是远程 URL（非 base64）
      const data = this.getTemplateData()
      if (!data || !Array.isArray(data.objects) || data.objects.length === 0) return data
      const newObjects = []
      for (const obj of data.objects) {
        if (obj.type === 'image' && obj.src && typeof obj.src === 'string' && obj.src.indexOf('data:') === 0) {
          try {
            const blob = this.dataURLToBlob(obj.src)
            const url = await this.uploadBlobAndGetUrl(blob, `image_${Date.now()}.png`)
            newObjects.push({ ...obj, src: url })
          } catch (e) {
            // 出错时移除 src，避免把 base64 写入数据库
            newObjects.push({ ...obj, src: '' })
          }
        } else {
          newObjects.push(obj)
        }
      }
      return { ...data, objects: newObjects }
    },
    
    showAddVariableModal() {
      this.addVariableVisible = true
      this.variableForm.resetFields()
    },
    
    handleAddVariable() {
      this.variableForm.validateFields((err, values) => {
        if (!err) {
          // 检查变量标识是否已存在
          if (this.variables.find(v => v.key === values.key)) {
            this.$message.error('变量标识已存在')
            return
          }
          
          this.variables.push(values)
          this.addVariableVisible = false
          this.$emit('change', this.getTemplateData())
        }
      })
    },
    
    removeVariable(key) {
      const index = this.variables.findIndex(v => v.key === key)
      if (index > -1) {
        this.variables.splice(index, 1)
        this.$emit('change', this.getTemplateData())
      }
    },
    
    updateRectRadius(value) {
      if (this.selectedObject && this.selectedObject.type === 'rect' && this.canvas) {
        const radius = Number(value) || 0
        this.selectedObject.set({
          rx: radius,
          ry: radius  // 保持 rx 和 ry 相同，创建统一的圆角
        })
        this.canvas.renderAll()
        this.$emit('change', this.getTemplateData())
      }
    },
    
    async replaceImagePlaceholder(newSrc) {
      if (!this.selectedObject || !newSrc) return
      
      try {
        
        // 处理普通占位符图片
        if (!this.selectedObject.isPlaceholder) {
          return
        }
        
        // 获取占位符的位置和属性
        const placeholder = this.selectedObject
        const left = placeholder.left || 0
        const top = placeholder.top || 0
        const angle = placeholder.angle || 0
        const opacity = placeholder.opacity || 1
        const variableKey = placeholder.variableKey || ''
        
        // 获取占位符的尺寸（从Group中获取）
        let width = 100
        let height = 100
        if (placeholder.type === 'group' && placeholder.getObjects && placeholder.getObjects().length > 0) {
          const rect = placeholder.getObjects()[0]
          width = rect.width || 100
          height = rect.height || 100
        }
        
        // 加载新图片
        const newImg = await this.imageFromURLCompat(newSrc, { crossOrigin: 'anonymous' })
        
        if (newImg) {
          // 设置图片属性
          newImg.set({
            left: left,
            top: top,
            angle: angle,
            opacity: opacity,
            variableKey: variableKey,
            selectable: true,
            evented: true
          })
          
          // 计算缩放比例以保持原占位符的尺寸
          if (newImg.width && newImg.height) {
            const scaleX = width / newImg.width
            const scaleY = height / newImg.height
            newImg.set({ scaleX, scaleY })
          }
          
          // 移除占位符并添加新图片
          this.canvas.remove(placeholder)
          this.canvas.add(newImg)
          this.canvas.setActiveObject(newImg)
          this.selectedObject = newImg
          
          this.canvas.renderAll()
          this.$emit('change', this.getTemplateData())
          this.$message.success('图片加载成功')
        }
      } catch (err) {
        console.error('替换占位符图片失败:', err)
        this.$message.error('图片加载失败，请检查URL是否正确')
        
        // 更新占位符的原始URL，以便用户再次尝试
        if (this.selectedObject) {
          this.selectedObject.originalSrc = newSrc
        }
      }
    },

    // 素材相关方法
    async loadMaterialCategories() {
      try {
        const response = await this.$http.get('/biz/posterMaterial/categories')
        if (response && response.success) {
          this.materialCategories = response.result || []
          // 分类按类型分组
          this.imageCategories = this.materialCategories.filter(cat => 
            cat.parentId === 1 || (cat.parentId === 0 && cat.name === '图片素材')
          )
          this.svgCategories = this.materialCategories.filter(cat => 
            cat.parentId === 2 || (cat.parentId === 0 && cat.name === 'SVG图标')
          )
        }
      } catch (error) {
        console.error('加载分类失败:', error)
      }
    },

    async loadMaterials() {
      await Promise.all([
        this.loadImageMaterials(),
        this.loadSvgMaterials(),
        this.loadFontMaterials(),
      ])
    },

    async loadImageMaterials() {
      try {
        const params = {
          type: 'image',
          categoryId: this.selectedImageCategory || undefined,
          pageSize: 50
        }
        const response = await this.$http.get('/biz/posterMaterial/list', { params })
        if (response && response.success) {
          this.imageMaterials = (response.result && response.result.records) || []
        }
      } catch (error) {
        console.error('加载图片素材失败:', error)
        this.imageMaterials = []
      }
    },

    async loadSvgMaterials() {
      try {
        const params = {
          type: 'svg',
          categoryId: this.selectedSvgCategory || undefined,
          pageSize: 50
        }
        const response = await this.$http.get('/biz/posterMaterial/list', { params })
        if (response && response.success) {
          this.svgMaterials = (response.result && response.result.records) || []
        }
      } catch (error) {
        console.error('加载SVG素材失败:', error)
        this.svgMaterials = []
      }
    },

    async loadFontMaterials() {
      try {
        const params = {
          type: 'font',
          categoryId: this.selectedFontCategory || undefined,
          pageSize: 50
        }
        const response = await this.$http.get('/biz/posterMaterial/list', { params })
        if (response && response.success) {
          this.fontMaterials = (response.result && response.result.records) || []
        }
      } catch (error) {
        console.error('加载字体素材失败:', error)
        this.fontMaterials = []
      }
    },

    async loadFontCategories() {
      try {
        const response = await this.$http.get('/biz/posterMaterial/categories')
        if (response && response.success) {
          const categories = response.result || []
          // 筛选字体分类（父分类ID为4的分类，根据之前的SQL初始化数据）
          this.fontCategories = categories.filter(cat => cat.parentId === 4)
        }
      } catch (error) {
        console.error('加载字体分类失败:', error)
        this.fontCategories = []
      }
    },


    async addImageMaterial(material) {
      this.setActiveTool('select')
      if (!this.canvas || !this.fabric) return
      
      try {
        // 增加使用次数
        await this.$http.post('/biz/posterMaterial/increaseDownload', null, {
          params: { id: material.id }
        })
        
        // 添加图片到画布
        const img = await this.imageFromURLCompat(material.fileUrl, { crossOrigin: 'anonymous' })
        
        // 限制初始大小
        const canvasWidth = this.canvas.getWidth()
        const maxWidth = canvasWidth * 0.8
        if (img && img.width && img.width > maxWidth && img.scaleToWidth) {
          img.scaleToWidth(maxWidth)
        }
        
        if (img) {
          img.set({ 
            left: 100, 
            top: 100, 
            opacity: 1,
            variableKey: '',
            materialId: material.id // 记录素材ID
          })
          this.canvas.add(img)
          this.canvas.setActiveObject(img)
          this.selectedObject = img
        }
        this.canvas.renderAll()
      } catch (err) {
        console.error('添加图片素材失败:', err)
        this.$message.error('添加图片素材失败')
      }
    },

    async addSvgMaterial(material) {
      this.setActiveTool('select')
      if (!this.canvas || !this.fabric) return
      
      try {
        // 增加使用次数
        await this.$http.post('/biz/posterMaterial/increaseDownload', null, {
          params: { id: material.id }
        })
        
        // 检测SVG中的颜色信息
        const detectedColors = this.detectSvgColors(material.svgContent)
        const colorPlaceholders = this.parseSvgColorPlaceholders(material.svgContent)
        
        // 合并检测到的颜色和占位符
        let svgColors = []
        
        if (detectedColors.length > 0) {
          // 使用检测到的颜色
          svgColors = detectedColors
        } else if (colorPlaceholders.length > 0) {
          // 使用占位符
          svgColors = colorPlaceholders
        }
        
        // 处理SVG内容，替换颜色占位符
        let svgContent = material.svgContent
        if (svgContent.includes('{{color}}')) {
          svgContent = svgContent.replace(/\{\{color\}\}/g, '#000000')
        }
        
        // 加载SVG到画布
        this.fabric.loadSVGFromString(svgContent, (objects, options) => {
          const svgObject = this.fabric.util.groupSVGElements(objects, options)
          svgObject.set({
            left: 0,
            top: 0,
            scaleX: 1,
            scaleY: 1,
            opacity: 1,
            materialId: material.id,
            originalSvg: material.svgContent, // 保存原始SVG用于颜色编辑
            svgColors: svgColors, // 保存颜色信息
            fill: '#000000' // 保持向后兼容
          })
          svgObject.subTargetCheck = true
          
          this.canvas.add(svgObject)
          this.canvas.setActiveObject(svgObject)
          this.selectedObject = svgObject
          this.canvas.renderAll()
        })
      } catch (err) {
        console.error('添加SVG素材失败:', err)
        this.$message.error('添加SVG素材失败')
      }
    },



    getSvgPreview(svgContent) {
      if (!svgContent) return ''
      // 处理SVG预览，替换颜色占位符并设置尺寸
      let preview = svgContent
      
      // 替换颜色占位符
      if (preview.includes('{{color}}')) {
        preview = preview.replace(/\{\{color\}\}/g, '#666666')
      }
      
      // 替换其他颜色占位符
      const placeholderRegex = /\{\{(\w+)\}\}/g
      preview = preview.replace(placeholderRegex, (match, placeholder) => {
        const colorMap = {
          'primary': '#666666',
          'secondary': '#999999',
          'accent': '#ff6b6b',
          'background': '#f0f0f0',
          'text': '#333333',
          'border': '#d9d9d9'
        }
        return colorMap[placeholder] || '#666666'
      })
      
      // 确保SVG有合适的尺寸
      if (!preview.includes('width=') && !preview.includes('height=')) {
        preview = preview.replace('<svg', '<svg width="24" height="24"')
      }
      return preview
    },

    // 检测SVG中的颜色信息
    detectSvgColors(svgContent) {
      if (!svgContent) return []
      
      const colors = []
      const colorRegex = /(?:fill|stroke)="([^"]*)"|(?:fill|stroke):\s*([^;]+)/g
      let match
      
      while ((match = colorRegex.exec(svgContent)) !== null) {
        const color = match[1] || match[2]
        if (color && color !== 'none' && color !== 'transparent' && !color.startsWith('url(')) {
          // 检查是否已经添加过这个颜色
          if (!colors.find(c => c.originalColor === color)) {
            colors.push({
              originalColor: color,
              currentColor: color,
              name: this.getColorName(color)
            })
          }
        }
      }
      
      return colors
    },

    // 获取颜色名称
    getColorName(color) {
      const colorNames = {
        '#000000': '黑色',
        '#ffffff': '白色',
        '#ff0000': '红色',
        '#00ff00': '绿色',
        '#0000ff': '蓝色',
        '#ffff00': '黄色',
        '#ff00ff': '洋红',
        '#00ffff': '青色',
        '#ffa500': '橙色',
        '#800080': '紫色',
        '#a52a2a': '棕色',
        '#808080': '灰色'
      }
      return colorNames[color.toLowerCase()] || '自定义颜色'
    },

    // 解析SVG中的颜色占位符
    parseSvgColorPlaceholders(svgContent) {
      if (!svgContent) return []
      
      const placeholders = []
      const placeholderRegex = /\{\{(\w+)\}\}/g
      let match
      
      while ((match = placeholderRegex.exec(svgContent)) !== null) {
        const placeholder = match[1]
        if (!placeholders.find(p => p.placeholder === placeholder)) {
          placeholders.push({
            placeholder: `{{${placeholder}}}`,
            name: this.getPlaceholderName(placeholder),
            currentColor: '#000000'
          })
        }
      }
      
      return placeholders
    },

    // 获取占位符名称
    getPlaceholderName(placeholder) {
      const nameMap = {
        'color': '主色',
        'primary': '主色',
        'secondary': '次色',
        'accent': '强调色',
        'background': '背景色',
        'text': '文字色',
        'border': '边框色'
      }
      return nameMap[placeholder] || placeholder
    },


    // SVG颜色更新方法（单颜色支持，向后兼容）
    updateSvgColor(color) {
      if (!this.selectedObject || !this.selectedObject.originalSvg) return
      
      try {
        // 更新SVG颜色
        const newSvgContent = this.selectedObject.originalSvg.replace(/\{\{color\}\}/g, color)
        
        // 重新加载SVG
        this.fabric.loadSVGFromString(newSvgContent, (objects, options) => {
          const newSvgObject = this.fabric.util.groupSVGElements(objects, options)
          
          // 保持当前的位置、尺寸等属性
          newSvgObject.set({
            left: this.selectedObject.left,
            top: this.selectedObject.top,
            scaleX: this.selectedObject.scaleX,
            scaleY: this.selectedObject.scaleY,
            angle: this.selectedObject.angle,
            opacity: this.selectedObject.opacity,
            materialId: this.selectedObject.materialId,
            originalSvg: this.selectedObject.originalSvg,
            variableKey: this.selectedObject.variableKey,
            fill: color
          })
          
          // 移除旧的SVG，添加新的SVG
          this.canvas.remove(this.selectedObject)
          this.canvas.add(newSvgObject)
          this.canvas.setActiveObject(newSvgObject)
          this.selectedObject = newSvgObject
          
          this.canvas.renderAll()
          this.$emit('change', this.getTemplateData())
        })
      } catch (err) {
        console.error('更新SVG颜色失败:', err)
        this.$message.error('更新颜色失败')
      }
    },

    // 多颜色SVG更新方法
    updateSvgMultiColor(colorIndex, newColor) {
      if (!this.selectedObject || !this.selectedObject.originalSvg) return
      
      try {
        // 更新颜色数组中的颜色
        if (this.selectedObject.svgColors && this.selectedObject.svgColors[colorIndex]) {
          this.selectedObject.svgColors[colorIndex].currentColor = newColor
        }
        
        // 重新生成SVG内容
        let newSvgContent = this.selectedObject.originalSvg
        
        // 替换所有颜色
        if (this.selectedObject.svgColors) {
          this.selectedObject.svgColors.forEach((colorInfo, index) => {
            if (colorInfo.originalColor) {
              // 替换原始颜色
              newSvgContent = newSvgContent.replace(
                new RegExp(`(fill|stroke)="${colorInfo.originalColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"`, 'g'),
                `$1="${colorInfo.currentColor}"`
              )
              newSvgContent = newSvgContent.replace(
                new RegExp(`(fill|stroke):\\s*${colorInfo.originalColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g'),
                `$1: ${colorInfo.currentColor}`
              )
            }
          })
        }
        
        // 重新加载SVG
        this.fabric.loadSVGFromString(newSvgContent, (objects, options) => {
          const newSvgObject = this.fabric.util.groupSVGElements(objects, options)
          
          // 保持当前的位置、尺寸等属性
          newSvgObject.set({
            left: this.selectedObject.left,
            top: this.selectedObject.top,
            scaleX: this.selectedObject.scaleX,
            scaleY: this.selectedObject.scaleY,
            angle: this.selectedObject.angle,
            opacity: this.selectedObject.opacity,
            materialId: this.selectedObject.materialId,
            originalSvg: this.selectedObject.originalSvg,
            variableKey: this.selectedObject.variableKey,
            svgColors: this.selectedObject.svgColors
          })
          
          // 移除旧的SVG，添加新的SVG
          this.canvas.remove(this.selectedObject)
          this.canvas.add(newSvgObject)
          this.canvas.setActiveObject(newSvgObject)
          this.selectedObject = newSvgObject
          
          this.canvas.renderAll()
          this.$emit('change', this.getTemplateData())
        })
      } catch (err) {
        console.error('更新SVG多颜色失败:', err)
        this.$message.error('更新颜色失败')
      }
    },

    // 应用常用颜色到SVG
    applyCommonColor(color) {
      if (!this.selectedObject || !this.selectedObject.originalSvg) return
      
      if (this.selectedObject.svgColors && this.selectedObject.svgColors.length > 0) {
        // 多颜色模式：应用到第一个颜色
        this.updateSvgMultiColor(0, color)
      } else {
        // 单颜色模式：使用原有方法
        this.updateSvgColor(color)
      }
    },




    // 加载图片
    async loadImageWithProxy(imageUrl) {
      return new Promise((resolve, reject) => {
        const img = new Image()
        
        // 处理跨域
        img.crossOrigin = 'anonymous'
        
        // 为WebP格式添加特殊处理
        if (imageUrl.toLowerCase().includes('.webp')) {
          // 尝试添加时间戳避免缓存问题
          const timestamp = new Date().getTime()
          const separator = imageUrl.includes('?') ? '&' : '?'
          img.src = `${imageUrl}${separator}_t=${timestamp}`
        } else {
          img.src = imageUrl
        }
        
        img.onload = () => {
          resolve(img)
        }
        
        img.onerror = (error) => {
          console.warn('❌ 图片加载失败:', imageUrl, error)
          
          // 如果加载失败，创建占位图片
          this.createPlaceholderImage(200, 200, '#f0f0f0').then(resolve).catch(reject)
        }
      })
    },

    // 创建占位图片
    createPlaceholderImage(width, height, color = '#f0f0f0') {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        canvas.width = width
        canvas.height = height
        
        // 绘制占位图片
        ctx.fillStyle = color
        ctx.fillRect(0, 0, width, height)
        
        // 绘制边框
        ctx.strokeStyle = '#ddd'
        ctx.lineWidth = 2
        ctx.strokeRect(1, 1, width - 2, height - 2)
        
        // 绘制文字
        ctx.fillStyle = '#999'
        ctx.font = '14px Arial'
        ctx.textAlign = 'center'
        ctx.fillText('图片加载失败', width / 2, height / 2)
        
        const img = new Image()
        img.onload = () => resolve(img)
        img.src = canvas.toDataURL()
      })
    },

    // === 素材上传相关方法 ===
    
    // 图片上传前处理
    beforeImageUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isLt5M) {
        this.$message.error('图片大小不能超过 5MB!')
        return false
      }
      
      this.uploadImageMaterial(file)
      return false
    },

    // 上传图片素材
    async uploadImageMaterial(file) {
      this.uploading = true
      const formData = new FormData()
      formData.append('file', file)
      formData.append('name', file.name.split('.')[0])
      if (this.selectedImageCategory) {
        formData.append('categoryId', this.selectedImageCategory)
      }
      
      try {
        const response = await this.$http.post('/biz/posterMaterial/uploadImage', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        if (response.success) {
          this.$message.success('图片素材上传成功!')
          await this.loadImageMaterials()
        } else {
          this.$message.error(response.message || '上传失败')
        }
      } catch (error) {
        console.error('上传图片素材失败:', error)
        this.$message.error('上传失败: ' + (error.message || '网络错误'))
      } finally {
        this.uploading = false
      }
    },

    // 显示SVG上传模态框
    showSvgUploadModal() {
      this.uploadForm = {
        name: '',
        categoryId: this.selectedSvgCategory || null,
        tags: '',
        description: '',
        svgContent: ''
      }
      this.svgUploadVisible = true
    },

    // 上传SVG素材
    async uploadSvgMaterial() {
      if (!this.uploadForm.name.trim()) {
        this.$message.error('请输入素材名称')
        return
      }
      if (!this.uploadForm.svgContent.trim()) {
        this.$message.error('请输入SVG内容')
        return
      }
      
      this.uploading = true
      try {
        const response = await this.$http.post('/biz/posterMaterial/addSvg', this.uploadForm)
        
        if (response.success) {
          this.$message.success('SVG素材添加成功!')
          this.svgUploadVisible = false
          await this.loadSvgMaterials()
        } else {
          this.$message.error(response.message || '添加失败')
        }
      } catch (error) {
        console.error('添加SVG素材失败:', error)
        this.$message.error('添加失败: ' + (error.message || '网络错误'))
      } finally {
        this.uploading = false
      }
    },

    // 显示字体上传模态框
    showFontUploadModal() {
      this.fontUploadForm = {
        name: '',
        categoryId: this.selectedFontCategory || null,
        tags: '',
        description: ''
      }
      this.selectedFontFile = null
      this.fontUploadVisible = true
    },

    // 取消字体上传
    cancelFontUpload() {
      this.fontUploadVisible = false
      this.selectedFontFile = null
      this.fontUploadForm = {
        name: '',
        categoryId: null,
        tags: '',
        description: ''
      }
    },

    // 字体文件上传前的处理
    beforeFontUpload(file) {
      const isFont = file.type === 'font/ttf' ||
                    file.type === 'font/otf' ||
                    file.type === 'font/woff' ||
                    file.type === 'font/woff2' ||
                    file.name.toLowerCase().endsWith('.ttf') ||
                    file.name.toLowerCase().endsWith('.otf') ||
                    file.name.toLowerCase().endsWith('.woff') ||
                    file.name.toLowerCase().endsWith('.woff2')

      if (!isFont) {
        this.$message.error('只能上传字体文件格式！')
        return false
      }

      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('字体文件大小不能超过 10MB!')
        return false
      }

      // 自动设置字体名称
      if (!this.fontUploadForm.name) {
        this.fontUploadForm.name = file.name.replace(/\.[^/.]+$/, "")
      }

      this.selectedFontFile = file
      this.$message.success(`已选择字体文件: ${file.name}`)
      return false // 阻止自动上传
    },

    // 上传字体素材
    async uploadFontMaterial() {
      if (!this.selectedFontFile) {
        this.$message.error('请选择字体文件')
        return
      }
      if (!this.fontUploadForm.name.trim()) {
        this.$message.error('请输入字体名称')
        return
      }

      this.uploading = true
      const formData = new FormData()
      formData.append('file', this.selectedFontFile)
      formData.append('name', this.fontUploadForm.name)
      if (this.fontUploadForm.categoryId) {
        formData.append('categoryId', this.fontUploadForm.categoryId)
      }
      if (this.fontUploadForm.tags) {
        formData.append('tags', this.fontUploadForm.tags)
      }
      if (this.fontUploadForm.description) {
        formData.append('description', this.fontUploadForm.description)
      }

      try {
        const response = await this.$http.post('/biz/posterMaterial/uploadFont', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response.success) {
          // 检查是否是重复文件（通过URL判断是否复用了已有文件）
          const material = response.result
          if (material && material.fileUrl) {
            // 如果文件URL不包含当前时间戳，可能是复用了已有文件
            const today = new Date().toISOString().slice(0, 10).replace(/-/g, '')
            if (material.fileUrl.includes(today)) {
              this.$message.success('字体上传成功!')
            } else {
              this.$message.success('检测到相同字体文件，已复用现有文件!')
            }
          } else {
            this.$message.success('字体上传成功!')
          }

          this.fontUploadVisible = false
          this.selectedFontFile = null
          // 重置表单
          this.fontUploadForm = {
            name: '',
            categoryId: null,
            tags: '',
            description: ''
          }
          await this.loadFontMaterials()
        } else {
          this.$message.error(response.message || '上传失败')
        }
      } catch (error) {
        console.error('上传字体失败:', error)
        let errorMessage = '上传失败'
        if (error.response && error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }
        this.$message.error(errorMessage)
      } finally {
        this.uploading = false
      }
    },

    // 获取字体族名称
    getFontFamily(material) {
      // 对于系统字体，直接返回名称
      if (!material.fileUrl) {
        return material.name
      }
      // 对于自定义字体，返回自定义字体族名称
      return `custom-font-${material.id}`
    },

    // 获取当前选中对象的字体族名称（用于下拉框显示）
    getCurrentFontFamily() {
      if (!this.selectedObject || !this.selectedObject.fontFamily) {
        return undefined
      }

      const currentFont = this.selectedObject.fontFamily

      // 如果是自定义字体，直接返回
      if (currentFont.startsWith('custom-font-')) {
        return currentFont
      }

      // 如果是系统字体，直接返回
      return currentFont
    },

    // 应用字体到选中的文本对象
    async applyFontToSelected(material) {
      if (!this.selectedObject || !this.isTextObject(this.selectedObject)) {
        this.$message.warning('请先选择一个文本对象')
        return
      }

      try {
        // 增加使用次数
        await this.$http.post('/biz/posterMaterial/increaseDownload', null, {
          params: { id: material.id }
        })

        const fontFamily = this.getFontFamily(material)

        // 如果是自定义字体，需要先加载字体文件
        if (material.fileUrl) {
          await this.loadCustomFont(material)
        }

        // 应用字体到文本对象
        console.log('应用字体:', fontFamily, '到文本:', this.selectedObject.text)

        this.selectedObject.set('fontFamily', fontFamily)
        this.canvas.renderAll()
        this.$emit('change', this.getTemplateData())

        // 强制更新UI
        this.$forceUpdate()

        this.$message.success(`已应用字体: ${material.name}`)
      } catch (error) {
        console.error('应用字体失败:', error)
        this.$message.error('应用字体失败')
      }
    },

    // 加载自定义字体
    async loadCustomFont(material) {
      const fontFamily = `custom-font-${material.id}`

      console.log('开始加载字体:', material.name, fontFamily)

      // 检查字体是否已经加载
      if (document.fonts && document.fonts.check(`12px "${fontFamily}"`)) {
        console.log('字体已经加载过:', fontFamily)
        return
      }

      try {
        const fontFace = new FontFace(fontFamily, `url(${material.fileUrl})`)
        await fontFace.load()
        document.fonts.add(fontFace)

        console.log('字体加载成功:', fontFamily)
      } catch (error) {
        console.error('加载字体失败:', error, {
          name: material.name,
          fileUrl: material.fileUrl,
          fontFamily: fontFamily
        })
        throw new Error('字体加载失败: ' + error.message)
      }
    },

    // 判断是否为文本对象
    isTextObject(obj) {
      return obj && (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox')
    },



         showColorPicker(type, index = null) {
       this.currentColorType = type
       this.currentColorIndex = index
       
       // 根据类型设置当前颜色值
       let colorValue = '#000000'
       switch (type) {
         case 'font':
           colorValue = this.selectedObject && this.selectedObject.fill || '#000000'
           break
         case 'shape-fill':
           colorValue = this.selectedObject && this.selectedObject.fill || '#000000'
           break
         case 'shape-stroke':
           colorValue = this.selectedObject && this.selectedObject.stroke || '#000000'
           break
         case 'svg-multi':
           colorValue = this.selectedObject.svgColors[index].currentColor || '#000000'
           break
         case 'svg-single':
           colorValue = this.selectedObject && this.selectedObject.fill || '#000000'
           break
         case 'canvas-bg':
           colorValue = this.canvasBg || '#ffffff'
           break
         case 'qrcode-fg':
           colorValue = this.qrcodeForeground || '#000000'
           break
         case 'qrcode-bg':
           colorValue = this.qrcodeBackground || '#ffffff'
           break
       }
       
       // 设置颜色选择器的当前颜色
       this.currentColor = { hex: colorValue }
       this.colorPickerVisible = true
     },
     
     // 应用常用颜色
     applyCommonColor(color) {
       switch (this.currentColorType) {
         case 'font':
           this.updateProperty('fill', color)
           break
         case 'shape-fill':
           this.updateProperty('fill', color)
           break
         case 'shape-stroke':
           this.updateProperty('stroke', color)
           break
         case 'svg-multi':
           this.updateSvgMultiColor(this.currentColorIndex, color)
           break
         case 'svg-single':
           this.updateSvgColor(color)
           break
         case 'canvas-bg':
           this.updateCanvasBg(color)
           break
       }
       this.colorPickerVisible = false
     },
     
     // 应用自定义颜色
     applyCustomColor() {
       if (!this.currentColor || !this.currentColor.hex) {
         this.$message.warning('请选择颜色')
         return
       }

       const color = this.currentColor.hex
       
       switch (this.currentColorType) {
         case 'font':
         case 'shape-fill':
         case 'svg-single':
           if (this.selectedObject) {
             this.updateProperty('fill', color)
           } else {
             this.$message.warning('请先选择要修改的对象')
             return
           }
           break
         case 'shape-stroke':
           if (this.selectedObject) {
             this.updateProperty('stroke', color)
           } else {
             this.$message.warning('请先选择要修改的对象')
             return
           }
           break
         case 'svg-multi':
           if (this.selectedObject && this.currentColorIndex !== null) {
             this.updateSvgMultiColor(this.currentColorIndex, color)
           }
           break
         case 'canvas-bg':
           this.updateCanvasBg(color)
           break
         case 'qrcode-fg':
           this.qrcodeForeground = color
           break
         case 'qrcode-bg':
           this.qrcodeBackground = color
           break
       }
       
       this.colorPickerVisible = false
       this.$message.success(`颜色已更改为 ${color}`)
     },

    // === 新增的第三方库功能方法 ===
    
    // 颜色选择器变化事件
    onColorChange(color) {
      // 更新当前颜色
      this.currentColor = color
    },

    // QR码生成功能
    async handleAddQRCode() {
      try {
        const text = prompt('请输入二维码内容:', 'https://www.example.com')
        if (!text) return

        const QRCode = await import('qrcode')
        const qrCodeDataURL = await QRCode.toDataURL(text, {
          width: 200,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })

        this.fabric.Image.fromURL(qrCodeDataURL, (img) => {
          img.set({
            left: 100,
            top: 100,
            scaleX: 0.5,
            scaleY: 0.5
          })
          this.canvas.add(img)
          this.canvas.setActiveObject(img)
          this.canvas.renderAll()
          this.saveHistoryState()
        })

        this.$message.success('二维码添加成功')
      } catch (err) {
        console.error('添加二维码失败:', err)
        this.$message.error('添加二维码失败')
      }
    },

    // 搜索Unsplash图片
    async searchUnsplashImages(keyword) {
      try {
        if (!keyword) {
          this.$message.warning('请输入搜索关键词')
          return
        }

        this.unsplashLoading = true
        this.unsplashImages = []

        // 调用Unsplash API搜索图片
        const result = await unsplash.search.getPhotos({
          query: keyword,
          page: 1,
          perPage: 20,
          orientation: 'portrait'
        })

        if (result.errors) {
          console.error('Unsplash API错误:', result.errors)
          this.$message.error('搜索图片失败: ' + result.errors[0])
          return
        }

        // 处理搜索结果
        const photos = result.response.results
        this.unsplashImages = photos.map(photo => ({
          id: photo.id,
          urls: {
            small: photo.urls.small,
            regular: photo.urls.regular,
            full: photo.urls.full
          },
          description: photo.description || photo.alt_description || '',
          alt_description: photo.alt_description || '',
          user: {
            name: photo.user.name,
            links: {
              html: photo.user.links.html
            }
          },
          links: {
            download_location: photo.links.download_location
          }
        }))

        console.log('搜索到图片:', this.unsplashImages.length, '张')
        
      } catch (err) {
        console.error('搜索图片失败:', err)
        this.$message.error('搜索图片失败: ' + (err.message || '网络错误'))
      } finally {
        this.unsplashLoading = false
      }
    },

    // 添加Unsplash图片到画布
    async addUnsplashImage(photo) {
      try {
        // 触发下载统计（Unsplash API要求）
        if (photo.links && photo.links.download_location) {
          fetch(photo.links.download_location, {
            headers: {
              Authorization: `Client-ID *******************************************`
            }
          }).catch(err => console.warn('下载统计失败:', err))
        }

        // 使用regular尺寸的图片
        const imageUrl = photo.urls.regular
        
        // 创建fabric图片对象
        const fabricImage = await new Promise((resolve, reject) => {
          fabric.Image.fromURL(imageUrl, (img) => {
            if (img) {
              resolve(img)
            } else {
              reject(new Error('图片加载失败'))
            }
          }, { crossOrigin: 'anonymous' })
        })

        // 设置图片属性
        fabricImage.set({
          left: 50,
          top: 50,
          scaleX: 0.5,
          scaleY: 0.5,
          selectable: true,
          hasControls: true,
          hasBorders: true,
          cornerStyle: 'circle',
          cornerSize: 10,
          transparentCorners: false,
          cornerColor: '#4CAF50',
          borderColor: '#4CAF50'
        })

        // 添加到画布
        this.canvas.add(fabricImage)
        this.canvas.setActiveObject(fabricImage)
        this.canvas.renderAll()

        // 保存历史状态
        this.saveState()

        this.$message.success(`已添加图片: ${photo.user.name}`)
        
      } catch (err) {
        console.error('添加Unsplash图片失败:', err)
        this.$message.error('添加图片失败: ' + (err.message || '未知错误'))
      }
    },

    // 添加二维码（简化版本）
    addQRCode() {
      this.handleAddQRCode()
    }

  },
  
  watch: {
    templateData: {
      handler(newData) {
        if (newData) {
          this.loadTemplateData(newData)
        }
      },
      immediate: true
    },
    
    width() {
      if (this.canvas) {
        this.canvas.setWidth(this.width)
      }
    },
    
    height() {
      if (this.canvas) {
        this.canvas.setHeight(this.height)
      }
    }
  }
}
</script>

<style scoped>
.poster-template-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  min-height: calc(100vh - 112px); /* 减去标签页和其他UI元素的高度 */
}

/* 顶部导航栏 */
.editor-header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  flex: 0 0 300px;
  padding-right: 16px;
}

.header-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
}

.header-right {
  flex: 0 0 300px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
}

.template-info {
  display: flex;
  align-items: center;
}

.template-name-input {
  width: 200px;
  font-weight: 500;
}

.template-desc-input {
  width: 200px;
}

.editor-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 主编辑区域 */
.editor-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧工具栏 */
.editor-toolbar {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
  border-right: 1px solid #e1e4e8;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* 模式切换器 */
.mode-switcher {
  padding: 20px 16px;
  border-bottom: 1px solid #e1e4e8;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
}

.mode-switcher .ant-btn-group {
  width: 100%;
}

.mode-switcher .ant-btn {
  flex: 1;
  border-radius: 6px !important;
  font-weight: 500;
  transition: all 0.2s ease;
}

.mode-switcher .ant-btn:first-child {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.mode-switcher .ant-btn:last-child {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

/* 工具面板 */
.tools-panel {
  flex: 1;
  overflow-y: auto;
  padding: 20px 16px;
  background: rgba(255, 255, 255, 0.5);
}

.tool-section {
  margin-bottom: 28px;
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f2f5;
}

.section-header {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 14px;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  position: relative;
  padding-bottom: 6px;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 1px;
}

/* 素材面板 */
.materials-panel {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 20px 16px;
  background: rgba(255, 255, 255, 0.5);
}

.material-types {
  padding: 16px;
  border-bottom: 1px solid #e1e4e8;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 8px 8px 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);
  margin-bottom: 16px;
}

.material-types .ant-radio-group {
  width: 100%;
}

.material-types .ant-radio-button-wrapper {
  flex: 1;
  text-align: center;
}

.material-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f2f5;
}

/* 兼容旧样式 */
.toolbar-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tool-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  background: #fff;
}

.tool-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.tool-item.active {
  border-color: #1890ff;
  background: #e6f7ff;
  color: #1890ff;
}

.tool-item .anticon {
  font-size: 16px;
  margin-bottom: 4px;
}

.tool-item span {
  font-size: 11px;
  text-align: center;
}

.layer-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.layer-controls .ant-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

/* 中间画布区域 */
.editor-canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.canvas-toolbar {
  height: 40px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 16px;
  justify-content: center;
}

.canvas-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.zoom-info {
  font-size: 12px;
  color: #666;
  min-width: 40px;
  text-align: center;
}

.canvas-container {
  flex: 1;
  padding: 10px;
  overflow: auto;
  position: relative;
  min-height: 0; /* 允许flex容器收缩 */
}

.canvas-scroll-area {
  position: relative;
}

.canvas-wrapper {
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
  transform-origin: center;
  transition: transform 0.2s ease;
  position: absolute;
  top: calc(50% - 10px); /* 向上偏移10px，补偿顶部工具栏 */
  left: 50%;
  transform-origin: center;
}

/* 右侧属性面板 */
.editor-properties {
  width: 300px;
  background: #fafafa;
  border-left: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.editor-properties .ant-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-properties .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.editor-properties .ant-tabs-content {
  height: 100%;
}

.editor-properties .ant-tabs-tabpane {
  height: 100%;
  overflow: hidden;
}

.editor-properties .ant-tabs-bar {
  margin-bottom: 0;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.property-panel {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.variable-panel {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
}

.property-section {
  margin-bottom: 24px;
}

.property-section .section-title {
  font-weight: 500;
  font-size: 14px;
  color: #262626;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
  text-transform: none;
  letter-spacing: normal;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

.form-item label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.form-item .ant-input-number,
.form-item .ant-input,
.form-item .ant-select {
  font-size: 12px;
}

.color-input {
  display: flex;
  gap: 8px;
  align-items: center;
}



.color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
}

.color-picker input[type="color"] {
  width: 100%;
  height: 100%;
  border: none;
  cursor: pointer;
}

/* 多颜色输入样式 */
.multi-color-inputs {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-input-group {
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  padding: 12px;
  background: #fafbfc;
}

.color-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.color-name {
  font-weight: 500;
  color: #24292e;
  font-size: 13px;
}

.color-original {
  font-size: 11px;
  color: #6a737d;
  background: #e1e4e8;
  padding: 2px 6px;
  border-radius: 4px;
}

.color-input-group .color-input {
  margin-bottom: 0;
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.variable-list {
  margin-bottom: 16px;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.variable-info {
  flex: 1;
}

.variable-key {
  font-weight: 500;
  font-size: 13px;
  color: #262626;
  margin-bottom: 2px;
}

.variable-name {
  font-size: 12px;
  color: #8c8c8c;
}

.empty-state {
  text-align: center;
  color: #bfbfbf;
  font-size: 12px;
  padding: 32px 16px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

.form-item label {
  font-size: 12px;
  color: #595959;
  font-weight: 500;
  margin: 0;
}

.form-item .ant-input-number,
.form-item .ant-input,
.form-item .ant-select,
.form-item .ant-textarea {
  width: 100%;
}

.color-input {
  display: flex;
  gap: 6px;
  align-items: center;
}

.color-input .ant-input {
  flex: 1;
}

.color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.3s;
}

.color-picker:hover {
  border-color: #40a9ff;
}

.color-picker input[type="color"] {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  cursor: pointer;
  background: none;
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.button-grid .ant-btn {
  font-size: 12px;
  height: 28px;
}

.no-selection {
  padding: 16px;
  text-align: center;
  color: #999;
}

.variable-panel {
  padding: 12px;
  height: 100%;
  overflow-y: auto;
}

.variable-list {
  margin-bottom: 0;
}

.variable-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 6px;
  background: #fafafa;
  transition: all 0.2s;
}

.variable-item:hover {
  border-color: #d9d9d9;
  background: #f5f5f5;
}

.variable-item:last-child {
  margin-bottom: 0;
}

.variable-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.variable-key {
  color: #262626;
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 2px;
}

.variable-name {
  color: #8c8c8c;
  font-size: 11px;
  line-height: 1.2;
}

.empty-state {
  text-align: center;
  color: #bfbfbf;
  font-size: 12px;
  padding: 20px 0;
  font-style: italic;
}

.page-header {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.page-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.page-title h2 {
  margin: 0;
  color: #262626;
  font-size: 20px;
  font-weight: 500;
}

.page-actions {
  display: flex;
  gap: 8px;
}

/* 滚动条样式 */
.property-panel::-webkit-scrollbar,
.variable-panel::-webkit-scrollbar,
.editor-toolbar::-webkit-scrollbar {
  width: 6px;
}

.property-panel::-webkit-scrollbar-track,
.variable-panel::-webkit-scrollbar-track,
.editor-toolbar::-webkit-scrollbar-track {
  background: transparent;
}

.property-panel::-webkit-scrollbar-thumb,
.variable-panel::-webkit-scrollbar-thumb,
.editor-toolbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.property-panel::-webkit-scrollbar-thumb:hover,
.variable-panel::-webkit-scrollbar-thumb:hover,
.editor-toolbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 输入框聚焦效果优化 */
.form-item .ant-input:focus,
.form-item .ant-input-number:focus,
.form-item .ant-select:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 滑块样式优化 */
.form-item .ant-slider {
  margin: 8px 0 4px 0;
}

.form-item .ant-slider-track {
  background-color: #40a9ff;
}

.form-item .ant-slider-handle {
  border: 2px solid #40a9ff;
}

/* 按钮样式微调 */
.button-grid .ant-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 素材面板样式 */
.material-tabs {
  margin-top: 8px;
}

.material-tabs .ant-tabs-bar {
  margin-bottom: 8px;
}

.material-tabs .ant-tabs-tab {
  padding: 4px 8px !important;
  font-size: 11px;
}

.material-categories {
  margin-bottom: 8px;
}

.material-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
}

.material-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 6px;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  min-height: 80px;
  position: relative;
  overflow: hidden;
}

.material-item:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
  background: linear-gradient(145deg, #ffffff 0%, #f0f8ff 100%);
}

.material-item img {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.material-item:hover img {
  transform: scale(1.05);
}

.material-item .svg-preview {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background: rgba(24, 144, 255, 0.05);
  transition: all 0.2s ease;
}

.material-item:hover .svg-preview {
  background: rgba(24, 144, 255, 0.1);
  transform: scale(1.05);
}

.material-item .svg-preview svg {
  max-width: 100%;
  max-height: 100%;
}


.material-item .material-name {
  font-size: 11px;
  color: #4a5568;
  text-align: center;
  margin-top: 6px;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  font-weight: 500;
}

.material-empty {
  grid-column: 1 / -1;
  text-align: center;
  color: #bfbfbf;
  font-size: 11px;
  padding: 20px 0;
}


/* 颜色选择器样式 */
.color-palette {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 4px;
  margin-top: 4px;
}

.color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s;
}

.color-item:hover {
  border-color: #1890ff;
  transform: scale(1.1);
}

/* 优化其他颜色选择器 */
.form-item .color-input {
  display: flex;
  gap: 8px;
  align-items: center;
}

.form-item .color-input .ant-input {
  flex: 1;
}

.form-item .color-picker {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: border-color 0.3s;
}

.form-item .color-picker:hover {
  border-color: #40a9ff;
}

.form-item .color-picker input[type="color"] {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  cursor: pointer;
  background: none;
}

/* 响应式调整 */
@media (max-width: 1400px) {
  .editor-toolbar {
    width: 260px;
  }
  
  .editor-properties {
    width: 280px;
  }
  
  .header-left {
    flex: 0 0 270px;
  }
  
  .header-right {
    flex: 0 0 280px;
  }
}

@media (max-width: 1200px) {
  .tool-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .button-grid {
    grid-template-columns: 1fr;
  }
  
  .editor-toolbar {
    width: 240px;
  }
  
  .material-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .editor-properties {
    width: 260px;
  }
}

.color-preview {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.color-swatch {
  width: 20px;
  height: 20px;
  border: 1px solid #ccc;
  margin-right: 4px;
}

.color-label {
  font-size: 12px;
  color: #666;
}

/* 颜色选择器弹窗样式 */
.color-picker-modal {
  padding: 12px 0;
}

.current-color-section {
  margin-bottom: 16px;
}

.common-colors-section {
  margin-bottom: 20px;
}

.current-color-section label,
.common-colors-section label,
.color-picker-section label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.color-input-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-preview {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  flex-shrink: 0;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.color-input-field {
  width: 230px;
}

.color-picker-trigger {
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.color-picker-trigger:hover {
  color: #1890ff;
}

.color-picker-native {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  border: none;
  background: none;
}

.color-groups {
  margin-top: 8px;
}

.color-group {
  margin-bottom: 12px;
}

.color-group-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: uppercase;
}

.color-group-colors {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 4px;
}

.common-color-item-modal {
  width: 32px;
  height: 32px;
  border: 2px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.common-color-item-modal:hover {
  transform: scale(1.1);
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.color-picker-actions {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 优化的颜色选择器样式 */
.color-picker-modal {
  text-align: center;
}

.sketch-picker-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.sketch-picker-wrapper .vue-color__sketch {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
}

.current-color-preview {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.color-preview-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.color-preview-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.color-preview-sample {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.color-preview-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #595959;
  font-weight: 500;
  background: #fff;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #d9d9d9;
}

.color-picker-actions {
  margin-top: 16px;
}

/* 确保调色板居中对齐 */
.color-picker-modal .vue-color__sketch {
  margin: 0 auto;
}

/* 字体素材样式 */
.font-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.font-item {
  padding: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  background: #fff;
}

.font-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.font-item .font-preview {
  font-size: 18px;
  font-weight: normal;
  color: #333;
  margin-bottom: 4px;
  height: 24px;
  line-height: 24px;
  overflow: hidden;
}

.font-item .material-name {
  font-size: 11px;
  color: #666;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>