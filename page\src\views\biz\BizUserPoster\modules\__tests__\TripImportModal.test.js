// Basic test to verify TripImportModal component structure
import { mount } from '@vue/test-utils'
import TripImportModal from '../TripImportModal.vue'

describe('TripImportModal', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(TripImportModal, {
      mocks: {
        $http: {
          get: jest.fn().mockResolvedValue({
            success: true,
            result: {
              records: [],
              total: 0
            }
          })
        },
        $message: {
          error: jest.fn(),
          warning: jest.fn(),
          success: jest.fn()
        }
      }
    })
  })

  afterEach(() => {
    wrapper.destroy()
  })

  it('should render modal with correct title', () => {
    expect(wrapper.find('.ant-modal-title').text()).toBe('导入行程数据')
  })

  it('should have three steps', () => {
    const steps = wrapper.findAll('.ant-steps-item')
    expect(steps.length).toBe(3)
    expect(steps.at(0).text()).toContain('选择行程')
    expect(steps.at(1).text()).toContain('数据预览')
    expect(steps.at(2).text()).toContain('变量映射')
  })

  it('should initialize with step 0', () => {
    expect(wrapper.vm.currentStep).toBe(0)
  })

  it('should show search form in first step', () => {
    expect(wrapper.find('input[placeholder="请输入行程名称"]').exists()).toBe(true)
    expect(wrapper.find('.ant-select').exists()).toBe(true)
  })

  it('should initialize variable mappings correctly', () => {
    const templateVariables = [
      { key: 'tripName', name: '行程名称', type: 'text', description: '海报标题' }
    ]
    
    wrapper.vm.initVariableMappings(templateVariables)
    
    expect(wrapper.vm.variableMappings.length).toBe(1)
    expect(wrapper.vm.variableMappings[0].templateVariable).toBe('tripName')
    expect(wrapper.vm.variableMappings[0].tripField).toBe('tripName')
  })

  it('should handle default mapping correctly', () => {
    expect(wrapper.vm.getDefaultMapping('tripName')).toBe('tripName')
    expect(wrapper.vm.getDefaultMapping('price')).toBe('price')
    expect(wrapper.vm.getDefaultMapping('unknown')).toBe(null)
  })

  it('should format preview values correctly', () => {
    wrapper.vm.selectedTripData = {
      tripName: '测试行程',
      price: 1000,
      dayNum: 5,
      imgUrl: 'http://example.com/image.jpg'
    }

    expect(wrapper.vm.getPreviewValue('tripName')).toBe('测试行程')
    expect(wrapper.vm.getPreviewValue('price')).toBe('¥1000')
    expect(wrapper.vm.getPreviewValue('dayNum')).toBe('5天')
    expect(wrapper.vm.getPreviewValue('imgUrl')).toBe('[图片]')
    expect(wrapper.vm.getPreviewValue('nonexistent')).toBe('-')
  })
})