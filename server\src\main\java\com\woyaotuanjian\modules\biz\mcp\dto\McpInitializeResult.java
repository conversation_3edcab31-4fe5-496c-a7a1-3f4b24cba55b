package com.woyaotuanjian.modules.biz.mcp.dto;

import lombok.Data;

import java.util.Map;

/**
 * MCP初始化响应结果
 */
@Data
public class McpInitializeResult {
    
    /**
     * 协议版本
     */
    private String protocolVersion;
    
    /**
     * 服务器能力
     */
    private Map<String, Object> capabilities;
    
    /**
     * 服务器信息
     */
    private McpServerInfo serverInfo;
    
    public McpInitializeResult(String protocolVersion, Map<String, Object> capabilities, McpServerInfo serverInfo) {
        this.protocolVersion = protocolVersion;
        this.capabilities = capabilities;
        this.serverInfo = serverInfo;
    }
}