import { axios } from '@/utils/request'

const api = {
  getSystemConfig: '/biz/posterConfig/getSystemConfig',
  updateSystemConfig: '/biz/posterConfig/updateSystemConfig',
  getUserPermissionConfig: '/biz/posterConfig/getUserPermissionConfig',
  listPermissions: '/biz/posterConfig/listPermissions',
  assignUserPermission: '/biz/posterConfig/assignUserPermission',
  assignRolePermission: '/biz/posterConfig/assignRolePermission',
  revokeUserPermission: '/biz/posterConfig/revokeUserPermission',
  revokeRolePermission: '/biz/posterConfig/revokeRolePermission',
  deletePermission: '/biz/posterConfig/deletePermission'
}

/**
 * 获取系统配置
 */
export function getSystemConfig() {
  return axios({
    url: api.getSystemConfig,
    method: 'get'
  })
}

/**
 * 更新系统配置
 * @param {Object} configData 配置数据
 */
export function updateSystemConfig(configData) {
  return axios({
    url: api.updateSystemConfig,
    method: 'post',
    data: configData
  })
}

/**
 * 获取用户权限配置
 * @param {Number} userId 用户ID（可选）
 */
export function getUserPermissionConfig(userId) {
  return axios({
    url: api.getUserPermissionConfig,
    method: 'get',
    params: { userId }
  })
}

/**
 * 分页查询权限配置列表
 * @param {Object} params 查询参数
 */
export function listPermissions(params) {
  return axios({
    url: api.listPermissions,
    method: 'get',
    params
  })
}

/**
 * 分配用户权限
 * @param {Object} data 权限数据
 */
export function assignUserPermission(data) {
  return axios({
    url: api.assignUserPermission,
    method: 'post',
    data
  })
}

/**
 * 分配角色权限
 * @param {Object} data 权限数据
 */
export function assignRolePermission(data) {
  return axios({
    url: api.assignRolePermission,
    method: 'post',
    data
  })
}

/**
 * 撤销用户权限
 * @param {Object} data 权限数据
 */
export function revokeUserPermission(data) {
  return axios({
    url: api.revokeUserPermission,
    method: 'post',
    data
  })
}

/**
 * 撤销角色权限
 * @param {Object} data 权限数据
 */
export function revokeRolePermission(data) {
  return axios({
    url: api.revokeRolePermission,
    method: 'post',
    data
  })
}

/**
 * 删除权限配置
 * @param {Object} params 删除参数
 */
export function deletePermission(params) {
  return axios({
    url: api.deletePermission,
    method: 'delete',
    params
  })
}