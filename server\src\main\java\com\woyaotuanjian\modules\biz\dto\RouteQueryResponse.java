package com.woyaotuanjian.modules.biz.dto;

import lombok.Data;
import java.util.List;

/**
 * 路线查询响应DTO
 */
@Data
public class RouteQueryResponse {
    
    /**
     * 距离（公里）
     */
    private String distance;
    
    /**
     * 时长（分钟）
     */
    private Integer duration;
    
    /**
     * 距离文本（如：12.5km）
     */
    private String distanceText;
    
    /**
     * 时长文本（如：1小时30分钟）
     */
    private String durationText;
    
    /**
     * 交通方式
     */
    private String transportType;
    
    /**
     * 路径点列表（用于绘制路线）
     */
    private List<RoutePoint> points;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 路径点
     */
    @Data
    public static class RoutePoint {
        private Double latitude;
        private Double longitude;
    }
} 