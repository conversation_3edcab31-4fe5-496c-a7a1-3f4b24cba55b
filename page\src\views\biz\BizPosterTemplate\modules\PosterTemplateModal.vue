<template>
  <a-modal
    :title="title"
    :width="1200"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form :form="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="模板名称">
              <a-input v-decorator="['templateName', validatorRules.templateName]" placeholder="请输入模板名称"></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="状态">
              <a-select v-decorator="['status', validatorRules.status]" placeholder="请选择状态">
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="画布宽度">
              <a-input-number v-decorator="['width', validatorRules.width]" :min="100" :max="2000" placeholder="画布宽度"></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="画布高度">
              <a-input-number v-decorator="['height', validatorRules.height]" :min="100" :max="3000" placeholder="画布高度"></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="排序">
              <a-input-number v-decorator="['sortOrder', validatorRules.sortOrder]" :min="0" placeholder="排序值"></a-input-number>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="24">
            <a-form-item label="模板描述">
              <a-textarea v-decorator="['templateDesc']" :rows="3" placeholder="请输入模板描述"></a-textarea>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row>
          <a-col :span="24">
            <a-form-item label="模板设计">
              <div class="template-editor-container">
                <poster-template-editor 
                  ref="templateEditor"
                  :template-data="templateData"
                  :width="canvasWidth"
                  :height="canvasHeight"
                  @change="handleTemplateDataChange">
                </poster-template-editor>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage'
import { validateDuplicateValue } from '@/utils/util'
import PosterTemplateEditor from './PosterTemplateEditor'

export default {
  name: 'PosterTemplateModal',
  components: {
    PosterTemplateEditor
  },
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      templateData: null,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      validatorRules: {
        templateName: {
          rules: [
            { required: true, message: '请输入模板名称!' },
            { min: 1, max: 100, message: '模板名称长度在1-100个字符之间!' }
          ]
        },
        status: {
          rules: [
            { required: true, message: '请选择状态!' }
          ],
          initialValue: 1
        },
        width: {
          rules: [
            { required: true, message: '请输入画布宽度!' }
          ],
          initialValue: 750
        },
        height: {
          rules: [
            { required: true, message: '请输入画布高度!' }
          ],
          initialValue: 1334
        },
        sortOrder: {
          initialValue: 0
        }
      },
      url: {
        add: '/biz/posterTemplate/add',
        edit: '/biz/posterTemplate/edit'
      }
    }
  },
  computed: {
    canvasWidth() {
      return this.form.getFieldValue('width') || 750
    },
    canvasHeight() {
      return this.form.getFieldValue('height') || 1334
    }
  },
  methods: {
    add() {
      this.edit({})
    },
    
    edit(record) {
      this.form.resetFields()
      this.model = Object.assign({}, record)
      this.visible = true
      
      this.$nextTick(() => {
        this.form.setFieldsValue(record)
        
        // 设置模板数据
        if (record.templateData) {
          try {
            this.templateData = JSON.parse(record.templateData)
          } catch (e) {
            console.error('解析模板数据失败:', e)
            this.templateData = this.getDefaultTemplateData()
          }
        } else {
          this.templateData = this.getDefaultTemplateData()
        }
        
        // 通知编辑器更新
        if (this.$refs.templateEditor) {
          this.$refs.templateEditor.loadTemplateData(this.templateData)
        }
      })
    },
    
    getDefaultTemplateData() {
      return {
        canvas: {
          width: this.canvasWidth,
          height: this.canvasHeight,
          backgroundColor: '#ffffff'
        },
        objects: [],
        variables: []
      }
    },
    
    handleTemplateDataChange(data) {
      this.templateData = data
    },
    
    handleOk() {
      const that = this
      // 触发表单验证
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true
          
          // 获取模板数据
          const templateData = this.$refs.templateEditor.getTemplateData()
          
          let httpurl = ''
          let method = ''
          if (!this.model.id) {
            httpurl = this.url.add
            method = 'post'
          } else {
            httpurl = this.url.edit
            method = 'put'
            values.id = this.model.id
          }
          
          // 添加模板数据
          values.templateData = JSON.stringify(templateData)
          
          httpAction(httpurl, values, method).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.$emit('ok')
              that.handleCancel()
            } else {
              that.$message.warning(res.message)
            }
          }).finally(() => {
            that.confirmLoading = false
          })
        }
      })
    },
    
    handleCancel() {
      this.visible = false
      this.form.resetFields()
      this.templateData = null
    }
  }
}
</script>

<style scoped>
.template-editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 500px;
}
</style>