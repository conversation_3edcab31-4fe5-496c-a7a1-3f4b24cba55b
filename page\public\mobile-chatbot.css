/* 游美乐AI客服 - 手机端专用样式 */

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  overflow: hidden;
}

/* 聊天容器 */
.webchat-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: #fff !important;
  z-index: 9999 !important;
  display: flex !important;
  flex-direction: column !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  font-size: 16px !important;
}

/* 隐藏浮动按钮 */
.webchat-bubble-tip {
  display: none !important;
}

/* 聊天根容器 */
.nlux-AiChat-root {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  background: #fff !important;
  border-radius: 0 !important;
  border: none !important;
}

/* 聊天头部 */
.nlux-AiChat-header {
  background: #1464E4 !important;
  color: #fff !important;
  padding: 15px 20px !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  text-align: center !important;
  border-radius: 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 聊天头部标题 */
.nlux-AiChat-header h1,
.nlux-AiChat-header .nlux-AiChat-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #fff !important;
  margin: 0 !important;
}

/* 消息容器 */
.nlux-AiChat-messagesContainer {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 15px !important;
  background: #f8f9fa !important;
  -webkit-overflow-scrolling: touch !important;
}

/* 消息列表 */
.nlux-AiChat-messagesList {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
  min-height: 100% !important;
}

/* 消息项 */
.nlux-AiChat-message {
  display: flex !important;
  align-items: flex-start !important;
  gap: 8px !important;
  max-width: 85% !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* 用户消息 */
.nlux-AiChat-message.nlux-AiChat-message--user {
  flex-direction: row-reverse !important;
  align-self: flex-end !important;
  margin-left: auto !important;
}

/* AI消息 */
.nlux-AiChat-message.nlux-AiChat-message--ai {
  align-self: flex-start !important;
  margin-right: auto !important;
}

/* 消息气泡 */
.nlux-AiChat-messageBubble {
  padding: 12px 16px !important;
  border-radius: 18px !important;
  font-size: 16px !important;
  line-height: 1.4 !important;
  max-width: 100% !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* 用户消息气泡 */
.nlux-AiChat-message--user .nlux-AiChat-messageBubble {
  background: #1464E4 !important;
  color: #fff !important;
  border-bottom-right-radius: 6px !important;
}

/* AI消息气泡 */
.nlux-AiChat-message--ai .nlux-AiChat-messageBubble {
  background: #fff !important;
  color: #333 !important;
  border: 1px solid #e8e8e8 !important;
  border-bottom-left-radius: 6px !important;
}

/* 头像 */
.nlux-AiChat-messageAvatar {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50% !important;
  flex-shrink: 0 !important;
  overflow: hidden !important;
  border: 2px solid #e8e8e8 !important;
}

.nlux-AiChat-messageAvatar img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

/* 输入区域 */
.nlux-AiChat-composer {
  background: #fff !important;
  border-top: 1px solid #e8e8e8 !important;
  padding: 15px !important;
  display: flex !important;
  align-items: flex-end !important;
  gap: 10px !important;
  min-height: 70px !important;
}

/* 输入框 */
.nlux-AiChat-composerInput {
  flex: 1 !important;
  min-height: 40px !important;
  max-height: 120px !important;
  padding: 10px 15px !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 20px !important;
  font-size: 16px !important;
  line-height: 1.4 !important;
  background: #f8f9fa !important;
  resize: none !important;
  outline: none !important;
  font-family: inherit !important;
}

.nlux-AiChat-composerInput:focus {
  border-color: #1464E4 !important;
  background: #fff !important;
}

/* 发送按钮 */
.nlux-AiChat-composerSendButton {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  background: #1464E4 !important;
  color: #fff !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.nlux-AiChat-composerSendButton:hover {
  background: #0d4fb8 !important;
}

.nlux-AiChat-composerSendButton:disabled {
  background: #ccc !important;
  cursor: not-allowed !important;
}

/* 对话启动器 */
.nlux-AiChat-conversationStarters {
  display: flex !important;
  flex-direction: column !important;
  gap: 8px !important;
  padding: 20px !important;
  background: #f8f9fa !important;
}

.nlux-AiChat-conversationStarter {
  background: #fff !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-size: 15px !important;
  color: #333 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  text-align: left !important;
}

.nlux-AiChat-conversationStarter:hover {
  background: #f0f0f0 !important;
  border-color: #1464E4 !important;
}

/* 加载状态 */
.nlux-AiChat-typingIndicator {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 12px 16px !important;
  background: #fff !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 18px !important;
  border-bottom-left-radius: 6px !important;
  max-width: 85% !important;
  align-self: flex-start !important;
  margin-right: auto !important;
}

.nlux-AiChat-typingIndicator-dots {
  display: flex !important;
  gap: 4px !important;
}

.nlux-AiChat-typingIndicator-dot {
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  background: #1464E4 !important;
  animation: typing-dot 1.4s infinite ease-in-out !important;
}

.nlux-AiChat-typingIndicator-dot:nth-child(1) {
  animation-delay: -0.32s !important;
}

.nlux-AiChat-typingIndicator-dot:nth-child(2) {
  animation-delay: -0.16s !important;
}

@keyframes typing-dot {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 欢迎消息 */
.nlux-AiChat-welcomeMessage {
  text-align: center !important;
  padding: 30px 20px !important;
  color: #666 !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
}

.nlux-AiChat-welcomeMessage h2 {
  font-size: 20px !important;
  color: #333 !important;
  margin-bottom: 10px !important;
  font-weight: 600 !important;
}

.nlux-AiChat-welcomeMessage p {
  margin-bottom: 20px !important;
}

/* 错误消息 */
.nlux-AiChat-errorMessage {
  background: #fff2f0 !important;
  border: 1px solid #ffccc7 !important;
  color: #ff4d4f !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  margin: 10px 0 !important;
  font-size: 14px !important;
}

/* 滚动条样式 */
.nlux-AiChat-messagesContainer::-webkit-scrollbar {
  width: 4px !important;
}

.nlux-AiChat-messagesContainer::-webkit-scrollbar-track {
  background: transparent !important;
}

.nlux-AiChat-messagesContainer::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 2px !important;
}

.nlux-AiChat-messagesContainer::-webkit-scrollbar-thumb:hover {
  background: #999 !important;
}

/* 适配更小的屏幕 */
@media (max-width: 360px) {
  .nlux-AiChat-header {
    padding: 12px 15px !important;
    font-size: 16px !important;
  }
  
  .nlux-AiChat-messagesContainer {
    padding: 10px !important;
  }
  
  .nlux-AiChat-message {
    max-width: 90% !important;
  }
  
  .nlux-AiChat-messageBubble {
    padding: 10px 14px !important;
    font-size: 15px !important;
  }
  
  .nlux-AiChat-composer {
    padding: 12px !important;
  }
  
  .nlux-AiChat-composerInput {
    padding: 8px 12px !important;
    font-size: 15px !important;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .nlux-AiChat-header {
    padding: 10px 15px !important;
    font-size: 16px !important;
  }
  
  .nlux-AiChat-messagesContainer {
    padding: 10px !important;
  }
  
  .nlux-AiChat-composer {
    padding: 10px !important;
  }
  
  .nlux-AiChat-composerInput {
    min-height: 36px !important;
    max-height: 72px !important;
  }
  
  .nlux-AiChat-composerSendButton {
    width: 36px !important;
    height: 36px !important;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #1a1a1a !important;
  }
  
  .nlux-AiChat-root {
    background: #1a1a1a !important;
  }
  
  .nlux-AiChat-messagesContainer {
    background: #1a1a1a !important;
  }
  
  .nlux-AiChat-message--ai .nlux-AiChat-messageBubble {
    background: #2d2d2d !important;
    color: #fff !important;
    border-color: #404040 !important;
  }
  
  .nlux-AiChat-composer {
    background: #2d2d2d !important;
    border-top-color: #404040 !important;
  }
  
  .nlux-AiChat-composerInput {
    background: #1a1a1a !important;
    border-color: #404040 !important;
    color: #fff !important;
  }
  
  .nlux-AiChat-composerInput:focus {
    background: #2d2d2d !important;
  }
  
  .nlux-AiChat-conversationStarters {
    background: #1a1a1a !important;
  }
  
  .nlux-AiChat-conversationStarter {
    background: #2d2d2d !important;
    border-color: #404040 !important;
    color: #fff !important;
  }
  
  .nlux-AiChat-conversationStarter:hover {
    background: #404040 !important;
  }
  
  .nlux-AiChat-typingIndicator {
    background: #2d2d2d !important;
    border-color: #404040 !important;
  }
  
  .nlux-AiChat-welcomeMessage {
    color: #ccc !important;
  }
  
  .nlux-AiChat-welcomeMessage h2 {
    color: #fff !important;
  }
}

/* 动画效果 */
.nlux-AiChat-message {
  animation: message-appear 0.3s ease-out !important;
}

@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 确保触摸友好 */
.nlux-AiChat-conversationStarter,
.nlux-AiChat-composerSendButton {
  -webkit-tap-highlight-color: transparent !important;
  touch-action: manipulation !important;
}

/* 防止双击缩放 */
.nlux-AiChat-composerInput {
  touch-action: manipulation !important;
}

/* iOS Safari 兼容性 */
@supports (-webkit-touch-callout: none) {
  .nlux-AiChat-messagesContainer {
    -webkit-overflow-scrolling: touch !important;
  }
  
  .nlux-AiChat-composerInput {
    -webkit-appearance: none !important;
    border-radius: 20px !important;
  }
} 