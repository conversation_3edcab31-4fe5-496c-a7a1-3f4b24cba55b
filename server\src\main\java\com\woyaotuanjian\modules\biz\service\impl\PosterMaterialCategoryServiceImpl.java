package com.woyaotuanjian.modules.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.modules.biz.entity.PosterMaterialCategory;
import com.woyaotuanjian.modules.biz.mapper.PosterMaterialCategoryMapper;
import com.woyaotuanjian.modules.biz.service.IPosterMaterialCategoryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 海报素材分类
 * @Author: system
 * @Date: 2024-08-16
 * @Version: V1.0
 */
@Service
public class PosterMaterialCategoryServiceImpl extends ServiceImpl<PosterMaterialCategoryMapper, PosterMaterialCategory> implements IPosterMaterialCategoryService {

    @Override
    public List<PosterMaterialCategory> getTreeList() {
        QueryWrapper<PosterMaterialCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("sort_order", "id");
        return this.list(queryWrapper);
    }

    @Override
    public List<PosterMaterialCategory> getByParentId(Long parentId) {
        QueryWrapper<PosterMaterialCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("parent_id", parentId == null ? 0 : parentId);
        queryWrapper.eq("status", 1);
        queryWrapper.orderByAsc("sort_order", "id");
        return this.list(queryWrapper);
    }
}