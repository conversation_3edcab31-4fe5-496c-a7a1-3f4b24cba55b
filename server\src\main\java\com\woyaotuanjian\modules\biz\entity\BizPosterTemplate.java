package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 海报模板
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Data
@TableName("biz_poster_template")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="biz_poster_template对象", description="海报模板")
public class BizPosterTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    /**模板名称*/
    @Excel(name = "模板名称", width = 15)
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    
    /**模板描述*/
    @Excel(name = "模板描述", width = 15)
    @ApiModelProperty(value = "模板描述")
    private String templateDesc;
    
    /**模板配置数据(JSON)*/
    @ApiModelProperty(value = "模板配置数据(JSON)")
    private String templateData;
    
    /**缩略图URL*/
    @ApiModelProperty(value = "缩略图URL")
    private String thumbnailUrl;
    
    /**画布宽度*/
    @Excel(name = "画布宽度", width = 15)
    @ApiModelProperty(value = "画布宽度")
    private Integer width;
    
    /**画布高度*/
    @Excel(name = "画布高度", width = 15)
    @ApiModelProperty(value = "画布高度")
    private Integer height;
    
    /**状态 1-启用 0-禁用*/
    @Excel(name = "状态", width = 15, dicCode = "yn")
    @ApiModelProperty(value = "状态 1-启用 0-禁用")
    private Integer status;
    
    /**排序*/
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private Integer createBy;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private Integer updateBy;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}