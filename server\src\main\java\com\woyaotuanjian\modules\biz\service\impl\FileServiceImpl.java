package com.woyaotuanjian.modules.biz.service.impl;

import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ObjectMetadata;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import com.woyaotuanjian.modules.biz.service.FileService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.apache.commons.io.IOUtils;
import org.springframework.util.DigestUtils;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.FileInputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;
import java.util.UUID;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class FileServiceImpl implements FileService {
    @Autowired
    private COSClient cosClient;

    @Value("${tencent.cos.bucket}")
    private String bucket;

    @Value("${tencent.cos.cdnDomain}")
    private String cdnDomain;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    private static final String FILE_MD5_KEY_PREFIX = "file:md5:";
    private static final long FILE_MD5_EXPIRE_DAYS = 30;

    // 抽取公共方法处理文件扩展名
    private String getFilePathWithExtension(String pathPrefix, String contentType) {
        // 如果是目录路径（以/结尾）或为空，则生成完整的文件路径
        if (pathPrefix == null || pathPrefix.isEmpty() || pathPrefix.endsWith("/")) {
            String fileName = UUID.randomUUID().toString().replaceAll("-", "");
            String dateFolder = new DateTime().toString("yyyyMMdd");
            
            Map<String, String> contentTypeToExtension = new HashMap<>();
            contentTypeToExtension.put(FileService.IMAGE_JPEG, ".jpg");
            contentTypeToExtension.put(FileService.IMAGE_GIF, ".gif");
            contentTypeToExtension.put(FileService.WORD, ".docx");
            contentTypeToExtension.put(FileService.WORD2, ".docx");
            contentTypeToExtension.put(FileService.PDF, ".pdf");
            contentTypeToExtension.put(FileService.VIDEO, ".mp4");
            contentTypeToExtension.put(FileService.PPTX, ".pptx");
            contentTypeToExtension.put("image/png", ".png");
            contentTypeToExtension.put("image/webp", ".webp");

            // 添加字体文件的 MIME 类型映射
            contentTypeToExtension.put("font/ttf", ".ttf");
            contentTypeToExtension.put("font/otf", ".otf");
            contentTypeToExtension.put("font/woff", ".woff");
            contentTypeToExtension.put("font/woff2", ".woff2");
            contentTypeToExtension.put("application/font-sfnt", ".ttf");
            contentTypeToExtension.put("application/font-woff", ".woff");
            contentTypeToExtension.put("application/font-woff2", ".woff2");
            contentTypeToExtension.put("application/x-font-ttf", ".ttf");
            contentTypeToExtension.put("application/x-font-otf", ".otf");
            contentTypeToExtension.put("application/octet-stream", ".ttf"); // 某些浏览器可能发送这个类型
            
            String extension = contentTypeToExtension.getOrDefault(contentType, ".jpg");
            
            if (pathPrefix == null || pathPrefix.isEmpty()) {
                return dateFolder + "/" + fileName + extension;
            } else {
                return pathPrefix + dateFolder + "/" + fileName + extension;
            }
        }
        return pathPrefix;
    }

    // 抽取公共方法处理文件上传
    private String doUploadFile(InputStream inputStream, String contentType, String path) {
        ObjectMetadata imageMetadata = new ObjectMetadata();
        imageMetadata.setContentType(contentType);
        
        try {
            // 方案1：如果是文件流，直接获取大小
            if (inputStream instanceof FileInputStream) {
                FileInputStream fileInputStream = (FileInputStream) inputStream;
                imageMetadata.setContentLength(fileInputStream.getChannel().size());
            } 
            // 方案2：对于其他类型的流，先将内容读入字节数组以确保获取准确大小
            else {
                byte[] bytes = IOUtils.toByteArray(inputStream);
                imageMetadata.setContentLength(bytes.length);
                inputStream = new ByteArrayInputStream(bytes);
            }
            
            PutObjectRequest request = new PutObjectRequest(bucket, path, inputStream, imageMetadata);
            cosClient.putObject(request);
            return cdnDomain + "/" + path;
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败", e);
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.error("关闭输入流失败", e);
            }
        }
    }

    @Override
    public String uploadFile(InputStream inputStream, String contentType, String path) {
        path = getFilePathWithExtension(path, contentType);
        return doUploadFile(inputStream, contentType, path);
    }

    @Override
    public String uploadUrl(String url, String contentType, String path) {
        try {
            URL urlFile = new URL(url);
            HttpURLConnection httpUrl = (HttpURLConnection) urlFile.openConnection();
            httpUrl.connect();
            if (httpUrl.getResponseCode() == 200) {
                String cdnUrl = uploadFileWithDuplicateCheck(httpUrl.getInputStream(), contentType, path);
                httpUrl.disconnect();
                return cdnUrl;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public String uploadFileWithDuplicateCheck(InputStream inputStream, String contentType, String path) throws IOException {
        // 1. 计算文件MD5
        byte[] fileBytes = IOUtils.toByteArray(inputStream);
        String md5 = DigestUtils.md5DigestAsHex(fileBytes);
        
        // 2. 检查Redis中是否存在相同MD5的文件
        String existingFile = redisTemplate.opsForValue().get(FILE_MD5_KEY_PREFIX + md5);
        if (existingFile != null) {
            return existingFile;
        }
        
        // 3. 上传新文件
        String filename = uploadFile(new ByteArrayInputStream(fileBytes), contentType, path);
        
        // 4. 将MD5存入Redis并设置过期时间
        redisTemplate.opsForValue().set(
            FILE_MD5_KEY_PREFIX + md5, 
            filename, 
            FILE_MD5_EXPIRE_DAYS, 
            TimeUnit.DAYS
        );
        
        return filename;
    }
}
