package com.woyaotuanjian.modules.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.woyaotuanjian.modules.biz.entity.PosterMaterial;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 海报素材
 * @Author: system
 * @Date: 2024-08-16
 * @Version: V1.0
 */
public interface IPosterMaterialService extends IService<PosterMaterial> {
    
    /**
     * 根据类型和分类获取素材列表
     */
    List<PosterMaterial> getByTypeAndCategory(String type, Long categoryId);
    
    /**
     * 上传图片素材
     */
    PosterMaterial uploadImageMaterial(MultipartFile file, String name, Long categoryId, String tags, String description);
    
    /**
     * 添加SVG素材
     */
    PosterMaterial addSvgMaterial(String name, String svgContent, Long categoryId, String tags, String description);
    
    /**
     * 添加遮罩素材
     */
    PosterMaterial addMaskMaterial(String name, String svgContent, Long categoryId, String tags, String description);

    /**
     * 上传字体素材
     */
    PosterMaterial uploadFontMaterial(MultipartFile file, String name, Long categoryId, String tags, String description);

    /**
     * 增加使用次数
     */
    void increaseDownloadCount(Long id);
    
    /**
     * 根据标签搜索素材
     */
    List<PosterMaterial> searchByTags(String tags, String type);
}