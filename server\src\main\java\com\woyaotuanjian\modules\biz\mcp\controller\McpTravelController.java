package com.woyaotuanjian.modules.biz.mcp.controller;

import com.woyaotuanjian.modules.biz.mcp.dto.*;
import com.woyaotuanjian.modules.biz.mcp.exception.McpException;
import com.woyaotuanjian.modules.biz.mcp.exception.McpInternalErrorException;
import com.woyaotuanjian.modules.biz.mcp.exception.McpInvalidParamsException;
import com.woyaotuanjian.modules.biz.mcp.exception.McpInvalidRequestException;
import com.woyaotuanjian.modules.biz.mcp.exception.McpParseErrorException;
import com.woyaotuanjian.modules.biz.mcp.service.McpTravelToolService;
import com.woyaotuanjian.modules.biz.mcp.util.McpConstants;
import com.woyaotuanjian.modules.biz.mcp.util.McpJsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP行程查询控制器
 * 提供标准的MCP协议端点
 * 只有在mcp.travel.enabled=true时才启用
 */
@Api(tags = "MCP行程查询")
@RestController
@RequestMapping("/mcp")
@ConditionalOnProperty(name = "mcp.travel.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class McpTravelController {

    @Autowired
    private McpTravelToolService mcpTravelToolService;

    /**
     * MCP初始化握手端点
     * 处理客户端连接和协议协商
     */
    @ApiOperation(value = "MCP初始化", notes = "处理MCP客户端初始化握手")
    @PostMapping(value = "/initialize", produces = "application/json")
    public McpResponse initialize(@RequestBody McpRequest request) {
        String requestId = request != null ? request.getId() : null;
        log.info("MCP初始化请求开始: method={}, id={}", 
                request != null ? request.getMethod() : "null", requestId);
        
        // 请求基础验证
        if (request == null) {
            log.warn("MCP初始化请求为空 [requestId={}]", requestId);
            throw new McpInvalidRequestException("请求不能为空");
        }
        
        if (!"initialize".equals(request.getMethod())) {
            log.warn("MCP初始化方法错误: method={} [requestId={}]", request.getMethod(), requestId);
            throw new McpInvalidRequestException("方法名必须为 initialize");
        }
        
        try {
            // 解析初始化参数
            McpInitializeParams params = McpJsonUtil.parseInitializeParams(request.getParams());
            
            // 验证协议版本
            if (params.getProtocolVersion() == null || params.getProtocolVersion().trim().isEmpty()) {
                log.warn("MCP协议版本缺失 [requestId={}]", requestId);
                throw new McpInvalidRequestException("协议版本不能为空");
            }
            
            // 创建服务器信息
            McpServerInfo serverInfo = new McpServerInfo(
                McpConstants.SERVER_NAME,
                McpConstants.SERVER_VERSION
            );
            
            // 创建服务器能力声明
            Map<String, Object> capabilities = new HashMap<>();
            Map<String, Object> tools = new HashMap<>();
            tools.put("listChanged", false);
            capabilities.put("tools", tools);
            
            // 创建初始化结果
            McpInitializeResult result = new McpInitializeResult(
                McpConstants.MCP_PROTOCOL_VERSION,
                capabilities,
                serverInfo
            );
            
            String clientName = params.getClientInfo() != null ? params.getClientInfo().getName() : "unknown";
            log.info("MCP初始化成功: clientName={}, clientProtocolVersion={}, serverProtocolVersion={} [requestId={}]", 
                    clientName, params.getProtocolVersion(), McpConstants.MCP_PROTOCOL_VERSION, requestId);
            
            return McpResponse.success(requestId, result);
            
        } catch (McpException e) {
            // MCP异常直接抛出，由全局异常处理器处理
            throw e;
        } catch (Exception e) {
            log.error("MCP初始化系统异常 [requestId={}]", requestId, e);
            throw new McpParseErrorException("初始化参数解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 工具列表查询端点
     * 返回所有可用的MCP工具
     */
    @ApiOperation(value = "获取工具列表", notes = "返回所有可用的MCP工具")
    @PostMapping(value = "/tools/list", produces = "application/json")
    public McpResponse listTools(@RequestBody McpRequest request) {
        String requestId = request != null ? request.getId() : null;
        log.info("工具列表查询请求开始: method={}, id={}", 
                request != null ? request.getMethod() : "null", requestId);
        
        // 请求基础验证
        if (request == null) {
            log.warn("工具列表查询请求为空 [requestId={}]", requestId);
            throw new McpInvalidRequestException("请求不能为空");
        }
        
        if (!"tools/list".equals(request.getMethod())) {
            log.warn("工具列表查询方法错误: method={} [requestId={}]", request.getMethod(), requestId);
            throw new McpInvalidRequestException("方法名必须为 tools/list");
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            List<McpTool> tools = mcpTravelToolService.getAvailableTools();
            
            Map<String, Object> result = new HashMap<>();
            result.put("tools", tools);
            
            long duration = System.currentTimeMillis() - startTime;
            log.info("工具列表查询成功: 共{}个工具, 耗时{}ms [requestId={}]", 
                    tools.size(), duration, requestId);
            
            return McpResponse.success(requestId, result);
            
        } catch (Exception e) {
            log.error("获取工具列表系统异常 [requestId={}]", requestId, e);
            throw new McpInternalErrorException("获取工具列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 工具调用执行端点
     * 执行具体的工具调用
     */
    @ApiOperation(value = "调用工具", notes = "执行具体的工具调用")
    @PostMapping(value = "/tools/call", produces = "application/json")
    public McpResponse callTool(@RequestBody McpRequest request) {
        String requestId = request != null ? request.getId() : null;
        log.info("工具调用请求开始: method={}, id={}", 
                request != null ? request.getMethod() : "null", requestId);
        
        // 请求基础验证
        if (request == null) {
            log.warn("工具调用请求为空 [requestId={}]", requestId);
            throw new McpInvalidRequestException("请求不能为空");
        }
        
        if (!"tools/call".equals(request.getMethod())) {
            log.warn("工具调用方法错误: method={} [requestId={}]", request.getMethod(), requestId);
            throw new McpInvalidRequestException("方法名必须为 tools/call");
        }
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 解析工具调用参数
            Map<String, Object> params = request.getParams();
            if (params == null) {
                log.warn("工具调用参数缺失 [requestId={}]", requestId);
                throw new McpInvalidParamsException("缺少调用参数");
            }
            
            String toolName = (String) params.get("name");
            if (toolName == null || toolName.trim().isEmpty()) {
                log.warn("工具名称缺失 [requestId={}]", requestId);
                throw new McpInvalidParamsException("缺少工具名称");
            }
            
            @SuppressWarnings("unchecked")
            Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
            if (arguments == null) {
                arguments = new HashMap<>();
            }
            
            log.info("开始执行工具调用: name={}, arguments={} [requestId={}]", 
                    toolName, arguments, requestId);
            
            // 执行工具调用
            McpToolCallResult result = mcpTravelToolService.callTool(toolName, arguments);
            
            long duration = System.currentTimeMillis() - startTime;
            int contentCount = result.getContent() != null ? result.getContent().size() : 0;
            
            log.info("工具调用成功: name={}, contentCount={}, 耗时{}ms [requestId={}]", 
                    toolName, contentCount, duration, requestId);
            
            return McpResponse.success(requestId, result);
            
        } catch (McpException e) {
            // MCP异常直接抛出，由全局异常处理器处理
            throw e;
        } catch (ClassCastException e) {
            log.warn("工具调用参数类型错误 [requestId={}]", requestId, e);
            throw new McpInvalidParamsException("参数类型错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("工具调用系统异常 [requestId={}]", requestId, e);
            throw new McpInternalErrorException("工具调用失败: " + e.getMessage(), e);
        }
    }
}