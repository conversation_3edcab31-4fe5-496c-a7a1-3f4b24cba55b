# 设计文档

## 概述

海报生成功能是一个基于模板化设计的图文编辑系统，采用前后端分离架构。后端使用Spring Boot提供RESTful API服务，前端使用Vue.js + Ant Design构建用户界面。系统支持管理员创建海报模板，用户基于模板进行图文编辑，并能从行程数据中自动导入信息，最终生成高质量的海报图片。

## 架构

### 整体架构

```mermaid
graph TB
    A[前端Vue.js] --> B[后端Spring Boot]
    B --> C[MySQL数据库]
    B --> D[腾讯云COS存储]
    B --> E[图片处理服务]
    
    subgraph "前端模块"
        F[海报模板管理]
        G[海报编辑器]
        H[海报库管理]
    end
    
    subgraph "后端模块"
        I[模板管理API]
        J[海报生成API]
        K[文件上传API]
        L[图片处理服务]
    end
    
    A --> F
    A --> G
    A --> H
    F --> I
    G --> J
    H --> K
    J --> L
```

### 技术栈

**后端技术栈：**
- Spring Boot 2.3.12
- MyBatis Plus 3.4.3
- MySQL 5.7
- 腾讯云COS（文件存储）
- Java Graphics2D（图片处理）
- Thumbnailator（图片缩放）

**前端技术栈：**
- Vue.js 2.x
- Ant Design Vue
- Fabric.js（画布编辑）
- html2canvas（截图生成）

## 组件和接口

### 数据库设计

#### 海报模板表 (biz_poster_template)

```sql
CREATE TABLE biz_poster_template (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    template_desc VARCHAR(500) COMMENT '模板描述',
    template_data TEXT NOT NULL COMMENT '模板配置数据(JSON)',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    width INT NOT NULL DEFAULT 750 COMMENT '画布宽度',
    height INT NOT NULL DEFAULT 1334 COMMENT '画布高度',
    status TINYINT DEFAULT 1 COMMENT '状态 1-启用 0-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序',
    create_by INT COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_by INT COMMENT '更新人',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
);
```

#### 用户海报表 (biz_user_poster)

```sql
CREATE TABLE biz_user_poster (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    poster_name VARCHAR(100) NOT NULL COMMENT '海报名称',
    template_id BIGINT COMMENT '模板ID',
    poster_data TEXT NOT NULL COMMENT '海报配置数据(JSON)',
    poster_url VARCHAR(500) COMMENT '生成的海报图片URL',
    thumbnail_url VARCHAR(500) COMMENT '缩略图URL',
    width INT NOT NULL DEFAULT 750 COMMENT '画布宽度',
    height INT NOT NULL DEFAULT 1334 COMMENT '画布高度',
    create_by INT NOT NULL COMMENT '创建人',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_create_by (create_by),
    INDEX idx_template_id (template_id),
    INDEX idx_create_time (create_time)
);
```

### 后端API接口

#### 模板管理接口

```java
@RestController
@RequestMapping("/biz/posterTemplate")
@Api(tags = "海报模板管理")
public class BizPosterTemplateController {
    
    @GetMapping("/list")
    @ApiOperation("获取模板列表")
    public Result<IPage<BizPosterTemplate>> queryPageList(
        @RequestParam(defaultValue = "1") Integer pageNo,
        @RequestParam(defaultValue = "10") Integer pageSize,
        BizPosterTemplate posterTemplate);
    
    @PostMapping("/add")
    @ApiOperation("新增模板")
    public Result<String> add(@RequestBody BizPosterTemplate posterTemplate);
    
    @PutMapping("/edit")
    @ApiOperation("编辑模板")
    public Result<String> edit(@RequestBody BizPosterTemplate posterTemplate);
    
    @DeleteMapping("/delete")
    @ApiOperation("删除模板")
    public Result<String> delete(@RequestParam String ids);
    
    @GetMapping("/detail/{id}")
    @ApiOperation("获取模板详情")
    public Result<BizPosterTemplate> queryById(@PathVariable String id);
}
```

#### 海报生成接口

```java
@RestController
@RequestMapping("/biz/userPoster")
@Api(tags = "用户海报管理")
public class BizUserPosterController {
    
    @GetMapping("/list")
    @ApiOperation("获取用户海报列表")
    public Result<IPage<BizUserPoster>> queryPageList(
        @RequestParam(defaultValue = "1") Integer pageNo,
        @RequestParam(defaultValue = "10") Integer pageSize,
        BizUserPoster userPoster);
    
    @PostMapping("/save")
    @ApiOperation("保存海报")
    public Result<String> save(@RequestBody BizUserPoster userPoster);
    
    @PostMapping("/generate")
    @ApiOperation("生成海报图片")
    public Result<String> generatePoster(@RequestBody PosterGenerateRequest request);
    
    @GetMapping("/trip-data/{tripId}")
    @ApiOperation("获取行程数据用于填充")
    public Result<TripPosterData> getTripData(@PathVariable Long tripId);
    
    @DeleteMapping("/delete")
    @ApiOperation("删除海报")
    public Result<String> delete(@RequestParam String ids);
}
```

### 前端组件设计

#### 海报模板管理组件

```vue
<!-- BizPosterTemplateList.vue -->
<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="模板名称">
              <a-input placeholder="请输入模板名称" v-model="queryParam.templateName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="状态">
              <a-select placeholder="请选择状态" v-model="queryParam.status" allowClear>
                <a-select-option value="1">启用</a-select-option>
                <a-select-option value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a-button type="primary" @click="handleAdd" icon="plus" style="margin-left: 8px">新增模板</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 表格区域 -->
    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange">
      
      <template slot="thumbnail" slot-scope="text">
        <img :src="text" style="width: 60px; height: 80px; object-fit: cover;" v-if="text"/>
      </template>
      
      <template slot="action" slot-scope="text, record">
        <a @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical" />
        <a @click="handleCopy(record)">复制</a>
        <a-divider type="vertical" />
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a>删除</a>
        </a-popconfirm>
      </template>
    </a-table>
    
    <!-- 模板编辑弹窗 -->
    <poster-template-modal ref="modalForm" @ok="modalFormOk"></poster-template-modal>
  </a-card>
</template>
```

#### 海报编辑器组件

```vue
<!-- PosterEditor.vue -->
<template>
  <div class="poster-editor">
    <div class="editor-header">
      <a-row type="flex" justify="space-between" align="middle">
        <a-col>
          <a-input v-model="posterName" placeholder="请输入海报名称" style="width: 300px;"/>
        </a-col>
        <a-col>
          <a-button @click="importTripData" icon="import">导入行程数据</a-button>
          <a-button @click="savePoster" type="primary" icon="save" style="margin-left: 8px">保存</a-button>
          <a-button @click="generatePoster" type="primary" icon="picture" style="margin-left: 8px">生成海报</a-button>
        </a-col>
      </a-row>
    </div>
    
    <div class="editor-content">
      <div class="editor-sidebar">
        <!-- 组件面板 -->
        <a-tabs>
          <a-tab-pane key="elements" tab="元素">
            <div class="element-list">
              <div class="element-item" @click="addText">
                <a-icon type="font-size" />
                <span>文本</span>
              </div>
              <div class="element-item" @click="addImage">
                <a-icon type="picture" />
                <span>图片</span>
              </div>
              <div class="element-item" @click="addShape">
                <a-icon type="border" />
                <span>形状</span>
              </div>
            </div>
          </a-tab-pane>
          <a-tab-pane key="properties" tab="属性">
            <div class="property-panel" v-if="selectedObject">
              <!-- 动态属性编辑面板 -->
              <component :is="getPropertyComponent(selectedObject.type)" 
                         :object="selectedObject" 
                         @change="updateObject"/>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
      
      <div class="editor-canvas">
        <canvas ref="fabricCanvas" :width="canvasWidth" :height="canvasHeight"></canvas>
      </div>
    </div>
    
    <!-- 导入行程数据弹窗 -->
    <trip-import-modal ref="tripImportModal" @ok="onTripDataImported"></trip-import-modal>
  </div>
</template>
```

## 数据模型

### 模板数据结构

```typescript
interface PosterTemplate {
  id: number;
  templateName: string;
  templateDesc: string;
  templateData: {
    canvas: {
      width: number;
      height: number;
      backgroundColor: string;
    };
    objects: PosterObject[];
    variables: TemplateVariable[];
  };
  thumbnailUrl: string;
  width: number;
  height: number;
  status: number;
}

interface PosterObject {
  id: string;
  type: 'text' | 'image' | 'shape';
  left: number;
  top: number;
  width: number;
  height: number;
  angle: number;
  scaleX: number;
  scaleY: number;
  // 类型特定属性
  [key: string]: any;
}

interface TemplateVariable {
  key: string;
  name: string;
  type: 'text' | 'image';
  defaultValue: string;
  description: string;
}
```

### 行程数据映射

```typescript
interface TripPosterData {
  tripName: string;
  tripFullName: string;
  dayNum: number;
  price: number;
  imgUrl: string;
  advantageDesc: string;
  tripTag: string;
  // 其他可用于海报的字段
}

interface VariableMapping {
  [templateVariable: string]: keyof TripPosterData;
}
```

## 错误处理

### 后端异常处理

```java
@RestControllerAdvice(basePackages = "com.woyaotuanjian.modules.biz.controller.poster")
@Slf4j
public class PosterExceptionHandler {
    
    @ExceptionHandler(PosterTemplateNotFoundException.class)
    public Result handleTemplateNotFound(PosterTemplateNotFoundException e) {
        log.error("海报模板未找到: {}", e.getMessage());
        return Result.error("海报模板不存在");
    }
    
    @ExceptionHandler(PosterGenerationException.class)
    public Result handlePosterGeneration(PosterGenerationException e) {
        log.error("海报生成失败: {}", e.getMessage(), e);
        return Result.error("海报生成失败，请稍后重试");
    }
    
    @ExceptionHandler(InvalidPosterDataException.class)
    public Result handleInvalidData(InvalidPosterDataException e) {
        log.error("海报数据无效: {}", e.getMessage());
        return Result.error("海报数据格式错误");
    }
}
```

### 前端错误处理

```javascript
// 统一错误处理
const handleApiError = (error) => {
  if (error.response) {
    const { status, data } = error.response;
    switch (status) {
      case 400:
        this.$message.error(data.message || '请求参数错误');
        break;
      case 404:
        this.$message.error('资源不存在');
        break;
      case 500:
        this.$message.error('服务器内部错误');
        break;
      default:
        this.$message.error('网络错误，请稍后重试');
    }
  } else {
    this.$message.error('网络连接失败');
  }
};
```

## 测试策略

### 单元测试

**后端测试：**
- 模板CRUD操作测试
- 海报生成逻辑测试
- 图片处理功能测试
- 数据验证测试

**前端测试：**
- 组件渲染测试
- 用户交互测试
- 数据流测试
- 画布操作测试

### 集成测试

- API接口集成测试
- 文件上传下载测试
- 数据库操作测试
- 第三方服务集成测试

### 性能测试

- 大图片处理性能测试
- 并发海报生成测试
- 前端画布渲染性能测试
- 内存使用监控

### 用户验收测试

- 模板创建和编辑流程测试
- 海报制作完整流程测试
- 不同浏览器兼容性测试
- 移动端响应式测试

## 安全考虑

### 文件上传安全

- 文件类型验证（仅允许图片格式）
- 文件大小限制（单个文件不超过10MB）
- 文件内容检查（防止恶意文件）
- 上传频率限制

### 数据安全

- 用户权限验证（只能操作自己的海报）
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

### API安全

- 接口访问频率限制
- 参数验证和过滤
- 敏感操作日志记录
- 错误信息脱敏

## 性能优化

### 后端优化

- 图片处理异步化
- 缓存常用模板数据
- 数据库查询优化
- 文件存储CDN加速

### 前端优化

- 组件懒加载
- 图片预加载和缓存
- 画布操作防抖
- 虚拟滚动优化

### 系统优化

- 数据库索引优化
- 静态资源压缩
- 接口响应缓存
- 监控和告警机制