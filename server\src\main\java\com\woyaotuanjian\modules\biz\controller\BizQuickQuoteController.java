package com.woyaotuanjian.modules.biz.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.common.system.vo.LoginUser;

import com.woyaotuanjian.modules.biz.entity.BizQuickQuote;
import com.woyaotuanjian.modules.biz.entity.BizTrip;
import com.woyaotuanjian.modules.biz.entity.pojo.BizTripExt;
import com.woyaotuanjian.modules.biz.service.IBizQuickQuoteService;
import com.woyaotuanjian.modules.biz.service.IBizTripService;
import com.woyaotuanjian.modules.biz.service.IBizCompanyService;
import com.woyaotuanjian.modules.biz.util.YdUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

// 文档处理相关导入
import com.aspose.words.Document;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

/**
 * @Description: 快捷报价
 * @Author: System
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Api(tags="快捷报价")
@RestController
@RequestMapping("/biz/bizQuickQuote")
@Slf4j
public class BizQuickQuoteController {
    
    @Autowired
    private IBizQuickQuoteService bizQuickQuoteService;
    
    @Autowired
    private IBizTripService bizTripService;
    
    @Autowired
    private IBizCompanyService companyService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "快捷报价-分页列表查询")
    @ApiOperation(value="快捷报价-分页列表查询", notes="快捷报价-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(BizQuickQuote bizQuickQuote,
                                 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                 HttpServletRequest req) {
        QueryWrapper<BizQuickQuote> queryWrapper = new QueryWrapper<BizQuickQuote>();
        
        // 获取当前用户，只查询当前用户创建的报价
        LoginUser currentUser = SysUserUtil.getCurrentUser();
        if (currentUser != null) {
            queryWrapper.eq("create_by", currentUser.getUsername());
        }
        
        // 添加查询条件
        if (bizQuickQuote.getQuoteName() != null && !bizQuickQuote.getQuoteName().isEmpty()) {
            queryWrapper.like("quote_name", bizQuickQuote.getQuoteName());
        }
        if (bizQuickQuote.getSourceType() != null && !bizQuickQuote.getSourceType().isEmpty()) {
            queryWrapper.eq("source_type", bizQuickQuote.getSourceType());
        }
        
        queryWrapper.orderByDesc("create_time");
        
        Page<BizQuickQuote> page = new Page<BizQuickQuote>(pageNo, pageSize);
        IPage<BizQuickQuote> pageList = bizQuickQuoteService.page(page, queryWrapper);
        
        return Result.ok(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "快捷报价-添加")
    @ApiOperation(value="快捷报价-添加", notes="快捷报价-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BizQuickQuote bizQuickQuote) {
        bizQuickQuoteService.save(bizQuickQuote);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "快捷报价-编辑")
    @ApiOperation(value="快捷报价-编辑", notes="快捷报价-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody BizQuickQuote bizQuickQuote) {
        // 检查权限：只能编辑自己创建的报价
        LoginUser currentUser = SysUserUtil.getCurrentUser();
        if (currentUser != null && bizQuickQuote.getId() != null) {
            BizQuickQuote existingQuote = bizQuickQuoteService.getById(bizQuickQuote.getId());
            if (existingQuote == null) {
                return Result.error("报价不存在");
            }
            if (!currentUser.getUsername().equals(existingQuote.getCreateBy())) {
                return Result.error("无权限编辑此报价");
            }
        }
        
        bizQuickQuoteService.updateById(bizQuickQuote);
        return Result.ok("编辑成功!");
    }

    /**
     * 复制快捷报价
     */
    @AutoLog(value = "快捷报价-复制")
    @ApiOperation(value="快捷报价-复制", notes="快捷报价-复制")
    @PostMapping(value = "/copy")
    public Result<?> copy(@RequestBody BizQuickQuote bizQuickQuote) {
        try {
            BizQuickQuote original = bizQuickQuoteService.getById(bizQuickQuote.getId());
            if (original == null) {
                return Result.error("原始报价不存在");
            }
            
            // 检查权限：只能复制自己创建的报价
            LoginUser currentUser = SysUserUtil.getCurrentUser();
            if (currentUser != null && !currentUser.getUsername().equals(original.getCreateBy())) {
                return Result.error("无权限复制此报价");
            }
            
            BizQuickQuote copy = bizQuickQuoteService.copyQuickQuote(original);
            return Result.ok(copy);
        } catch (Exception e) {
            log.error("复制报价失败", e);
            return Result.error("复制失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "快捷报价-通过id删除")
    @ApiOperation(value="快捷报价-通过id删除", notes="快捷报价-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        // 检查权限：只能删除自己创建的报价
        LoginUser currentUser = SysUserUtil.getCurrentUser();
        if (currentUser != null) {
            BizQuickQuote existingQuote = bizQuickQuoteService.getById(id);
            if (existingQuote == null) {
                return Result.error("报价不存在");
            }
            if (!currentUser.getUsername().equals(existingQuote.getCreateBy())) {
                return Result.error("无权限删除此报价");
            }
        }
        
        bizQuickQuoteService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "快捷报价-批量删除")
    @ApiOperation(value="快捷报价-批量删除", notes="快捷报价-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.bizQuickQuoteService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "快捷报价-通过id查询")
    @ApiOperation(value="快捷报价-通过id查询", notes="快捷报价-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        BizQuickQuote bizQuickQuote = bizQuickQuoteService.getById(id);
        if(bizQuickQuote==null) {
            return Result.error("未找到对应数据");
        }
        
        // 检查权限：只能查看自己创建的报价
        LoginUser currentUser = SysUserUtil.getCurrentUser();
        if (currentUser != null && !currentUser.getUsername().equals(bizQuickQuote.getCreateBy())) {
            return Result.error("无权限查看此报价");
        }
        
        return Result.ok(bizQuickQuote);
    }

    /**
     * 解析粘贴内容
     */
    @AutoLog(value = "快捷报价-解析粘贴内容")
    @ApiOperation(value="快捷报价-解析粘贴内容", notes="快捷报价-解析粘贴内容")
    @PostMapping(value = "/parsePastedContent")
    public Result<?> parsePastedContent(@RequestBody JSONObject request) {
        try {
            String content = request.getString("content");
            if (content == null || content.trim().isEmpty()) {
                return Result.error("请提供要解析的内容");
            }
            
            JSONObject result = bizQuickQuoteService.parsePastedContent(content);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("解析粘贴内容失败", e);
            return Result.error("解析失败: " + e.getMessage());
        }
    }

    /**
     * 从现有行程导入
     */
    @AutoLog(value = "快捷报价-从现有行程导入")
    @ApiOperation(value="快捷报价-从现有行程导入", notes="快捷报价-从现有行程导入")
    @PostMapping(value = "/importFromTrip")
    public Result<?> importFromTrip(@RequestBody JSONObject request) {
        try {
            Long tripId = request.getLong("tripId");
            if (tripId == null) {
                return Result.error("请提供行程ID");
            }
            
            JSONObject result = bizQuickQuoteService.importFromTrip(tripId);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("从行程导入失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 从文件导入
     */
    @AutoLog(value = "快捷报价-从文件导入")
    @ApiOperation(value="快捷报价-从文件导入", notes="快捷报价-从文件导入")
    @PostMapping(value = "/importFromFile")
    public Result<?> importFromFile(HttpServletRequest request) {
        try {
            MultipartFile file = null;
            if (request instanceof MultipartHttpServletRequest) {
                MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
                file = multipartRequest.getFile("file");
            }
            
            if (file == null) {
                return Result.error("请提供文件");
            }
            
            // 检查文件类型
            String fileName = file.getOriginalFilename();
            if (fileName == null || !(fileName.toLowerCase().endsWith(".docx") || fileName.toLowerCase().endsWith(".pdf"))) {
                return Result.error("目前仅支持.docx和.pdf格式文件");
            }
            
            // 提取文件内容
            String fileContent = extractFileContent(file);
            
            // 使用服务处理文件内容
            JSONObject result = bizQuickQuoteService.importFromFile(fileContent);
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("从文件导入失败", e);
            return Result.error("导入失败: " + e.getMessage());
        }
    }
    
    /**
     * 提取文件内容的辅助方法（与BizTripController相同的逻辑）
     */
    private String extractFileContent(MultipartFile file) throws Exception {
        File tempFile = File.createTempFile("upload_", "_" + file.getOriginalFilename());
        try {
            file.transferTo(tempFile);
            
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new IllegalArgumentException("文件名不能为空");
            }
            
            if (fileName.toLowerCase().endsWith(".pdf")) {
                // 使用PDFBox提取PDF文本
                try (PDDocument document = PDDocument.load(tempFile)) {
                    PDFTextStripper textStripper = new PDFTextStripper();
                    String text = textStripper.getText(document);
                    // 确保文档完全关闭
                    document.close();
                    return text;
                }
            } else {
                // 使用Aspose.Words提取docx文本
                try (java.io.InputStream inputStream = new java.io.FileInputStream(tempFile)) {
                    Document doc = new Document(tempFile.getAbsolutePath());
                    String text = doc.getText();
                    return text;
                }
            }
        } finally {
            // 确保删除临时文件
            if (tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 搜索行程列表（用于导入选择）
     */
    @AutoLog(value = "快捷报价-搜索行程列表")
    @ApiOperation(value="快捷报价-搜索行程列表", notes="快捷报价-搜索行程列表")
    @GetMapping(value = "/searchTrips")
    public Result<?> searchTrips(@RequestParam(name="keyword", required=false) String keyword,
                                @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                @RequestParam(name="onlyCheckedCom", defaultValue="1") Integer onlyCheckedCom,
                                HttpServletRequest req) {
        try {
            Map<String, Object> param = YdUtil.sqlMap(req.getParameterMap());
            LoginUser sysUser = SysUserUtil.getCurrentUser();

            // 应用权限过滤
            companyService.tripListFilter(param, sysUser, onlyCheckedCom);

            // 我的数据优先
            param.put("currentUserId", sysUser.getId());

            // 如果有搜索关键词，进行处理
            if (keyword != null && !keyword.trim().isEmpty()) {
                String searchValue = keyword.trim();
                // 如果输入内容是1-2位大写字母+1位以上数字，当作搜索id处理，去除掉字母
                if (searchValue.matches("^[A-Z]{1,5}\\d+$")) {
                    searchValue = searchValue.replaceAll("[A-Z]+", "");
                }
                // 如果仅仅是ID搜索，不再join多表
                if (searchValue.matches("^[A-Z]{0,5}\\d+$")) {
                    param.put("tripIDSearch", 1);
                }
                param.put("searchValue", searchValue);
            }
            // 使用与BizTripController相同的查询方法
            IPage<BizTripExt> pageList = bizTripService.getBizTripExtList(new Page(pageNo, pageSize), param);
            
            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("搜索行程列表失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取最新的行程列表（用于导入选择默认显示）
     */
    @AutoLog(value = "快捷报价-获取最新行程列表")
    @ApiOperation(value="快捷报价-获取最新行程列表", notes="快捷报价-获取最新行程列表")
    @GetMapping(value = "/getRecentTrips")
    public Result<?> getRecentTrips(@RequestParam(name="onlyCheckedCom", defaultValue="1") Integer onlyCheckedCom,
                                   HttpServletRequest req) {
        try {
            Map<String, Object> param = YdUtil.sqlMap(req.getParameterMap());
            LoginUser sysUser = SysUserUtil.getCurrentUser();

            // 应用权限过滤
            companyService.tripListFilter(param, sysUser, onlyCheckedCom);

            // 我的数据优先
            param.put("currentUserId", sysUser.getId());

            // 获取最新的5个行程
            IPage<BizTripExt> pageList = bizTripService.getBizTripExtList(new Page(1, 5), param);
            
            return Result.ok(pageList.getRecords());
        } catch (Exception e) {
            log.error("获取最新行程列表失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 搜索景点列表（用于报价项目选择）
     */
    @AutoLog(value = "快捷报价-搜索景点列表")
    @ApiOperation(value="快捷报价-搜索景点列表", notes="快捷报价-搜索景点列表")
    @GetMapping(value = "/searchScenicList")
    public Result<?> searchScenicList(@RequestParam(name="keyword", required=false) String keyword,
                                     @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                     @RequestParam(name="pageSize", defaultValue="20") Integer pageSize) {
        try {
            JSONObject result = bizQuickQuoteService.searchScenicList(keyword, pageNo, pageSize);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("搜索景点列表失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 搜索酒店列表（用于报价项目选择）
     */
    @AutoLog(value = "快捷报价-搜索酒店列表")
    @ApiOperation(value="快捷报价-搜索酒店列表", notes="快捷报价-搜索酒店列表")
    @GetMapping(value = "/searchHotelList")
    public Result<?> searchHotelList(@RequestParam(name="keyword", required=false) String keyword,
                                    @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                    @RequestParam(name="pageSize", defaultValue="20") Integer pageSize) {
        try {
            JSONObject result = bizQuickQuoteService.searchHotelList(keyword, pageNo, pageSize);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("搜索酒店列表失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }



} 