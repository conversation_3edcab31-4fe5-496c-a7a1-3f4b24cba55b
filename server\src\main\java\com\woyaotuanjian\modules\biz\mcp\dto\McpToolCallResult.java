package com.woyaotuanjian.modules.biz.mcp.dto;

import lombok.Data;

import java.util.List;

/**
 * MCP工具调用结果
 */
@Data
public class McpToolCallResult {
    
    /**
     * 内容列表
     */
    private List<McpContent> content;
    
    /**
     * 是否为错误
     */
    private Boolean isError;
    
    public McpToolCallResult(List<McpContent> content) {
        this.content = content;
        this.isError = false;
    }
    
    public McpToolCallResult(List<McpContent> content, Boolean isError) {
        this.content = content;
        this.isError = isError;
    }
}