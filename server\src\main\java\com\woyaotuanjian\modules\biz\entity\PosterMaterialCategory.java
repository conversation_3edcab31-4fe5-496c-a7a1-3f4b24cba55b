package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 海报素材分类
 * @Author: system
 * @Date: 2024-08-16
 * @Version: V1.0
 */
@Data
@TableName("poster_material_category")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="poster_material_category对象", description="海报素材分类")
public class PosterMaterialCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**分类名称*/
    @ApiModelProperty(value = "分类名称")
    private String name;
    
    /**父分类ID，0为顶级分类*/
    @ApiModelProperty(value = "父分类ID，0为顶级分类")
    private Long parentId;
    
    /**分类图标*/
    @ApiModelProperty(value = "分类图标")
    private String icon;
    
    /**排序*/
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;
    
    /**状态：1启用 0禁用*/
    @ApiModelProperty(value = "状态：1启用 0禁用")
    private Integer status;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}