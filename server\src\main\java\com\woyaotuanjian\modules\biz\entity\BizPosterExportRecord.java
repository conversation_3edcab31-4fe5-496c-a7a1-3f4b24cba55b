package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 海报导出记录
 * @Author: jeecg-boot
 * @Date: 2025-01-13
 * @Version: V1.0
 */
@Data
@TableName("biz_poster_export_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BizPosterExportRecord", description = "海报导出记录")
public class BizPosterExportRecord implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    @ApiModelProperty(value = "海报ID")
    private Long posterId;
    
    @ApiModelProperty(value = "海报名称")
    private String posterName;
    
    @ApiModelProperty(value = "导出文件名")
    private String fileName;
    
    @ApiModelProperty(value = "文件URL")
    private String fileUrl;
    
    @ApiModelProperty(value = "导出格式")
    private String exportFormat;
    
    @ApiModelProperty(value = "图片质量")
    private Double exportQuality;
    
    @ApiModelProperty(value = "文件大小(KB)")
    private Integer fileSize;
    
    @ApiModelProperty(value = "导出宽度")
    private Integer exportWidth;
    
    @ApiModelProperty(value = "导出高度")
    private Integer exportHeight;
    
    @ApiModelProperty(value = "分辨率倍数")
    private Double multiplier;
    
    @ApiModelProperty(value = "是否高分辨率")
    private Boolean highResolution;
    
    @ApiModelProperty(value = "是否包含背景")
    private Boolean withBackground;
    
    @ApiModelProperty(value = "导出耗时(毫秒)")
    private Long exportDuration;
    
    @ApiModelProperty(value = "导出状态: 1-成功 0-失败")
    private Integer status;
    
    @ApiModelProperty(value = "错误信息")
    private String errorMessage;
    
    @ApiModelProperty(value = "IP地址")
    private String ipAddress;
    
    @ApiModelProperty(value = "用户代理")
    private String userAgent;
    
    @ApiModelProperty(value = "导出备注")
    private String remark;
    
    @ApiModelProperty(value = "创建人")
    private Integer createBy;
    
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}