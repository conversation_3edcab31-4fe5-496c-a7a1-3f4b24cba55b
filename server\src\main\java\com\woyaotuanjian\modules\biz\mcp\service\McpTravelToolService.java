package com.woyaotuanjian.modules.biz.mcp.service;

import com.woyaotuanjian.modules.biz.entity.BizTrip;
import com.woyaotuanjian.modules.biz.mcp.dto.*;
import com.woyaotuanjian.modules.biz.mcp.exception.McpException;
import com.woyaotuanjian.modules.biz.mcp.exception.McpInternalErrorException;
import com.woyaotuanjian.modules.biz.mcp.exception.McpInvalidParamsException;
import com.woyaotuanjian.modules.biz.mcp.exception.McpMethodNotFoundException;
import com.woyaotuanjian.modules.biz.mcp.util.McpConstants;
import com.woyaotuanjian.modules.biz.mcp.util.TripConverter;
import com.woyaotuanjian.modules.biz.service.IBizTripService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * MCP行程工具服务
 * 封装具体的行程查询业务逻辑
 */
@Service
@Slf4j
public class McpTravelToolService {

    @Autowired
    private IBizTripService bizTripService;

    /**
     * 获取所有可用的MCP工具
     */
    public List<McpTool> getAvailableTools() {
        log.debug("开始获取可用工具列表");
        
        try {
            List<McpTool> tools = new ArrayList<>();
            
            // 创建行程详情查询工具
            McpTool getTripDetailTool = createGetTripDetailTool();
            tools.add(getTripDetailTool);
            
            log.debug("成功创建{}个工具", tools.size());
            return tools;
            
        } catch (Exception e) {
            log.error("创建工具列表失败", e);
            throw new McpInternalErrorException("创建工具列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行工具调用
     */
    public McpToolCallResult callTool(String toolName, Map<String, Object> arguments) {
        log.info("开始执行工具调用: toolName={}, argumentsSize={}", 
                toolName, arguments != null ? arguments.size() : 0);
        
        // 参数基础验证
        if (toolName == null || toolName.trim().isEmpty()) {
            log.warn("工具名称为空");
            throw new McpInvalidParamsException("工具名称不能为空");
        }
        
        if (arguments == null) {
            log.debug("工具调用参数为空，使用默认空参数");
            arguments = new HashMap<>();
        }
        
        try {
            switch (toolName.trim()) {
                case McpConstants.TOOL_GET_TRIP_DETAIL:
                    return callGetTripDetail(arguments);
                default:
                    log.warn("请求了未知的工具: {}", toolName);
                    throw new McpMethodNotFoundException("未知的工具: " + toolName);
            }
        } catch (McpException e) {
            // MCP异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("工具调用执行异常: toolName={}", toolName, e);
            throw new McpInternalErrorException("工具执行失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建行程详情查询工具定义
     */
    private McpTool createGetTripDetailTool() {
        log.debug("创建行程详情查询工具定义");
        
        try {
            // 创建参数schema
            Map<String, McpToolProperty> properties = new HashMap<>();
            
            // tripId参数定义
            McpToolProperty tripIdProperty = new McpToolProperty(
                McpConstants.SCHEMA_TYPE_INTEGER,
                "行程ID，必须是大于0的整数"
            );
            properties.put("tripId", tripIdProperty);
            
            McpToolInputSchema inputSchema = new McpToolInputSchema(
                McpConstants.SCHEMA_TYPE_OBJECT,
                properties,
                Arrays.asList("tripId")
            );
            
            McpTool tool = new McpTool(
                McpConstants.TOOL_GET_TRIP_DETAIL,
                "获取指定行程的详细信息，包括名称、价格、天数、行程介绍、特色简介、日程安排等。参数tripId必须是有效的行程ID。",
                inputSchema
            );
            
            log.debug("成功创建行程详情查询工具: name={}", tool.getName());
            return tool;
            
        } catch (Exception e) {
            log.error("创建行程详情查询工具失败", e);
            throw new McpInternalErrorException("创建工具定义失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行行程详情查询
     */
    private McpToolCallResult callGetTripDetail(Map<String, Object> arguments) {
        log.debug("开始执行行程详情查询: arguments={}", arguments);
        
        // 参数验证
        Object tripIdObj = arguments.get("tripId");
        if (tripIdObj == null) {
            log.warn("行程详情查询缺少tripId参数");
            throw new McpInvalidParamsException("缺少必需参数: tripId");
        }
        
        Long tripId;
        try {
            if (tripIdObj instanceof Number) {
                tripId = ((Number) tripIdObj).longValue();
            } else if (tripIdObj instanceof String) {
                String tripIdStr = ((String) tripIdObj).trim();
                if (tripIdStr.isEmpty()) {
                    throw new McpInvalidParamsException("参数tripId不能为空字符串");
                }
                tripId = Long.parseLong(tripIdStr);
            } else {
                log.warn("参数tripId类型错误: type={}, value={}", tripIdObj.getClass().getSimpleName(), tripIdObj);
                throw new McpInvalidParamsException("参数tripId必须是数字类型，当前类型: " + tripIdObj.getClass().getSimpleName());
            }
        } catch (NumberFormatException e) {
            log.warn("参数tripId格式无效: value={}", tripIdObj);
            throw new McpInvalidParamsException("参数tripId格式无效: " + tripIdObj);
        }
        
        if (tripId <= 0) {
            log.warn("参数tripId值无效: tripId={}", tripId);
            throw new McpInvalidParamsException("参数tripId必须大于0，当前值: " + tripId);
        }
        
        log.info("开始查询行程详情: tripId={}", tripId);
        
        try {
            // 查询行程数据
            BizTrip bizTrip = bizTripService.getById(tripId);
            if (bizTrip == null) {
                log.warn("行程不存在: tripId={}", tripId);
                // 返回空结果而不是错误
                return createEmptyResult("行程不存在 (ID: " + tripId + ")");
            }
            
            // 转换为MCP响应格式
            TripDetailResult tripDetail = TripConverter.toTripDetailResult(bizTrip);
            
            // 验证转换结果
            if (tripDetail == null) {
                log.error("行程数据转换失败: tripId={}", tripId);
                throw new McpInternalErrorException("行程数据转换失败");
            }
            
            // 创建内容对象
            String formattedText = formatTripDetailText(tripDetail);
            McpContent content = McpContent.text(formattedText);
            
            // 创建结果对象
            McpToolCallResult result = new McpToolCallResult(Arrays.asList(content), false);
            
            log.info("行程详情查询成功: tripId={}, tripName={}, contentLength={}", 
                    tripId, tripDetail.getTripName(), formattedText.length());
            
            return result;
            
        } catch (McpException e) {
            // MCP异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("查询行程详情时发生异常: tripId={}", tripId, e);
            throw new McpInternalErrorException("查询行程详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建空结果
     */
    private McpToolCallResult createEmptyResult(String message) {
        try {
            if (message == null || message.trim().isEmpty()) {
                message = "未找到相关数据";
            }
            
            McpContent content = McpContent.text(message);
            McpToolCallResult result = new McpToolCallResult(Arrays.asList(content), false);
            
            log.debug("创建空结果: message={}", message);
            return result;
            
        } catch (Exception e) {
            log.error("创建空结果时发生异常", e);
            // 返回最基本的错误结果
            McpContent errorContent = McpContent.text("系统错误：无法创建响应结果");
            return new McpToolCallResult(Arrays.asList(errorContent), false);
        }
    }

    /**
     * 格式化行程详情为文本
     */
    private String formatTripDetailText(TripDetailResult tripDetail) {
        if (tripDetail == null) {
            log.warn("行程详情对象为空，无法格式化");
            return "行程详情不可用";
        }
        
        try {
            StringBuilder sb = new StringBuilder();
            
            sb.append("=== 行程详情 ===\n");
            sb.append("行程ID: ").append(tripDetail.getId() != null ? tripDetail.getId() : "未知").append("\n");
            sb.append("行程名称: ").append(tripDetail.getTripName() != null ? tripDetail.getTripName() : "未设置").append("\n");
            
            if (tripDetail.getTripFullName() != null && !tripDetail.getTripFullName().trim().isEmpty() 
                && !tripDetail.getTripFullName().equals(tripDetail.getTripName())) {
                sb.append("完整名称: ").append(tripDetail.getTripFullName()).append("\n");
            }
            
            if (tripDetail.getPrice() != null) {
                sb.append("价格: ¥").append(tripDetail.getPrice()).append("\n");
            }
            
            if (tripDetail.getDayNum() != null && tripDetail.getDayNum() > 0) {
                sb.append("天数: ").append(tripDetail.getDayNum()).append("天\n");
            }
            
            if (isNotEmpty(tripDetail.getAdvantageDesc())) {
                sb.append("\n=== 特色简介 ===\n");
                sb.append(tripDetail.getAdvantageDesc().trim()).append("\n");
            }
            
            if (isNotEmpty(tripDetail.getTripDesc())) {
                sb.append("\n=== 行程介绍 ===\n");
                sb.append(tripDetail.getTripDesc().trim()).append("\n");
            }
            
            if (isNotEmpty(tripDetail.getSchedule())) {
                sb.append("\n=== 日程安排 ===\n");
                sb.append(tripDetail.getSchedule().trim()).append("\n");
            }
            
            if (isNotEmpty(tripDetail.getPriceRemark())) {
                sb.append("\n=== 价格说明 ===\n");
                sb.append(tripDetail.getPriceRemark().trim()).append("\n");
            }
            
            if (isNotEmpty(tripDetail.getTripTip())) {
                sb.append("\n=== 行程提示 ===\n");
                sb.append(tripDetail.getTripTip().trim()).append("\n");
            }
            
            if (isNotEmpty(tripDetail.getTripTag())) {
                sb.append("\n=== 行程标签 ===\n");
                sb.append(tripDetail.getTripTag().trim()).append("\n");
            }
            
            String result = sb.toString();
            log.debug("行程详情格式化完成: length={}", result.length());
            return result;
            
        } catch (Exception e) {
            log.error("格式化行程详情时发生异常: tripId={}", tripDetail.getId(), e);
            return "行程详情格式化失败: " + e.getMessage();
        }
    }
    
    /**
     * 检查字符串是否非空且非空白
     */
    private boolean isNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }
}