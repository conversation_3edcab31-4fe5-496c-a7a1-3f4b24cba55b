<template>
  <a-modal
    :title="title"
    :width="1000"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭"
    okText="确定"
    :bodyStyle="{ padding: '24px' }"
    :zIndex="1000"
    :maskClosable="false"
  >
    <a-spin :spinning="confirmLoading">
      <a-form :form="form" :labelCol="{ span: 4 }" :wrapperCol="{ span: 20 }">
        <!-- 标题 -->
        <a-form-item label="标题">
          <a-input
            placeholder="请输入新闻标题"
            v-decorator="['title', validatorRules.title]"
          />
        </a-form-item>
        
        <!-- 类型 -->
        <a-form-item label="类型">
          <a-select
            placeholder="请选择类型"
            v-decorator="['type', validatorRules.type]"
            style="width: 200px"
          >
            <a-select-option value="notice">通知公告</a-select-option>
            <a-select-option value="version">版本更新</a-select-option>
          </a-select>
        </a-form-item>
        
        <!-- 状态 -->
        <a-form-item label="状态">
          <a-select
            placeholder="请选择状态"
            v-decorator="['status', { ...validatorRules.status, initialValue: 1 }]"
            style="width: 200px"
          >
            <a-select-option :value="1">启用</a-select-option>
            <a-select-option :value="0">禁用</a-select-option>
          </a-select>
        </a-form-item>
        
        <!-- 置顶 -->
        <a-form-item label="置顶">
          <a-switch
            v-decorator="['isTop', { valuePropName: 'checked' }]"
            checkedChildren="是"
            unCheckedChildren="否"
          />
        </a-form-item>
        
        <!-- 可见角色 -->
        <a-form-item label="可见角色">
          <a-checkbox-group
            v-decorator="['visibleRoles']"
            :options="roleOptions"
          />
        </a-form-item>
        
        <!-- 内容 -->
        <a-form-item label="内容">
          <div class="editor-container">
            <Editor
              :key="editorKey"
              v-model="model.content"
              :init="editorConfig"
              :disabled="false"
              api-key="no-api-key"
            />
          </div>
          <div v-if="contentError" class="error-text">{{ contentError }}</div>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { httpAction, getAction } from '@/api/manage';
import { validateDuplicateValue } from '@/utils/util';
import Editor from '@tinymce/tinymce-vue';
import { getTinymceConfig } from '@/config/tinymce.config';

export default {
  name: 'SysNewsModal',
  components: {
    Editor
  },
  data() {
    return {
      title: '操作',
      visible: false,
      model: {},
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      form: this.$form.createForm(this),
      roleOptions: [],
      editorKey: Date.now(),
      contentError: '',
      validatorRules: {
        title: {
          rules: [
            { required: true, message: '请输入标题!' },
            { min: 1, max: 100, message: '标题长度在1-100个字符之间!' }
          ]
        },
        type: {
          rules: [{ required: true, message: '请选择类型!' }]
        },
        status: {
          rules: [{ required: true, message: '请选择状态!' }]
        },
        content: {
          rules: [{ required: true, message: '请输入内容!' }]
        }
      },
      url: {
        add: '/sys/news/add',
        edit: '/sys/news/edit',
        getRoleList: '/sys/news/getRoleList'
      },
      editorConfig: getTinymceConfig({
        height: 400,
        toolbar:
          'undo redo | formatselect | bold italic underline | ' +
          'alignleft aligncenter alignright | ' +
          'bullist numlist | image | removeformat',
        images_upload_url: (window._CONFIG['domianURL'] || '') + '/file/tinymce/upload',
        images_upload_credentials: true,
        setup: (editor) => {
          editor.on('init', () => {
            console.log('TinyMCE 编辑器初始化完成');
          });
        }
      })
    };
  },
  created() {
    this.loadRoleList();
  },
  mounted() {
    // 确保TinyMCE在模态框中正确工作
    this.$nextTick(() => {
      // 检查TinyMCE是否已加载
      if (typeof window.tinymce !== 'undefined') {
        console.log('TinyMCE 已加载');
      } else {
        console.warn('TinyMCE 未加载');
      }
    });
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 模态框打开时，确保编辑器能正确显示
        this.$nextTick(() => {
          setTimeout(() => {
            console.log('模态框已打开，TinyMCE 应该可以正常显示');
            // 尝试手动触发编辑器的重新渲染
            if (window.tinymce && window.tinymce.editors) {
              window.tinymce.editors.forEach(editor => {
                if (editor && editor.getContainer) {
                  const container = editor.getContainer();
                  if (container) {
                    // 确保工具栏可见
                    const toolbar = container.querySelector('.tox-toolbar');
                    if (toolbar) {
                      toolbar.style.display = 'block';
                      toolbar.style.visibility = 'visible';
                    }
                  }
                }
              });
            }
          }, 300);
        });
      }
    },
    'model.content'(newVal) {
      if (newVal && newVal.trim()) {
        this.contentError = '';
      }
    }
  },
  methods: {
    add() {
      this.model = {
        content: ''
      };
      this.visible = true;
      this.title = '新增';
      this.editorKey = Date.now(); // 重新生成key，强制重新渲染编辑器
      this.contentError = '';
      
      this.$nextTick(() => {
        this.form.resetFields();
        // 默认选中所有角色
        const allRoles = this.roleOptions.map(role => role.value);
        this.form.setFieldsValue({
          visibleRoles: allRoles
        });
      });
    },
    edit(record) {
      this.model = Object.assign({ content: '' }, record);
      this.visible = true;
      this.title = record.id ? '编辑' : '新增';
      this.editorKey = Date.now(); // 重新生成key，强制重新渲染编辑器
      this.contentError = '';
      
      this.$nextTick(() => {
        this.form.resetFields();
        
        if (record.id) {
          // 设置表单值
          let formData = Object.assign({}, this.model);
          
          // 处理置顶字段
          formData.isTop = formData.isTop === 1;
          
          // 处理可见角色
          if (formData.visibleRoles) {
            formData.visibleRoles = formData.visibleRoles.split(',').filter(role => role.trim());
          } else {
            formData.visibleRoles = [];
          }
          
          this.form.setFieldsValue(formData);
        }
      });
    },
    close() {
      this.$emit('close');
      this.visible = false;
    },
    handleOk() {
      const that = this;
      
      // 手动验证内容
      this.contentError = '';
      if (!this.model.content || this.model.content.trim() === '') {
        this.contentError = '请输入内容!';
        return;
      }
      
      // 触发表单验证（除了content字段）
      this.form.validateFields((err, values) => {
        if (!err) {
          that.confirmLoading = true;
          let httpurl = '';
          let method = '';
          
          // 添加内容字段
          values.content = this.model.content;
          
          // 处理置顶字段
          values.isTop = values.isTop ? 1 : 0;
          
          // 处理可见角色
          if (values.visibleRoles && values.visibleRoles.length > 0) {
            values.visibleRoles = values.visibleRoles.join(',');
          } else {
            values.visibleRoles = '';
          }
          
          if (!this.model.id) {
            httpurl = that.url.add;
            method = 'post';
          } else {
            httpurl = that.url.edit;
            method = 'put';
            values.id = this.model.id;
          }
          
          httpAction(httpurl, values, method)
            .then(res => {
              if (res.success) {
                that.$message.success(res.message);
                that.$emit('ok');
                that.close();
              } else {
                that.$message.warning(res.message);
              }
            })
            .finally(() => {
              that.confirmLoading = false;
            });
        }
      });
    },
    handleCancel() {
      this.close();
    },
    loadRoleList() {
      getAction(this.url.getRoleList).then(res => {
        if (res.success) {
          this.roleOptions = res.result.map(role => ({
            label: role.roleName,
            value: role.roleCode
          }));
          
          // 如果是新增模式且表单已经初始化，设置默认选中所有角色
          if (this.visible && this.title === '新增') {
            this.$nextTick(() => {
              const allRoles = this.roleOptions.map(role => role.value);
              this.form.setFieldsValue({
                visibleRoles: allRoles
              });
            });
          }
        }
      });
    }
  }
};
</script>

<style scoped>
/* 表单提示文字 */
.form-tip {
  margin-top: 8px;
  color: #999;
  font-size: 12px;
}

/* 编辑器容器 */
.editor-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}

.editor-container >>> .tox-tinymce {
  border: none;
}

.editor-container >>> .tox-toolbar {
  background: #fafafa;
  border-bottom: 1px solid #e8e8e8;
}

.editor-container >>> .tox-edit-area {
  min-height: 350px;
}

/* 错误提示 */
.error-text {
  color: #f5222d;
  font-size: 12px;
  margin-top: 8px;
}

/* 表单标签对齐 */
.ant-form >>> .ant-form-item-label {
  text-align: right;
}

/* 三列布局中的表单项标签对齐 */
.ant-row .ant-col .ant-form-item-label {
  text-align: right;
  padding-right: 8px;
}

/* 确保开关组件垂直居中 */
.ant-form-item-control-wrapper .ant-switch {
  vertical-align: middle;
}

/* 角色选择区域样式优化 */
.ant-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
  align-items: center;
}

.ant-checkbox-group >>> .ant-checkbox-wrapper {
  margin-right: 0;
  display: flex;
  align-items: center;
}

/* 表单项间距优化 */
.ant-form-item {
  margin-bottom: 24px;
}

.ant-row .ant-col .ant-form-item {
  margin-bottom: 16px;
}

/* 响应式 */
@media (max-width: 768px) {
  .ant-checkbox-group {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .ant-row .ant-col {
    margin-bottom: 16px;
  }
  
  .ant-row .ant-col .ant-form-item-label {
    text-align: left;
    padding-right: 0;
  }
}

.ant-form-item {
  display: flex !important;
  align-items: center !important;
}
.ant-form-item-label, .ant-form-item-control {
  display: inline-block !important;
  vertical-align: middle !important;
}
</style> 