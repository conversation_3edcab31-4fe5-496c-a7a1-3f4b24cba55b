package com.woyaotuanjian.modules.biz.mcp.util;

import com.woyaotuanjian.modules.biz.entity.BizTrip;
import com.woyaotuanjian.modules.biz.mcp.dto.TripDetailResult;

/**
 * 行程数据转换工具类
 * 负责将业务实体转换为MCP协议兼容的响应对象
 */
public class TripConverter {
    
    /**
     * 将BizTrip转换为TripDetailResult
     * 确保返回的数据格式符合MCP规范
     * 
     * @param bizTrip 业务行程对象
     * @return MCP格式的行程详情结果，如果输入为null则返回null
     */
    public static TripDetailResult toTripDetailResult(BizTrip bizTrip) {
        if (bizTrip == null) {
            return null;
        }
        
        TripDetailResult result = new TripDetailResult();
        
        // 基本信息映射
        result.setId(bizTrip.getId());
        result.setTripName(cleanString(bizTrip.getTripName()));
        result.setTripFullName(cleanString(bizTrip.getTripFullName()));
        result.setPrice(bizTrip.getPrice());
        result.setDayNum(bizTrip.getDayNum());
        
        // 描述信息映射
        result.setTripDesc(cleanString(bizTrip.getTripDesc()));
        result.setAdvantageDesc(cleanString(bizTrip.getAdvantageDesc()));
        result.setSchedule(cleanString(bizTrip.getSchedule()));
        result.setPriceRemark(cleanString(bizTrip.getPriceRemark()));
        result.setTripTip(cleanString(bizTrip.getTripTip()));
        result.setTripTag(cleanString(bizTrip.getTripTag()));
        
        return result;
    }
    
    /**
     * 清理字符串，去除空白字符，如果为空则返回null
     * 确保MCP响应中不包含空字符串
     * 
     * @param str 原始字符串
     * @return 清理后的字符串或null
     */
    private static String cleanString(String str) {
        if (str == null) {
            return null;
        }
        
        String cleaned = str.trim();
        return cleaned.isEmpty() ? null : cleaned;
    }
}