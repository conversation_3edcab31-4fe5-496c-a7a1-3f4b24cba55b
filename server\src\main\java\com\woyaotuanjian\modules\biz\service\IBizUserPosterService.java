package com.woyaotuanjian.modules.biz.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.woyaotuanjian.modules.biz.entity.BizUserPoster;
import com.woyaotuanjian.modules.biz.entity.BizPosterExportRecord;
import com.woyaotuanjian.modules.biz.entity.dto.PosterExportRequest;
import com.woyaotuanjian.modules.biz.entity.vo.TripPosterData;

import java.util.List;

/**
 * @Description: 用户海报
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
public interface IBizUserPosterService extends IService<BizUserPoster> {

    /**
     * 从行程数据提取海报变量
     * @param tripId 行程ID
     * @return 行程海报数据
     */
    TripPosterData extractTripData(Long tripId);
    
    /**
     * 验证海报数据格式
     * @param posterData 海报数据JSON字符串
     * @return 是否有效
     */
    boolean validatePosterData(String posterData);
    
    /**
     * 生成海报缩略图
     * @param posterId 海报ID
     * @return 缩略图URL
     */
    String generateThumbnail(Long posterId);
    
    /**
     * 检查用户是否有权限访问海报
     * @param posterId 海报ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasAccessPermission(Long posterId, Integer userId);
    
    /**
     * 保存导出记录
     * @param exportRecord 导出记录
     */
    void saveExportRecord(BizPosterExportRecord exportRecord);
    
    /**
     * 获取导出记录列表
     * @param userId 用户ID
     * @param posterId 海报ID（可选）
     * @param page 分页参数
     * @return 导出记录分页列表
     */
    IPage<BizPosterExportRecord> getExportRecords(Integer userId, Long posterId, Page<BizPosterExportRecord> page);
    
    /**
     * 批量导出海报
     * @param request 导出请求
     * @param userId 用户ID
     * @return 导出结果列表
     */
    List<String> batchExportPoster(PosterExportRequest request, Integer userId);
}