package com.woyaotuanjian.modules.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.woyaotuanjian.modules.biz.dto.RouteQueryRequest;
import com.woyaotuanjian.modules.biz.dto.RouteQueryResponse;
import com.woyaotuanjian.modules.biz.entity.BizTripRoute;
import com.woyaotuanjian.modules.biz.service.IBizTripRouteService;
import com.woyaotuanjian.modules.biz.service.IRouteQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 路线查询服务实现
 */
@Slf4j
@Service
public class RouteQueryServiceImpl implements IRouteQueryService {
    
    private static final String CACHE_PREFIX = "route:driving:";
    private static final long CACHE_EXPIRE_DAYS = 7;
    private static final String TENCENT_MAP_KEY = "232BZ-7K263-SUP3U-ONEWQ-CMN2J-E2FMD";
    
    @Autowired
    private IBizTripRouteService bizTripRouteService;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Override
    public List<RouteQueryResponse> batchQueryRoutes(List<RouteQueryRequest> requests) {
        List<RouteQueryResponse> responses = new ArrayList<>();
        
        for (RouteQueryRequest request : requests) {
            RouteQueryResponse response = queryRoute(request);
            responses.add(response);
        }
        
        return responses;
    }
    
    /**
     * 查询单个路线
     */
    private RouteQueryResponse queryRoute(RouteQueryRequest request) {
        try {
            // 1. 优先查询数据库配置
            RouteQueryResponse dbResult = queryFromDatabase(request);
            if (dbResult != null && dbResult.getSuccess()) {
                return dbResult;
            }
            
            // 2. 查询Redis缓存
            String cacheKey = generateCacheKey(request.getOriginLat(), request.getOriginLng(), 
                                             request.getDestLat(), request.getDestLng());
            String cachedResult = redisTemplate.opsForValue().get(cacheKey);
            if (cachedResult != null) {
                return JSON.parseObject(cachedResult, RouteQueryResponse.class);
            }
            
            // 3. 调用腾讯地图API
            RouteQueryResponse apiResult = queryFromTencentMap(request);
            if (apiResult != null && apiResult.getSuccess()) {
                // 缓存到Redis
                redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(apiResult), 
                                               CACHE_EXPIRE_DAYS, TimeUnit.DAYS);
            }
            
            return apiResult;
            
        } catch (Exception e) {
            log.error("[ROUTE_QUERY] 查询路线失败", e);
            RouteQueryResponse errorResponse = new RouteQueryResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("查询路线失败: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 从数据库查询用户配置的路线
     */
    private RouteQueryResponse queryFromDatabase(RouteQueryRequest request) {
        if (request.getTripId() == null || request.getOriginId() == null || 
            request.getDestinationId() == null) {
            return null;
        }
        
        try {
            BizTripRoute dbRoute = bizTripRouteService.getByRoute(
                request.getTripId(),
                request.getOriginId(), 
                request.getOriginType(),
                request.getDestinationId(), 
                request.getDestinationType()
            );
            
            if (dbRoute != null) {
                RouteQueryResponse response = new RouteQueryResponse();
                response.setDistance(dbRoute.getDistance() != null ? dbRoute.getDistance().toString() : "0");
                response.setDuration(dbRoute.getDuration());
                response.setDistanceText(dbRoute.getDistance() != null ? dbRoute.getDistance() + "km" : "未知距离");
                response.setDurationText(formatDuration(dbRoute.getDuration()));
                response.setTransportType(dbRoute.getTransportType());
                response.setSuccess(true);
                
                // 数据库配置的路线不包含详细路径点，根据交通方式决定是否需要路径
                if ("driving".equals(dbRoute.getTransportType())) {
                    // 驾车方式需要详细路径，但数据库没有，设为空让前端用直线
                    response.setPoints(new ArrayList<>());
                } else {
                    // 非驾车方式使用直线连接
                    response.setPoints(createStraightLine(request));
                }
                return response;
            }
        } catch (Exception e) {
            log.error("查询数据库路线配置失败", e);
        }
        
        return null;
    }
    
    /**
     * 调用腾讯地图API查询路线
     */
    private RouteQueryResponse queryFromTencentMap(RouteQueryRequest request) {
        try {
            String from = request.getOriginLat() + "," + request.getOriginLng();
            String to = request.getDestLat() + "," + request.getDestLng();
            String url = String.format(
                "https://apis.map.qq.com/ws/direction/v1/driving/?from=%s&to=%s&policy=SHORT_DISTANCE&key=%s",
                from, to, TENCENT_MAP_KEY);
            
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            String responseBody = response.getBody();
            
            if (responseBody == null) {
                throw new RuntimeException("腾讯地图API返回空响应");
            }
            
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            Integer status = jsonResponse.getInteger("status");
            
            if (status == null || status != 0) {
                String message = jsonResponse.getString("message");
                throw new RuntimeException("腾讯地图API错误: " + (message != null ? message : "未知错误"));
            }
            
            JSONObject result = jsonResponse.getJSONObject("result");
            JSONArray routes = result.getJSONArray("routes");
            
            if (routes == null || routes.isEmpty()) {
                throw new RuntimeException("未找到路线");
            }
            
            JSONObject route = routes.getJSONObject(0);
            Integer distance = route.getInteger("distance"); // 米
            Integer duration = route.getInteger("duration");   // 分钟
            JSONArray polyline = route.getJSONArray("polyline");
            
            RouteQueryResponse queryResponse = new RouteQueryResponse();
            queryResponse.setDistance(String.valueOf(distance / 1000.0));
            queryResponse.setDuration(duration);
            queryResponse.setDistanceText(distance >= 1000 ? 
                String.format("%.1fkm", distance / 1000.0) : distance + "m");
            queryResponse.setDurationText(formatDuration(duration));
            queryResponse.setTransportType("driving");
            queryResponse.setPoints(decodePolyline(polyline));
            queryResponse.setSuccess(true);
            
            return queryResponse;
            
        } catch (Exception e) {
            log.error("调用腾讯地图API失败", e);
            RouteQueryResponse errorResponse = new RouteQueryResponse();
            errorResponse.setSuccess(false);
            errorResponse.setErrorMessage("调用腾讯地图API失败: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 解码腾讯地图polyline
     */
    private List<RouteQueryResponse.RoutePoint> decodePolyline(JSONArray polyline) {
        List<RouteQueryResponse.RoutePoint> points = new ArrayList<>();
        
        if (polyline == null || polyline.size() < 2) {
            return points;
        }
        
        try {
            double[] coords = new double[polyline.size()];
            for (int i = 0; i < polyline.size(); i++) {
                coords[i] = polyline.getDoubleValue(i);
            }
            
            // 腾讯地图polyline解码算法
            double kr = 1000000.0;
            for (int i = 2; i < coords.length; i++) {
                coords[i] = coords[i - 2] + coords[i] / kr;
            }
            
            // 转换为点数组
            for (int i = 0; i < coords.length; i += 2) {
                if (i + 1 >= coords.length) break;
                
                double lat = coords[i];
                double lng = coords[i + 1];
                
                if (Math.abs(lat) <= 90 && Math.abs(lng) <= 180) {
                    RouteQueryResponse.RoutePoint point = new RouteQueryResponse.RoutePoint();
                    point.setLatitude(lat);
                    point.setLongitude(lng);
                    points.add(point);
                }
            }
        } catch (Exception e) {
            log.error("解码polyline失败", e);
        }
        
        return points;
    }
    
    /**
     * 创建直线路径
     */
    private List<RouteQueryResponse.RoutePoint> createStraightLine(RouteQueryRequest request) {
        List<RouteQueryResponse.RoutePoint> points = new ArrayList<>();
        
        RouteQueryResponse.RoutePoint start = new RouteQueryResponse.RoutePoint();
        start.setLatitude(request.getOriginLat());
        start.setLongitude(request.getOriginLng());
        points.add(start);
        
        // 添加中点
        double midLat = (request.getOriginLat() + request.getDestLat()) / 2;
        double midLng = (request.getOriginLng() + request.getDestLng()) / 2;
        RouteQueryResponse.RoutePoint mid = new RouteQueryResponse.RoutePoint();
        mid.setLatitude(midLat);
        mid.setLongitude(midLng);
        points.add(mid);
        
        RouteQueryResponse.RoutePoint end = new RouteQueryResponse.RoutePoint();
        end.setLatitude(request.getDestLat());
        end.setLongitude(request.getDestLng());
        points.add(end);
        
        return points;
    }
    
    /**
     * 格式化时长
     */
    private String formatDuration(Integer minutes) {
        if (minutes == null || minutes <= 0) {
            return "0分钟";
        }
        
        if (minutes < 60) {
            return minutes + "分钟";
        } else {
            int hours = minutes / 60;
            int mins = minutes % 60;
            return mins > 0 ? hours + "小时" + mins + "分钟" : hours + "小时";
        }
    }
    
    /**
     * 生成缓存Key，正反方向共用一份缓存
     */
    private String generateCacheKey(Double originLat, Double originLng, Double destLat, Double destLng) {
        // 比较起终点，按纬度优先，其次经度排序，保证正反方向key一致
        boolean originFirst = (originLat < destLat) || (originLat.equals(destLat) && originLng <= destLng);
        Double firstLat = originFirst ? originLat : destLat;
        Double firstLng = originFirst ? originLng : destLng;
        Double secondLat = originFirst ? destLat : originLat;
        Double secondLng = originFirst ? destLng : originLng;
        return CACHE_PREFIX + firstLat + "," + firstLng + ":" + secondLat + "," + secondLng;
    }
} 