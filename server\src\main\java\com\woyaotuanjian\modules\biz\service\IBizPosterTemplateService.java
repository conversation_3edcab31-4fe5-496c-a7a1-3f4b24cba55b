package com.woyaotuanjian.modules.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.woyaotuanjian.modules.biz.entity.BizPosterTemplate;

import java.util.List;

/**
 * @Description: 海报模板
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
public interface IBizPosterTemplateService extends IService<BizPosterTemplate> {

    /**
     * 复制模板
     * @param original 原始模板
     * @return 复制后的模板
     */
    BizPosterTemplate copyTemplate(BizPosterTemplate original);
    
    /**
     * 验证模板数据格式
     * @param templateData 模板数据JSON字符串
     * @return 是否有效
     */
    boolean validateTemplateData(String templateData);
    
    /**
     * 生成模板缩略图
     * @param templateId 模板ID
     * @return 缩略图URL
     */
    String generateThumbnail(Long templateId);
    
    /**
     * 启用模板
     * @param id 模板ID
     * @return 是否成功
     */
    boolean enableTemplate(Long id);
    
    /**
     * 禁用模板
     * @param id 模板ID
     * @return 是否成功
     */
    boolean disableTemplate(Long id);
    
    /**
     * 批量更新模板状态
     * @param ids 模板ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> ids, Integer status);
    
    /**
     * 获取启用的模板列表
     * @return 启用的模板列表
     */
    List<BizPosterTemplate> getEnabledTemplates();
    
    /**
     * 获取用户可访问的启用模板列表
     * @param accessibleTemplateIds 用户可访问的模板ID列表，空列表表示无限制
     * @return 启用的模板列表
     */
    List<BizPosterTemplate> getEnabledTemplatesForUser(List<Long> accessibleTemplateIds);
    
    /**
     * 更新模板排序
     * @param id 模板ID
     * @param sortOrder 排序值
     * @return 是否成功
     */
    boolean updateSortOrder(Long id, Integer sortOrder);
}