<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <link
      rel="icon"
      href="https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico"
      type="image/x-icon"
    />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>游美乐AI客服</title>
    <!-- 自定义手机端CSS -->
    <link rel="stylesheet" href="mobile-chatbot.css" />
  </head>
  <body>
    <link rel="stylesheet" crossorigin href="https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.css" />
    <script type="module" crossorigin src="https://g.alicdn.com/aliyun-documentation/web-chatbot-ui/0.0.24/index.js"></script>
    <script>
      window.CHATBOT_CONFIG = {
        endpoint: "https://webchat-bot-yml-oadharjhbi.cn-hangzhou.fcapp.run/chat", // 可以替换为 https://{your-fc-http-trigger-domain}/chat
        displayByDefault: true, // 默认显示 AI 助手对话框
        title: '游美乐AI客服', // 自定义 AI 助手标题
        draggable: false, // 关闭拖拽功能，适合手机端
        aiChatOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#conversation-options
          conversationOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#conversation-options
            conversationStarters: [
              {prompt: '走吧！阿勒泰 有什么特色'},
              {prompt: '新疆亚克西纵横南北大环线最近的团期是？'},
              {prompt: '尊享伊+1 线路有哪些特色？'},
            ]
          },
          displayOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#display-options
            height: '100vh',
            width: '100%',
          },
          personaOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#chat-personas
            assistant: {
              name: '您好，我是游美乐AI客服小乐',
              // AI 助手的图标
              avatar: 'https://img.alicdn.com/imgextra/i2/O1CN01Pda9nq1YDV0mnZ31H_!!6000000003025-54-tps-120-120.apng',
              tagline: '有什么可以帮助您的吗？您可以点击下方的快捷问题开始咨询！',
            }
          },
          messageOptions: { // 自定义取值参考：https://docs.nlkit.com/nlux/reference/ui/ai-chat#message-options
            waitTimeBeforeStreamCompletion: 'never'
          }
        },
        dataProcessor: {
          /**
          * 在向后端大模型应用发起请求前改写 Prompt。
          * 比如可以用于总结网页场景，在发送前将网页内容包含在内，同时避免在前端显示这些内容。
          * @param {string} prompt - 用户输入的 Prompt
          * @param {string}  - 改写后的 Prompt
          */
          rewritePrompt(prompt) {
            return prompt;
          }
        }
      };
    </script>
  </body>
</html> 