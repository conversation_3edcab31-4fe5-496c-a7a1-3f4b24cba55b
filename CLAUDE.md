# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a travel/trip management system called "mailang" with separate frontend and backend:
- **Frontend**: Vue.js 2.7 application in `/page` directory using Ant Design Vue
- **Backend**: Spring Boot 2.3.12 application in `/server` directory with MySQL and Redis

## Common Development Commands

### Frontend Development (Vue.js)
```bash
cd page
yarn install                    # Install dependencies (yarn is enforced via preinstall script)
yarn run serve                  # Development mode (port 3300)
yarn run build:prod            # Production build
yarn run build:dev             # Development build
yarn run build:test            # Test build
yarn run lint                  # Run linting
yarn run test:unit             # Run unit tests
yarn run test:e2e              # Run e2e tests
```

### Backend Development (Spring Boot)
```bash
cd server
mvn clean install              # Install dependencies
mvn spring-boot:run -P dev      # Development mode
mvn clean package -P prod       # Production build
mvn clean package -P dev        # Development build
mvn clean package -P test       # Test build
```

### Full Stack Development
```bash
# From root directory
npm run dev                     # Start both frontend and backend
npm run prod                    # Build both for production
npm run frontend               # Frontend only
npm run backend                # Backend only
```

## Project Architecture

### Backend Structure (`/server/src/main/java/com/woyaotuanjian/`)
- **Application.java** - Main Spring Boot entry point
- **config/** - Configuration classes (Redis, Swagger, Shiro, etc.)
- **common/** - Shared utilities, constants, and base classes
  - **ai/** - AI service integration (OpenAI, Gemini, DeepSeek, Qwen)
  - **api/** - Common API utilities and exception handling
  - **util/** - Utility classes (Redis, JWT, Date, etc.)
- **modules/biz/** - Core business logic for travel/trip management
  - **controller/** - REST API endpoints
  - **service/** - Business logic implementation
  - **entity/** - Database entities (JPA/MyBatis)
  - **mapper/** - MyBatis mappers
  - **export/** - Export functionality (Word, Excel, PPT)
- **modules/system/** - System administration (users, roles, permissions)
- **modules/shiro/** - Authentication and authorization

### Frontend Structure (`/page/src/`)
- **main.js** - Vue application entry point
- **App.vue** - Root component
- **router/** - Vue Router configuration
- **store/** - Vuex state management
- **views/** - Page components
  - **biz/** - Business-related pages (trips, hotels, restaurants, etc.)
  - **system/** - System administration pages
  - **dashboard/** - Analytics and monitoring
- **components/** - Reusable components
- **utils/** - Frontend utilities (HTTP, auth, validation)

## Key Technologies

### Backend
- Spring Boot 2.3.12 with Java 8
- MyBatis Plus for database operations
- Shiro for security and JWT authentication
- Redis for caching
- MySQL 5.7 for data persistence
- Swagger for API documentation
- Multiple AI service integrations

### Frontend
- Vue.js 2.7 with Composition API support
- Ant Design Vue for UI components
- Vue Router for routing
- Vuex for state management
- Axios for HTTP requests
- VXE Table for advanced table functionality

## Environment Configuration

### Backend Configuration Files
- `application.yml` - Main configuration
- `application-dev.yml` - Development environment
- `application-prod.yml` - Production environment
- `application-test.yml` - Test environment

### Frontend Environment Files
- `.env.prod` - Production environment variables
- `.env.dev` - Development environment variables  
- `.env.test` - Test environment variables
- Update `VUE_APP_API_BASE_URL` for API endpoint configuration

## Database Requirements
- MySQL 5.7 required
- Disable strict mode and remove `ONLY_FULL_GROUP_BY` from `sql_mode`
- Import schema from `server/src/main/resources/sql/ddl_data.sql`

## Development Setup
1. Start MySQL and Redis services
2. Configure database connection in `application-dev.yml`
3. Run backend: `cd server && mvn spring-boot:run -P dev`
4. Run frontend: `cd page && yarn run serve`
5. Access application at http://localhost:3300
6. API documentation at http://localhost:8077/trip/doc.html

## Export Features
The system supports exporting trip data to multiple formats:
- **Word documents** using poi-tl templates
- **Excel files** using JXLS templates  
- **PowerPoint presentations** using Aspose.Slides
- Templates located in `server/src/main/resources/word/`, `excel/`, `ppt/`

## AI Integration
Supports multiple AI providers for content generation:
- OpenAI GPT models
- Google Gemini
- Alibaba Qwen
- DeepSeek
- Configurable through admin interface