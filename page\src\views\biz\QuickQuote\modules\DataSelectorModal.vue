<template>
  <a-modal
    :title="modalTitle"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
    :width="isMobile() ? '100%' : '800px'"
    :bodyStyle="isMobile() ? { padding: '12px', maxHeight: '60vh', overflowY: 'auto' } : { padding: '16px' }"
    :destroyOnClose="true"
    okText="确定"
    cancelText="取消"
  >
    <!-- 搜索框 -->
    <div class="search-section">
      <a-input-search
        v-model="searchKeyword"
        placeholder="输入名称搜索"
        enter-button="搜索"
        size="default"
        @search="handleSearch"
        @change="handleSearchChange"
        allowClear
        class="search-input"
      />
    </div>

    <!-- 数据列表 -->
    <div class="data-list" :class="{ mobile: isMobile() }">
      <!-- 桌面端表格 -->
      <div v-if="!isMobile()" class="desktop-table">
        <a-table
          :dataSource="dataList"
          :columns="tableColumns"
          :pagination="paginationConfig"
          :loading="loading"
          :rowKey="record => record.id"
          :rowSelection="{ 
            type: 'radio', 
            selectedRowKeys: selectedKeys, 
            onChange: handleSelectionChange 
          }"
          size="middle"
          @change="handleTableChange"
          :customRow="getCustomRowProps"
        >
          <template slot="name" slot-scope="text, record">
            <div class="name-cell">
              <div class="main-name">{{ record.name }}</div>
              <div v-if="record.simpleName && record.simpleName !== record.name" class="simple-name">
                {{ record.simpleName }}
              </div>
            </div>
          </template>

          <template slot="price" slot-scope="text, record">
            <div class="price-cell">
              <div v-if="record.price" class="price-value">￥{{ record.price }}</div>
              <div v-else class="no-price">未设置</div>
              <div v-if="record.priceRemark" class="price-remark">{{ record.priceRemark }}</div>
            </div>
          </template>

          <template slot="location" slot-scope="text, record">
            <div class="location-cell">
              {{ record.location || '未设置' }}
            </div>
          </template>
        </a-table>
      </div>

      <!-- 移动端卡片列表 -->
      <div v-else class="mobile-cards">
        <div v-if="loading" class="loading-container">
          <a-spin size="large" />
        </div>
        
        <div v-else-if="dataList.length === 0" class="empty-container">
          <a-empty description="暂无数据" />
        </div>
        
        <div v-else>
          <div
            v-for="item in displayDataList"
            :key="item.id"
            class="mobile-card"
            :class="{ selected: selectedKeys.includes(item.id) }"
            @click="selectMobileItem(item)"
          >
            <div class="card-content">
              <div class="card-header">
                <div class="name-section">
                  <div class="main-name">{{ item.name }}</div>
                  <div v-if="item.simpleName && item.simpleName !== item.name" class="simple-name">
                    {{ item.simpleName }}
                  </div>
                </div>
                <div class="price-section">
                  <div v-if="item.price" class="price-value">￥{{ item.price }}</div>
                  <div v-else class="no-price">未设置</div>
                </div>
              </div>
              
              <div class="card-body">
                <div v-if="item.location" class="location-info">
                  <a-icon type="environment" />
                  {{ item.location }}
                </div>
                <div v-if="item.priceRemark" class="price-remark">
                  {{ item.priceRemark }}
                </div>
              </div>
            </div>
            
            <!-- 选中状态指示器 -->
            <div v-if="selectedKeys.includes(item.id)" class="selected-indicator">
              <a-icon type="check-circle" />
            </div>
          </div>

          <!-- 移动端简单提示 -->
          <div class="mobile-info" v-if="isMobile() && total > 3">
            <div class="total-info">共返回 {{ total }} 条数据，显示前 3 条</div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import { mixinDevice } from '@/utils/mixin.js'

export default {
  name: 'DataSelectorModal',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dataType: {
      type: String,
      required: true,
      validator: value => ['scenic', 'hotel'].includes(value)
    }
  },
  mixins: [mixinDevice],

  data() {
    return {
      searchKeyword: '',
      dataList: [],
      loading: false,
      selectedKeys: [],
      selectedItem: null,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchTimer: null
    }
  },

  computed: {
    modalTitle() {
      const titles = {
        scenic: '选择景点',
        hotel: '选择酒店'
      }
      return titles[this.dataType] || '选择数据'
    },

    tableColumns() {
      return [
        {
          title: '名称',
          dataIndex: 'name',
          width: '40%',
          scopedSlots: { customRender: 'name' }
        },
        {
          title: '价格',
          dataIndex: 'price',
          width: '25%',
          scopedSlots: { customRender: 'price' }
        },
        {
          title: '位置',
          dataIndex: 'location',
          width: '35%',
          scopedSlots: { customRender: 'location' }
        }
      ]
    },

    paginationConfig() {
      return {
        current: this.currentPage,
        pageSize: this.pageSize,
        total: this.total,
        showSizeChanger: false,
        showQuickJumper: true,
        showTotal: (total, range) => `共 ${total} 条，当前 ${range[0]}-${range[1]} 条`,
        size: 'default'
      }
    },

    apiEndpoint() {
      const endpoints = {
        scenic: '/biz/bizQuickQuote/searchScenicList',
        hotel: '/biz/bizQuickQuote/searchHotelList'
      }
      return endpoints[this.dataType]
    },

    currentPageSize() {
      return this.isMobile() ? 3 : this.pageSize
    },

    displayDataList() {
      return this.isMobile() ? this.dataList.slice(0, 3) : this.dataList
    }
  },

  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetModal()
        this.loadData()
      }
    }
  },

  mounted() {
  },

  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  },

  methods: {

    // 重置模态框状态
    resetModal() {
      this.searchKeyword = ''
      this.dataList = []
      this.selectedKeys = []
      this.selectedItem = null
      this.currentPage = 1
      this.total = 0
    },

    // 加载数据
    async loadData() {
      if (!this.apiEndpoint) return

      this.loading = true
      try {
        const params = {
          keyword: this.searchKeyword || undefined,
          pageNo: this.isMobile() ? 1 : this.currentPage,  // 移动端始终加载第一页
          pageSize: this.currentPageSize
        }

        const response = await getAction(this.apiEndpoint, params)
        
        if (response.success) {
          this.dataList = response.result.items || []
          this.total = response.result.total || 0
        } else {
          this.$message.error(response.message || '加载数据失败')
          this.dataList = []
          this.total = 0
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
        this.dataList = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1
      this.loadData()
    },

    // 搜索输入变化（防抖）
    handleSearchChange(e) {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      
      this.searchTimer = setTimeout(() => {
        if (this.searchKeyword !== e.target.value) {
          this.searchKeyword = e.target.value
          this.handleSearch()
        }
      }, 500)
    },

    // 桌面端选择变化
    handleSelectionChange(selectedRowKeys, selectedRows) {
      this.selectedKeys = selectedRowKeys
      this.selectedItem = selectedRows[0] || null
    },

    // 自定义行属性，使整行可点击
    getCustomRowProps(record) {
      return {
        on: {
          click: () => {
            this.handleRowClick(record)
          }
        },
        style: {
          cursor: 'pointer'
        }
      }
    },

    // 处理行点击
    handleRowClick(record) {
      if (this.selectedKeys.includes(record.id)) {
        // 如果已选中，则取消选择
        this.selectedKeys = []
        this.selectedItem = null
      } else {
        // 选择当前行
        this.selectedKeys = [record.id]
        this.selectedItem = record
      }
    },

    // 移动端选择项目
    selectMobileItem(item) {
      if (this.selectedKeys.includes(item.id)) {
        // 取消选择
        this.selectedKeys = []
        this.selectedItem = null
      } else {
        // 选择项目
        this.selectedKeys = [item.id]
        this.selectedItem = item
      }
    },

    // 表格变化处理
    handleTableChange(pagination) {
      this.currentPage = pagination.current
      this.loadData()
    },

    // 移动端分页变化
    handlePageChange(page) {
      this.currentPage = page
      this.loadData()
    },

    // 确定选择
    handleOk() {
      if (!this.selectedItem) {
        this.$message.warning('请选择一个项目')
        return
      }

      this.$emit('ok', {
        ...this.selectedItem,
        category: this.dataType,
        bizType: this.dataType
      })
    },

    // 取消选择
    handleCancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="less" scoped>
.search-section {
  margin-bottom: 16px;

  .search-input {
    width: 100%;
  }
}

.data-list {
  min-height: 300px;

  &.mobile {
    height: auto;
    max-height: 50vh;
    overflow-y: auto;
  }

  .desktop-table {
    // 表格行悬停效果
    :deep(.ant-table-tbody > tr:hover > td) {
      background-color: #e6f7ff !important;
    }

    // 选中行样式
    :deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
      background-color: #bae7ff !important;
    }

    .name-cell {
      .main-name {
        font-weight: 500;
        color: #262626;
        margin-bottom: 2px;
      }

      .simple-name {
        font-size: 12px;
        color: #8c8c8c;
      }
    }

    .price-cell {
      .price-value {
        font-weight: 500;
        color: #f5222d;
        margin-bottom: 2px;
      }

      .no-price {
        color: #bfbfbf;
        font-style: italic;
        margin-bottom: 2px;
      }

      .price-remark {
        font-size: 12px;
        color: #8c8c8c;
      }
    }

    .location-cell {
      color: #595959;
    }
  }

  .mobile-cards {
    .loading-container,
    .empty-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 200px;
    }

    .mobile-card {
      position: relative;
      margin-bottom: 6px;
      padding: 10px;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
      background: #fff;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #d9d9d9;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
      }

      &.selected {
        border-color: #1890ff;
        background: #e6f7ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
      }

      .card-content {
        padding-right: 24px;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .name-section {
          flex: 1;
          min-width: 0;

          .main-name {
            font-size: 15px;
            font-weight: 500;
            color: #262626;
            line-height: 1.4;
            margin-bottom: 2px;
            word-break: break-all;
          }

          .simple-name {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 1.3;
          }
        }

        .price-section {
          flex-shrink: 0;
          text-align: right;
          margin-left: 12px;

          .price-value {
            font-size: 15px;
            font-weight: 600;
            color: #f5222d;
            white-space: nowrap;
          }

          .no-price {
            font-size: 13px;
            color: #bfbfbf;
            font-style: italic;
          }
        }
      }

      .card-body {
        .location-info {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #8c8c8c;
          margin-bottom: 4px;

          .anticon {
            margin-right: 4px;
            font-size: 11px;
          }
        }

        .price-remark {
          font-size: 11px;
          color: #bfbfbf;
          line-height: 1.3;
        }
      }

      .selected-indicator {
        position: absolute;
        top: 8px;
        right: 8px;
        color: #1890ff;
        font-size: 16px;
      }
    }

    .mobile-info {
      margin-top: 12px;
      text-align: center;
      
      .total-info {
        font-size: 12px;
        color: #8c8c8c;
        padding: 8px;
        background: #f5f5f5;
        border-radius: 4px;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .search-section {
    margin-bottom: 12px;
  }

  .mobile-cards {
    .mobile-card {
      margin-bottom: 4px;
      padding: 8px;
      border-radius: 4px;

      .card-header {
        margin-bottom: 6px;

        .name-section {
          .main-name {
            font-size: 14px;
          }

          .simple-name {
            font-size: 11px;
          }
        }

        .price-section {
          margin-left: 8px;

          .price-value {
            font-size: 14px;
          }

          .no-price {
            font-size: 12px;
          }
        }
      }

      .card-body {
        .location-info {
          font-size: 11px;
          margin-bottom: 3px;
        }

        .price-remark {
          font-size: 10px;
        }
      }

      .selected-indicator {
        top: 6px;
        right: 6px;
        font-size: 14px;
      }
    }

    .mobile-info {
      margin-top: 8px;
      
      .total-info {
        font-size: 11px;
        padding: 6px;
      }
    }
  }
}
</style> 