<template>
  <div class="quick-quote-list">
    <!-- 搜索栏 -->
    <a-card class="search-card" :bordered="false">
      <a-form layout="inline">
        <a-form-item label="报价名称">
          <a-input 
            v-model="searchForm.quoteName" 
            placeholder="输入报价名称"
            allowClear
            
            @pressEnter="handleSearch"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <a-icon type="search" />
            搜索
          </a-button>
          <a-button  type="primary"   style="margin-left: 8px" @click="goToCreate">
            <a-icon type="plus" />
            新建报价
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 报价列表 -->
    <a-card class="table-card" :bordered="false">
      <a-table
        :columns="columns"
        :dataSource="dataSource"
        :pagination="pagination"
        :loading="loading"
        :rowKey="record => record.id"
        @change="handleTableChange"
      >
        <!-- 报价名称 -->
        <template slot="quoteName" slot-scope="text">
          <span>{{ text }}</span>
        </template>

        <!-- 来源类型 -->
        <template slot="sourceType" slot-scope="text">
          <a-tag :color="getSourceTypeColor(text)">
            {{ getSourceTypeName(text) }}
          </a-tag>
        </template>

        <!-- 总金额 -->
        <template slot="totalAmount" slot-scope="text">
          <span class="amount-text">{{ formatPrice(text) }}</span>
        </template>

        <!-- 人均金额 -->
        <template slot="perPersonAmount" slot-scope="text">
          <span class="amount-text">{{ formatPrice(text) }}</span>
        </template>

        <!-- 人数 -->
        <template slot="personCount" slot-scope="text">
          <span>{{ text }}人</span>
        </template>

        <!-- 创建时间 -->
        <template slot="createTime" slot-scope="text">
          <span>{{ formatDate(text) }}</span>
        </template>

        <!-- 操作 -->
        <template slot="action" slot-scope="text, record">
          <div class="action-buttons">
            <a-button type="link" size="small" @click="handleEdit(record)">
              <a-icon type="edit" />
              编辑
            </a-button>
            <a-divider type="vertical" />
            <a-button type="link" size="small" @click="handleCopy(record)">
              <a-icon type="copy" />
              复制
            </a-button>
            <a-divider type="vertical" />
            <a-popconfirm
              title="确定要删除这个报价吗？"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" size="small" class="danger-link">
                <a-icon type="delete" />
                删除
              </a-button>
            </a-popconfirm>
          </div>
        </template>
      </a-table>
    </a-card>

    <!-- 帮助栏 -->
    <a-card class="help-card" :bordered="false">
      <div class="help-header">
        <a-icon type="question-circle" style="color: #13c2c2; margin-right: 8px;" />
        <span class="help-title">快捷报价使用说明</span>
        <a-button 
          type="link" 
          size="small" 
          @click="toggleHelp"
          style="margin-left: auto;"
        >
          {{ showHelp ? '收起' : '展开' }}
          <a-icon :type="showHelp ? 'up' : 'down'" />
        </a-button>
      </div>
      
      <div v-show="showHelp" class="help-content">
        <div class="help-section">
          <h4><a-icon type="bulb" /> 适用场景</h4>
          <p>• 已有简易文字行程，只需要快速报价</p>
          <p>• 心中有大致行程，想简单核算成本</p>
          <p>• 不在电脑前，需要手机便捷操作</p>
        </div>

        <div class="help-section">
          <h4><a-icon type="ordered-list" /> 使用步骤</h4>
          <div class="step-list">
            <div class="step-item">
              <span class="step-number">1</span>
              <span>点击"新建报价"按钮</span>
            </div>
            <div class="step-item">
              <span class="step-number">2</span>
              <span>选择数据来源：<strong>粘贴内容</strong>（文字行程）、<strong>现有行程</strong>、<strong>文档导入</strong>或<strong>空白新建</strong></span>
            </div>
            <div class="step-item">
              <span class="step-number">3</span>
              <span>人工修改完善内容</span>
            </div>
            <div class="step-item">
              <span class="step-number">4</span>
              <span>导出Excel、复制文字版或保存报价</span>
            </div>
          </div>
        </div>

        <div class="help-section">
          <h4><a-icon type="tips-and-updates" /> 使用技巧</h4>
          <p>• 景点、酒店支持从数据库选择（点击输入框后的图标）</p>
          <p>• 先设置人数，门票和房间数会自动更新</p>
          <p>• 支持手机端操作，可收藏网址随时核价</p>
        </div>
      </div>
    </a-card>

  </div>
</template>

<script>
import { getAction, deleteAction, postAction } from '@/api/manage'
import moment from 'moment'


export default {
  name: 'QuickQuoteList',
  


  data() {
    return {
      // 搜索表单
      searchForm: {
        quoteName: ''
      },

      // 表格数据
      dataSource: [],
      loading: false,

      // 帮助栏展开状态
      showHelp: true,
      
      // 分页
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
      },



      // 表格列定义
      columns: [
        {
          title: '报价名称',
          dataIndex: 'quoteName',
          width: 200,
          scopedSlots: { customRender: 'quoteName' }
        },
        {
          title: '来源',
          dataIndex: 'sourceType',
          width: 100,
          scopedSlots: { customRender: 'sourceType' }
        },
        {
          title: '人数',
          dataIndex: 'personCount',
          width: 80,
          scopedSlots: { customRender: 'personCount' }
        },
        {
          title: '总金额',
          dataIndex: 'totalAmount',
          width: 120,
          scopedSlots: { customRender: 'totalAmount' },
          sorter: true
        },
        {
          title: '人均金额',
          dataIndex: 'perPersonAmount',
          width: 120,
          scopedSlots: { customRender: 'perPersonAmount' },
          sorter: true
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          width: 150,
          scopedSlots: { customRender: 'createTime' },
          sorter: true
        },
        {
          title: '操作',
          width: 180,
          align: 'center',
          scopedSlots: { customRender: 'action' }
        }
      ]
    }
  },

  mounted() {
    this.loadData()
  },

  methods: {
    // 加载数据
    async loadData() {
      this.loading = true
      try {
        const params = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }

        const response = await getAction('/biz/bizQuickQuote/list', params)
        
        if (response.success) {
          const pageData = response.result
          this.dataSource = pageData.records || []
          this.pagination.total = pageData.total || 0
        } else {
          this.$message.error('加载数据失败')
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.current = 1
      this.loadData()
    },



    // 表格变化
    handleTableChange(pagination, filters, sorter) {
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.loadData()
    },

    // 新建报价
    goToCreate() {
      this.$router.push('/biz/quick-quote')
    },



    // 编辑报价
    handleEdit(record) {
      this.$router.push({
        path: '/biz/quick-quote',
        query: { id: record.id }
      })
    },

    // 复制报价
    async handleCopy(record) {
      try {
        const response = await postAction('/biz/bizQuickQuote/copy', { id: record.id })
        
        if (response.success) {
          this.$message.success('复制成功')
          this.loadData() // 刷新页面数据
        } else {
          this.$message.error(response.message || '复制失败')
        }
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败')
      }
    },

    // 删除报价
    async handleDelete(record) {
      try {
        const response = await deleteAction('/biz/bizQuickQuote/delete', { id: record.id })
        
        if (response.success) {
          this.$message.success('删除成功')
          this.loadData()
        } else {
          this.$message.error(response.message || '删除失败')
        }
      } catch (error) {
        console.error('删除失败:', error)
        this.$message.error('删除失败')
      }
    },



    // 获取来源类型颜色
    getSourceTypeColor(type) {
      const colorMap = {
        paste: 'blue',
        trip: 'green',
        file: 'orange',
        manual: 'purple'
      }
      return colorMap[type] || 'default'
    },

    // 获取来源类型名称
    getSourceTypeName(type) {
      const nameMap = {
        paste: '粘贴内容',
        trip: '现有行程',
        file: '文档导入',
        manual: '手动创建'
      }
      return nameMap[type] || type
    },

    // 格式化价格
    formatPrice(price) {
      return `￥${(price || 0).toFixed(2)}`
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return moment(date).format('YYYY-MM-DD HH:mm')
    },

    // 切换帮助栏显示状态
    toggleHelp() {
      this.showHelp = !this.showHelp
    }
  }
}
</script>

<style lang="less" scoped>
  .quick-quote-list {
  .page-header {
    margin-bottom: 16px;
    padding-right: 16px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      h2 {
        margin: 0;
        color: #262626;
      }
    }
  }

  .search-card,
  .table-card,
  .help-card {
    margin-bottom: 16px;
    margin-right: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .help-card {
    .help-header {
      display: flex;
      align-items: center;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 16px;
      
      .help-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
    }

    .help-content {
      .help-section {
        margin-bottom: 20px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        h4 {
          margin-bottom: 8px;
          color: #13c2c2;
          font-size: 14px;
          font-weight: 500;
          
          .anticon {
            margin-right: 6px;
          }
        }
        
        p {
          margin: 4px 0;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
          padding-left: 16px;
        }
      }

      .step-list {
        padding-left: 16px;
        
        .step-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 8px;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
          
          .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background-color: #13c2c2;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
            flex-shrink: 0;
            margin-top: 1px;
          }
          
          strong {
            color: #13c2c2;
          }
        }
      }
    }
  }

  .amount-text {
    font-weight: 500;
    color: #f5222d;
  }

  .action-buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
    padding: 0 8px;
    
    .ant-btn-link {
      padding: 4px 6px;
      margin: 0;
      font-size: 14px;
      min-width: auto;
      
      &.danger-link {
        color: #ff4d4f;
        
        &:hover {
          color: #ff7875;
        }
      }
    }
    
    .ant-divider-vertical {
      height: 12px;
      margin: 0 4px;
      flex-shrink: 0;
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .quick-quote-list {
    .page-header {
      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        
        h2 {
          text-align: center;
        }
      }
    }

    .search-card {
      ::v-deep .ant-form-inline .ant-form-item {
        margin-bottom: 8px;
        margin-right: 0;
      }
    }

    .help-card {
      .help-header {
        .help-title {
          font-size: 14px;
        }
      }

      .help-content {
        .help-section {
          h4 {
            font-size: 13px;
          }
          
          p {
            font-size: 13px;
            padding-left: 12px;
          }
        }

        .step-list {
          padding-left: 12px;
          
          .step-item {
            font-size: 13px;
            
            .step-number {
              width: 18px;
              height: 18px;
              font-size: 11px;
            }
          }
        }
      }
    }

    .action-buttons {
      .ant-btn-link {
        padding: 0 4px;
        font-size: 12px;
      }
      
      .ant-divider-vertical {
        height: 10px;
        margin: 0 2px;
      }
    }
  }
}

// 斑马纹表格样式
::v-deep .ant-table-tbody > tr:nth-child(even) > td {
  background-color: #fafafa;
}

::v-deep .ant-table-tbody > tr:nth-child(odd) > td {
  background-color: #ffffff;
}

// 鼠标悬停效果
::v-deep .ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff !important;
}
</style> 