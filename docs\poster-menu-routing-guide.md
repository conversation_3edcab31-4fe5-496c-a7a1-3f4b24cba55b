# 海报功能菜单和路由配置指南

## 概述

本文档详细说明了海报生成功能的菜单权限配置和前端路由设置，包括权限控制、导航管理和面包屑配置。

## 菜单权限结构

### 权限层级

```
海报管理 (poster)
├── 海报模板管理 (poster:template:list)
│   ├── 新增模板 (poster:template:add)
│   ├── 编辑模板 (poster:template:edit)
│   ├── 删除模板 (poster:template:delete)
│   └── 查看模板详情 (poster:template:detail)
└── 我的海报 (poster:user:list)
    ├── 创建海报 (poster:user:add)
    ├── 编辑海报 (poster:user:edit)
    ├── 删除海报 (poster:user:delete)
    ├── 生成海报 (poster:user:generate)
    └── 导入行程数据 (poster:user:import)
```

### 数据库权限配置

权限数据已包含在 `server/sql/update.sql` 中，包括：

1. **一级菜单**: 海报管理
   - URL: `/poster`
   - 组件: `layouts/RouteView`
   - 图标: `picture`

2. **子菜单**: 海报模板管理、我的海报
   - 对应具体的Vue组件
   - 包含相应的权限标识

3. **按钮权限**: 各种操作的细粒度权限控制

4. **隐藏路由**: 编辑页面路由（不在菜单中显示）

## 前端路由配置

### 路由定义 (router.config.js)

```javascript
// 海报相关路由
{
  path: '/poster/template',
  name: 'poster-template-list',
  component: () => import('@/views/biz/BizPosterTemplate/BizPosterTemplateList'),
  meta: { title: '海报模板管理', icon: 'layout', keepAlive: true }
},
{
  path: '/poster/template/edit',
  name: 'poster-template-edit',
  component: () => import('@/views/biz/BizPosterTemplate/modules/PosterTemplateEditor'),
  meta: { title: '海报模板编辑', icon: 'edit', hidden: true }
},
{
  path: '/poster/user',
  name: 'poster-user-list',
  component: () => import('@/views/biz/BizUserPoster/BizUserPosterList'),
  meta: { title: '我的海报', icon: 'file-image', keepAlive: true }
},
{
  path: '/poster/user/edit',
  name: 'poster-user-edit',
  component: () => import('@/views/biz/BizUserPoster/PosterEditor'),
  meta: { title: '海报编辑', icon: 'edit', hidden: true }
}
```

### 路由特性说明

- **keepAlive**: 列表页面启用缓存，提升用户体验
- **hidden**: 编辑页面隐藏，不在菜单中显示
- **meta.title**: 页面标题，用于面包屑和浏览器标题

## 导航工具类

### posterNavigation.js

提供统一的导航管理功能：

```javascript
// 页面跳转
goToTemplateList(router)
goToTemplateEdit(router, options)
goToUserPosterList(router)
goToUserPosterEdit(router, options)

// 页面标题
getPosterPageTitle(routeName, query)

// 面包屑导航
getPosterBreadcrumb(routeName, query)

// 权限检查
hasPosterPermission(permissions, permission)
getPosterPermissions()
```

### 使用示例

```javascript
import { goToTemplateEdit, getPosterBreadcrumb } from '@/utils/posterNavigation'

// 跳转到编辑页面
goToTemplateEdit(this.$router, { 
  mode: 'edit', 
  id: templateId 
})

// 获取面包屑
computed: {
  breadcrumbItems() {
    return getPosterBreadcrumb(this.$route.name, this.$route.query)
  }
}
```

## 权限控制

### 前端权限指令

使用 `v-has` 指令进行权限控制：

```vue
<!-- 按钮权限 -->
<a-button v-has="'poster:template:add'">新增模板</a-button>

<!-- 菜单项权限 -->
<a-menu-item v-has="'poster:user:delete'">删除</a-menu-item>

<!-- 分割线权限（配合按钮使用） -->
<a-divider type="vertical" v-has="'poster:template:edit'" />
```

### 权限常量

```javascript
const POSTER_PERMISSIONS = {
  // 模板管理权限
  TEMPLATE_LIST: 'poster:template:list',
  TEMPLATE_ADD: 'poster:template:add',
  TEMPLATE_EDIT: 'poster:template:edit',
  TEMPLATE_DELETE: 'poster:template:delete',
  TEMPLATE_DETAIL: 'poster:template:detail',
  
  // 用户海报权限
  USER_LIST: 'poster:user:list',
  USER_ADD: 'poster:user:add',
  USER_EDIT: 'poster:user:edit',
  USER_DELETE: 'poster:user:delete',
  USER_GENERATE: 'poster:user:generate',
  USER_IMPORT: 'poster:user:import'
}
```

## 面包屑导航

### 自动生成面包屑

每个页面都使用计算属性自动生成面包屑：

```vue
<template>
  <a-breadcrumb>
    <a-breadcrumb-item v-for="(item, index) in breadcrumbItems" :key="index">
      <a-icon :type="item.icon" v-if="item.icon" />
      <router-link :to="item.path" v-if="index < breadcrumbItems.length - 1">
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script>
computed: {
  breadcrumbItems() {
    return getPosterBreadcrumb(this.$route.name, this.$route.query)
  }
}
</script>
```

### 面包屑结构

- **首页** → **海报模板管理**
- **首页** → **海报模板管理** → **编辑海报模板**
- **首页** → **我的海报**
- **首页** → **我的海报** → **制作海报**

## 页面标题管理

### 动态标题

根据路由和参数动态设置页面标题：

```javascript
// 根据编辑模式设置标题
const title = getPosterPageTitle(this.$route.name, this.$route.query)
// 结果: "新增海报模板" | "编辑海报模板" | "制作海报" | "编辑海报"

// 设置浏览器标题
document.title = `${title} - 海报管理系统`
```

## 部署和配置

### 1. 数据库更新

执行 `server/sql/update.sql` 中的权限配置SQL：

```sql
-- 海报功能菜单权限配置已包含在update.sql中
-- 包括一级菜单、子菜单、按钮权限和隐藏路由
```

### 2. 前端路由

路由配置已包含在 `page/src/config/router.config.js` 中，无需额外配置。

### 3. 权限分配

在系统管理中为相应角色分配海报相关权限：

1. 管理员：所有海报权限
2. 普通用户：用户海报相关权限
3. 只读用户：仅查看权限

## 开发注意事项

### 1. 权限一致性

- 前端权限控制仅用于UI展示
- 后端必须进行相同的权限验证
- 权限标识必须与数据库配置一致

### 2. 路由参数

- 编辑页面通过query参数传递状态
- 支持的参数：`mode`, `id`, `templateId`, `tripId`
- 参数变化时需要更新页面状态

### 3. 导航一致性

- 所有页面跳转使用导航工具类
- 面包屑使用统一的生成逻辑
- 保持URL结构的一致性

### 4. 缓存管理

- 列表页面启用keepAlive缓存
- 编辑页面不缓存，确保数据最新
- 路由切换时正确处理缓存刷新

## 故障排除

### 常见问题

1. **菜单不显示**
   - 检查权限配置是否正确
   - 确认用户是否有相应权限
   - 验证SQL是否正确执行

2. **路由跳转失败**
   - 检查路由配置是否正确
   - 确认组件路径是否存在
   - 验证路由参数格式

3. **权限控制失效**
   - 检查v-has指令使用是否正确
   - 确认权限标识是否匹配
   - 验证用户权限数据

4. **面包屑显示异常**
   - 检查路由名称是否正确
   - 确认导航工具类配置
   - 验证计算属性逻辑