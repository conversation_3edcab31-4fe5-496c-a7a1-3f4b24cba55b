# 海报功能菜单配置说明

## 概述

海报功能的菜单配置采用数据库驱动的方式，通过 `sys_permission` 表来管理菜单权限和路由配置。

## 菜单结构

```
海报管理 (/poster)
├── 海报模板管理 (/poster/template)
│   ├── 新增模板 (poster:template:add)
│   ├── 编辑模板 (poster:template:edit)
│   ├── 删除模板 (poster:template:delete)
│   └── 查看模板详情 (poster:template:detail)
├── 我的海报 (/poster/user)
│   ├── 创建海报 (poster:user:add)
│   ├── 编辑海报 (poster:user:edit)
│   ├── 删除海报 (poster:user:delete)
│   ├── 生成海报 (poster:user:generate)
│   └── 导入行程数据 (poster:user:import)
├── 海报模板编辑 (/poster/template/edit) [隐藏菜单]
└── 海报编辑 (/poster/user/edit) [隐藏菜单]
```

## 权限配置

### 菜单类型说明
- `menu_type = 0`: 一级菜单
- `menu_type = 1`: 子菜单/页面
- `menu_type = 2`: 按钮权限

### 权限编码规范
- 模板管理权限: `poster:template:*`
- 用户海报权限: `poster:user:*`

### 路由配置
- `is_route = 1`: 生成路由
- `hidden = 0`: 显示在菜单中
- `hidden = 1`: 隐藏菜单项（编辑页面等）

## 安装步骤

1. 执行 SQL 脚本创建菜单权限：
   ```bash
   mysql -u username -p database_name < server/sql/poster_menu_permissions.sql
   ```

2. 为相关角色分配权限：
   - 管理员角色：分配所有海报相关权限
   - 普通用户角色：分配用户海报相关权限

3. 重启应用或刷新权限缓存

## 前端路由配置

前端路由配置位于 `page/src/config/router.config.js`，包含以下路由：

- `/poster/template` - 海报模板管理列表
- `/poster/template/edit` - 海报模板编辑器
- `/poster/user` - 用户海报管理列表  
- `/poster/user/edit` - 用户海报编辑器

## 导航和面包屑

### 面包屑导航结构
- 首页 > 海报模板管理 > 海报模板编辑
- 首页 > 我的海报 > 海报编辑

### 页面标题动态设置
- 根据路由参数 `action` 和 `title` 动态设置页面标题
- 支持浏览器标题同步更新

## 参数传递

### 编辑页面参数
- `action`: 操作类型 (`add`/`edit`/`create`)
- `id`: 记录ID（编辑模式）
- `title`: 自定义页面标题

### 示例
```javascript
// 新增模板
this.$router.push({
  path: '/poster/template/edit',
  query: { 
    action: 'add',
    title: '新增海报模板'
  }
})

// 编辑模板
this.$router.push({
  path: '/poster/template/edit',
  query: { 
    id: record.id,
    action: 'edit',
    title: '编辑海报模板'
  }
})
```

## 权限控制

### 按钮权限控制
在组件中使用权限指令控制按钮显示：

```vue
<a-button v-if="$auth('poster:template:add')" @click="handleAdd">
  新增模板
</a-button>
```

### 菜单权限控制
菜单权限通过后端 API 动态加载，前端根据用户权限自动生成菜单结构。

## 注意事项

1. 菜单配置修改后需要重新登录或刷新权限缓存
2. 隐藏菜单项仍需要配置权限，用于页面访问控制
3. 路由路径需要与菜单配置中的 `url` 字段保持一致
4. 组件路径需要与菜单配置中的 `component` 字段保持一致