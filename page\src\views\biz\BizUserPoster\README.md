# 用户海报管理模块

## 功能概述

用户海报管理模块提供了完整的用户海报制作、编辑、管理功能，包括：

- 用户海报列表管理
- 基于模板的海报编辑器
- 行程数据导入功能
- 多格式导出功能

## 页面结构

### 1. 用户海报列表页面 (BizUserPosterList.vue)

**路由路径**: `/poster/user`
**权限要求**: `poster:user:list`

**功能特性**:
- 卡片式海报展示
- 搜索和筛选功能
- 批量选择和导出
- 制作、编辑、删除操作
- 多格式下载（PNG、JPG、PDF）

**操作权限**:
- 制作海报: `poster:user:add`
- 编辑海报: `poster:user:edit`
- 删除海报: `poster:user:delete`

### 2. 海报编辑器 (PosterEditor.vue)

**路由路径**: `/poster/user/edit`
**权限要求**: `poster:user:edit`

**功能特性**:
- 基于Fabric.js的可视化编辑器
- 模板选择和加载
- 行程数据导入
- 实时预览和编辑
- 多格式导出

**URL参数**:
- `mode`: 编辑模式 (`add`新增, `edit`编辑)
- `id`: 海报ID（编辑时）
- `templateId`: 模板ID（基于模板创建时）
- `tripId`: 行程ID（导入行程数据时）

### 3. 行程数据导入弹窗 (TripImportModal.vue)

**功能特性**:
- 行程列表选择
- 数据预览和映射
- 自动变量填充
- 手动调整功能

## 导航和面包屑

模块使用统一的导航工具类 `@/utils/posterNavigation.js`：

```javascript
import { goToUserPosterEdit, getPosterBreadcrumb } from '@/utils/posterNavigation'

// 跳转到编辑页面
goToUserPosterEdit(this.$router, { mode: 'add' })

// 获取面包屑数据
const breadcrumb = getPosterBreadcrumb(this.$route.name, this.$route.query)
```

## 权限配置

### 菜单权限

相关权限配置已包含在 `server/sql/update.sql` 中：

- 我的海报（子菜单）
- 创建、编辑、删除、生成、导入等按钮权限

### 前端权限控制

```vue
<a-button v-has="'poster:user:add'">制作海报</a-button>
<a-button v-has="'poster:user:edit'">编辑</a-button>
<a-menu-item v-has="'poster:user:delete'">删除</a-menu-item>
```

## 核心功能

### 1. 海报制作流程

1. 选择模板或从空白开始
2. 编辑文本、图片等元素
3. 可选择导入行程数据自动填充
4. 实时预览效果
5. 保存和导出

### 2. 行程数据导入

- 支持从行程管理系统导入数据
- 自动映射常用字段（行程名称、天数、价格等）
- 支持手动调整和补充

### 3. 多格式导出

- **PNG**: 支持透明背景
- **JPG**: 较小文件大小
- **PDF**: 适合打印
- 支持高分辨率导出
- 批量导出功能

## 组件依赖

- **Fabric.js**: 画布编辑功能
- **html2canvas**: 截图生成
- **jsPDF**: PDF导出
- **JSZip**: 批量导出压缩
- **Ant Design Vue**: UI组件库

## 开发注意事项

1. 编辑器性能优化：大图片处理需要注意内存使用
2. 导出功能：不同格式需要不同的处理逻辑
3. 权限控制：确保用户只能操作自己的海报
4. 数据同步：编辑过程中定期保存，避免数据丢失
5. 浏览器兼容性：某些Canvas功能可能存在兼容性问题