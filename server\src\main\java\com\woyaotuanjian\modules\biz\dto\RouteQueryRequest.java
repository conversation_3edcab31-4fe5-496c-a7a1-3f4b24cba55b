package com.woyaotuanjian.modules.biz.dto;

import lombok.Data;

/**
 * 路线查询请求DTO
 */
@Data
public class RouteQueryRequest {
    
    /**
     * 起点纬度
     */
    private Double originLat;
    
    /**
     * 起点经度
     */
    private Double originLng;
    
    /**
     * 终点纬度
     */
    private Double destLat;
    
    /**
     * 终点经度
     */
    private Double destLng;
    
    /**
     * 起点ID（用于查询数据库配置）
     */
    private Long originId;
    
    /**
     * 起点类型（用于查询数据库配置）
     */
    private String originType;
    
    /**
     * 终点ID（用于查询数据库配置）
     */
    private Long destinationId;
    
    /**
     * 终点类型（用于查询数据库配置）
     */
    private String destinationType;
    
    /**
     * 行程ID（用于查询数据库配置）
     */
    private Long tripId;
} 