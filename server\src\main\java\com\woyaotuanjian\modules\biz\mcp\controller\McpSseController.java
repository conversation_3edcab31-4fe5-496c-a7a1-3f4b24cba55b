package com.woyaotuanjian.modules.biz.mcp.controller;

import com.woyaotuanjian.modules.biz.mcp.dto.*;
import com.woyaotuanjian.modules.biz.mcp.service.McpTravelToolService;
import com.woyaotuanjian.modules.biz.mcp.util.McpConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP SSE控制器
 * 提供简化的MCP端点（暂时不实现完整SSE功能）
 */
@Api(tags = "MCP SSE")
@RestController
@RequestMapping("/mcp/sse")
@ConditionalOnProperty(name = "mcp.travel.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class McpSseController {

    @Autowired
    private McpTravelToolService mcpTravelToolService;

    /**
     * SSE连接端点
     */
    @ApiOperation(value = "建立SSE连接", notes = "建立Server-Sent Events连接")
    @GetMapping(value = "", produces = "text/event-stream")
    public String sseConnect(HttpServletRequest request) {
        log.info("收到SSE连接请求: {}", request.getRequestURI());
        
        // 返回SSE格式的连接信息
        StringBuilder response = new StringBuilder();
        response.append("event: connection\n");
        response.append("data: {\"type\":\"connection\",\"status\":\"connected\",\"service\":\"MCP Travel Query Service\",\"timestamp\":").append(System.currentTimeMillis()).append("}\n\n");
        response.append("event: info\n");
        response.append("data: {\"message\":\"MCP SSE连接已建立\",\"endpoints\":[\"/mcp/initialize\",\"/mcp/tools/list\",\"/mcp/tools/call\"]}\n\n");
        
        return response.toString();
    }

    /**
     * 获取服务状态
     */
    @ApiOperation(value = "获取服务状态", notes = "获取MCP SSE服务状态")
    @GetMapping(value = "/status", produces = "application/json")
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "MCP SSE Service");
        status.put("status", "UP");
        
        Map<String, String> serverInfo = new HashMap<>();
        serverInfo.put("name", McpConstants.SERVER_NAME);
        serverInfo.put("version", McpConstants.SERVER_VERSION);
        status.put("serverInfo", serverInfo);
        
        return status;
    }

    /**
     * 处理SSE端点的POST请求（MCP协议）
     */
    @ApiOperation(value = "处理MCP请求", notes = "通过SSE端点处理MCP协议请求")
    @PostMapping(value = "", produces = "application/json")
    public Map<String, Object> handleMcpRequest(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        log.info("收到SSE端点MCP请求: {}", httpRequest.getRequestURI());
        log.info("请求内容: {}", request);
        
        Map<String, Object> response = new HashMap<>();
        response.put("jsonrpc", "2.0");
        response.put("timestamp", System.currentTimeMillis());
        
        if (request != null && request.containsKey("method")) {
            String method = (String) request.get("method");
            String id = request.get("id") != null ? request.get("id").toString() : "unknown";
            
            try {
                switch (method) {
                    case "initialize":
                        return handleInitialize(request, id);
                    case "tools/list":
                        return handleToolsList(request, id);
                    case "tools/call":
                        return handleToolsCall(request, id);
                    default:
                        Map<String, Object> error = new HashMap<>();
                        error.put("code", -32601);
                        error.put("message", "未知方法: " + method);
                        response.put("id", id);
                        response.put("error", error);
                        return response;
                }
            } catch (Exception e) {
                log.error("处理MCP请求失败: method={}, id={}", method, id, e);
                Map<String, Object> error = new HashMap<>();
                error.put("code", -32603);
                error.put("message", "内部服务器错误: " + e.getMessage());
                response.put("id", id);
                response.put("error", error);
                return response;
            }
        } else {
            Map<String, Object> result = new HashMap<>();
            result.put("message", "这是MCP SSE端点");
            result.put("service", "MCP Travel Query Service");
            result.put("endpoints", new String[]{"/initialize", "/tools/list", "/tools/call"});
            response.put("result", result);
            return response;
        }
    }

    /**
     * 简化的工具列表端点
     */
    @ApiOperation(value = "获取工具列表", notes = "通过SSE端点获取工具列表")
    @PostMapping(value = "/tools/list", produces = "application/json")
    public McpResponse listTools(@RequestBody McpRequest request) {
        try {
            List<McpTool> tools = mcpTravelToolService.getAvailableTools();
            
            Map<String, Object> result = new HashMap<>();
            result.put("tools", tools);
            
            return McpResponse.success(request.getId(), result);
            
        } catch (Exception e) {
            McpError error = new McpError(-32603, "获取工具列表失败: " + e.getMessage());
            return McpResponse.error(request.getId(), error);
        }
    }

    /**
     * 简化的工具调用端点
     */
    @ApiOperation(value = "调用工具", notes = "通过SSE端点调用工具")
    @PostMapping(value = "/tools/call", produces = "application/json")
    public McpResponse callTool(@RequestBody McpRequest request) {
        try {
            Map<String, Object> params = request.getParams();
            String toolName = (String) params.get("name");
            @SuppressWarnings("unchecked")
            Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
            
            if (arguments == null) {
                arguments = new HashMap<>();
            }
            
            McpToolCallResult result = mcpTravelToolService.callTool(toolName, arguments);
            return McpResponse.success(request.getId(), result);
            
        } catch (Exception e) {
            McpError error = new McpError(-32603, "工具调用失败: " + e.getMessage());
            return McpResponse.error(request.getId(), error);
        }
    }

    /**
     * 处理初始化请求
     */
    private Map<String, Object> handleInitialize(Map<String, Object> request, String id) {
        try {
            log.info("SSE端点处理初始化请求: id={}", id);
            
            // 创建服务器信息
            Map<String, String> serverInfo = new HashMap<>();
            serverInfo.put("name", "MCP Travel Query Service");
            serverInfo.put("version", "1.0.0");
            
            // 创建服务器能力声明
            Map<String, Object> capabilities = new HashMap<>();
            Map<String, Object> tools = new HashMap<>();
            tools.put("listChanged", false);
            capabilities.put("tools", tools);
            
            // 创建初始化结果
            Map<String, Object> result = new HashMap<>();
            result.put("protocolVersion", "2025-06-18");
            result.put("capabilities", capabilities);
            result.put("serverInfo", serverInfo);
            
            Map<String, Object> response = new HashMap<>();
            response.put("jsonrpc", "2.0");
            response.put("id", id);
            response.put("result", result);
            
            log.info("SSE端点初始化成功: id={}", id);
            return response;
            
        } catch (Exception e) {
            log.error("SSE端点初始化失败: id={}", id, e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("jsonrpc", "2.0");
            response.put("id", id);
            
            Map<String, Object> error = new HashMap<>();
            error.put("code", -32603);
            error.put("message", "初始化失败: " + e.getMessage());
            response.put("error", error);
            
            return response;
        }
    }

    /**
     * 处理工具列表请求
     */
    private Map<String, Object> handleToolsList(Map<String, Object> request, String id) {
        try {
            log.info("SSE端点处理工具列表请求: id={}", id);
            
            List<McpTool> tools = mcpTravelToolService.getAvailableTools();
            
            Map<String, Object> result = new HashMap<>();
            result.put("tools", tools);
            
            Map<String, Object> response = new HashMap<>();
            response.put("jsonrpc", "2.0");
            response.put("id", id);
            response.put("result", result);
            
            log.info("SSE端点工具列表返回成功: 共{}个工具", tools.size());
            return response;
            
        } catch (Exception e) {
            log.error("SSE端点获取工具列表失败: id={}", id, e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("jsonrpc", "2.0");
            response.put("id", id);
            
            Map<String, Object> error = new HashMap<>();
            error.put("code", -32603);
            error.put("message", "获取工具列表失败: " + e.getMessage());
            response.put("error", error);
            
            return response;
        }
    }

    /**
     * 处理工具调用请求
     */
    private Map<String, Object> handleToolsCall(Map<String, Object> request, String id) {
        try {
            log.info("SSE端点处理工具调用请求: id={}", id);
            
            @SuppressWarnings("unchecked")
            Map<String, Object> params = (Map<String, Object>) request.get("params");
            String toolName = (String) params.get("name");
            @SuppressWarnings("unchecked")
            Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");
            
            if (arguments == null) {
                arguments = new HashMap<>();
            }
            
            McpToolCallResult result = mcpTravelToolService.callTool(toolName, arguments);
            
            Map<String, Object> response = new HashMap<>();
            response.put("jsonrpc", "2.0");
            response.put("id", id);
            response.put("result", result);
            
            log.info("SSE端点工具调用成功: name={}", toolName);
            return response;
            
        } catch (Exception e) {
            log.error("SSE端点工具调用失败: id={}", id, e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("jsonrpc", "2.0");
            response.put("id", id);
            
            Map<String, Object> error = new HashMap<>();
            error.put("code", -32603);
            error.put("message", "工具调用失败: " + e.getMessage());
            response.put("error", error);
            
            return response;
        }
    }
}