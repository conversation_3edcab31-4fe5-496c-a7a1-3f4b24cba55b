-- 海报素材分类表
CREATE TABLE `poster_material_category` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT 0 COMMENT '父分类ID，0为顶级分类',
  `icon` varchar(100) COMMENT '分类图标',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='海报素材分类表';

-- 海报素材表
CREATE TABLE `poster_material` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '素材名称',
  `type` varchar(20) NOT NULL COMMENT '素材类型：image/svg/font',
  `category_id` bigint DEFAULT 0 COMMENT '分类ID',
  `file_url` varchar(500) COMMENT '文件URL（图片素材用）',
  `thumbnail_url` varchar(500) COMMENT '缩略图URL',
  `svg_content` text COMMENT 'SVG内容（SVG素材用）',
  `width` int DEFAULT 0 COMMENT '宽度',
  `height` int DEFAULT 0 COMMENT '高度',
  `file_size` bigint DEFAULT 0 COMMENT '文件大小（字节）',
  `tags` varchar(200) COMMENT '标签，逗号分隔',
  `description` varchar(255) COMMENT '描述',
  `download_count` int DEFAULT 0 COMMENT '使用次数',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态：1启用 0禁用',
  `create_by` varchar(50) COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_type` (`type`),
  INDEX `idx_category` (`category_id`),
  INDEX `idx_status` (`status`)
) COMMENT='海报素材表';

-- 初始化分类数据
INSERT INTO `poster_material_category` (`name`, `parent_id`, `icon`, `sort_order`) VALUES
('图片素材', 0, 'picture', 1),
('SVG图标', 0, 'appstore', 2),
('字体素材', 0, 'font-size', 4),
('人物', 1, 'user', 11),
('风景', 1, 'environment', 12),
('动物', 1, 'bug', 13),
('商务', 2, 'bank', 21),
('社交', 2, 'message', 22),
('中文字体', 4, 'font-colors', 41),
('英文字体', 4, 'font-size', 42),
('艺术字体', 4, 'highlight', 43);

-- 初始化字体素材数据（系统默认字体）
INSERT INTO `poster_material` (`name`, `type`, `category_id`, `file_url`, `tags`, `description`, `sort_order`, `status`) VALUES
('微软雅黑', 'font', 41, '', '中文,无衬线,现代', '微软雅黑字体，适合现代设计', 1, 1),
('宋体', 'font', 41, '', '中文,衬线,传统', '宋体字体，适合正式文档', 2, 1),
('黑体', 'font', 41, '', '中文,无衬线,粗体', '黑体字体，醒目大方', 3, 1),
('楷体', 'font', 41, '', '中文,手写,传统', '楷体字体，优雅传统', 4, 1),
('Arial', 'font', 42, '', '英文,无衬线,现代', 'Arial字体，清晰易读', 11, 1),
('Times New Roman', 'font', 42, '', '英文,衬线,传统', 'Times New Roman字体，经典衬线', 12, 1),
('Helvetica', 'font', 42, '', '英文,无衬线,简洁', 'Helvetica字体，简洁现代', 13, 1),
('Georgia', 'font', 42, '', '英文,衬线,优雅', 'Georgia字体，优雅易读', 14, 1);

-- 初始化一些示例SVG图标
INSERT INTO `poster_material` (`name`, `type`, `category_id`, `svg_content`, `tags`, `description`) VALUES
('用户图标', 'svg', 21, '<svg viewBox="0 0 24 24"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="{{color}}"/></svg>', '用户,人物,商务', '用户头像图标'),
('邮件图标', 'svg', 22, '<svg viewBox="0 0 24 24"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z" fill="{{color}}"/></svg>', '邮件,联系,社交', '邮件图标'),
('电话图标', 'svg', 22, '<svg viewBox="0 0 24 24"><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z" fill="{{color}}"/></svg>', '电话,联系,通信', '电话图标');