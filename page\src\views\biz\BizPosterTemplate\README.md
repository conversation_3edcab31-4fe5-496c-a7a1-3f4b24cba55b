# 海报模板管理模块

## 功能概述

海报模板管理模块提供了完整的海报模板创建、编辑、管理功能，包括：

- 海报模板列表管理
- 可视化模板编辑器
- 模板变量配置
- 权限控制

## 页面结构

### 1. 海报模板列表页面 (BizPosterTemplateList.vue)

**路由路径**: `/poster/template`
**权限要求**: `poster:template:list`

**功能特性**:
- 模板列表展示（支持缩略图预览）
- 搜索和筛选功能
- 新增、编辑、复制、删除操作
- 权限控制按钮显示

**操作权限**:
- 新增模板: `poster:template:add`
- 编辑模板: `poster:template:edit`
- 删除模板: `poster:template:delete`

### 2. 海报模板编辑器 (PosterTemplateEditor.vue)

**路由路径**: `/poster/template/edit`
**权限要求**: `poster:template:edit`

**功能特性**:
- 基于Fabric.js的可视化编辑器
- 支持文本、图片、形状等元素
- 模板变量配置
- 实时预览功能

**URL参数**:
- `mode`: 编辑模式 (`add`新增, `edit`编辑, `copy`复制)
- `id`: 模板ID（编辑时）
- `templateId`: 源模板ID（复制时）

## 导航和面包屑

模块使用统一的导航工具类 `@/utils/posterNavigation.js` 来管理页面跳转和面包屑导航：

```javascript
import { goToTemplateEdit, getPosterBreadcrumb } from '@/utils/posterNavigation'

// 跳转到编辑页面
goToTemplateEdit(this.$router, { mode: 'add' })

// 获取面包屑数据
const breadcrumb = getPosterBreadcrumb(this.$route.name, this.$route.query)
```

## 权限配置

### 菜单权限

系统通过 `sys_permission` 表配置菜单权限，相关SQL已包含在 `server/sql/update.sql` 中：

- 海报管理（一级菜单）
- 海报模板管理（子菜单）
- 相关按钮权限

### 前端权限控制

使用 `v-has` 指令进行权限控制：

```vue
<a-button v-has="'poster:template:add'">新增模板</a-button>
```

## 组件依赖

- **Fabric.js**: 画布编辑功能
- **Ant Design Vue**: UI组件库
- **JeecgListMixin**: 列表页面混入

## 开发注意事项

1. 所有页面跳转应使用导航工具类，保持一致性
2. 权限检查必须在前端和后端同时实现
3. 面包屑导航使用计算属性动态生成
4. 编辑器页面需要处理路由参数来确定编辑模式