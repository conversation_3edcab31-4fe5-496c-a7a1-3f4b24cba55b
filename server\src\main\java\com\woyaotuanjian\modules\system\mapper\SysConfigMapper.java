package com.woyaotuanjian.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.woyaotuanjian.modules.system.entity.SysConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 系统配置管理
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {

    /**
     * 根据配置键获取配置值
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValueByKey(@Param("configKey") String configKey);
}