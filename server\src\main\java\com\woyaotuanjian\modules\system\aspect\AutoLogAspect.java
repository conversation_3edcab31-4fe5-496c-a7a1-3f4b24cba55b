package com.woyaotuanjian.modules.system.aspect;

import com.alibaba.fastjson.JSONObject;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.common.util.IPUtils;
import com.woyaotuanjian.common.util.OConvertUtils;
import com.woyaotuanjian.common.util.SpringContextUtils;
import com.woyaotuanjian.modules.system.entity.SysLog;
import com.woyaotuanjian.modules.system.service.ISysLogService;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 系统日志，切面处理类
 */
@Aspect
@Component
public class AutoLogAspect {
	@Autowired
	private ISysLogService sysLogService;
	
	@Pointcut("@annotation(com.woyaotuanjian.common.aspect.annotation.AutoLog)")
	public void logPointCut() { 
		
	}

	@Around("logPointCut()")
	public Object around(ProceedingJoinPoint point) throws Throwable {
		long beginTime = System.currentTimeMillis();
		//执行方法
		Object result = point.proceed();
		//执行时长(毫秒)
		long time = System.currentTimeMillis() - beginTime;

		//保存日志
		saveSysLog(point, time);

		return result;
	}

	private void saveSysLog(ProceedingJoinPoint joinPoint, long time) {
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();

		SysLog sysLog = new SysLog();
		AutoLog syslog = method.getAnnotation(AutoLog.class);
		if(syslog != null){
			//注解上的描述,操作日志内容
			sysLog.setLogContent(syslog.value());
			sysLog.setLogType(syslog.logType());
			if(syslog.operateType()!=0){
				sysLog.setOperateType(syslog.operateType());
			}
		}

		//请求的方法名
//		String className = joinPoint.getTarget().getClass().getName();
//		String methodName = signature.getName();
//		sysLog.setMethod(className + "." + methodName + "()");

		//请求的参数
		Object[] args = joinPoint.getArgs();
		List<Object> logArgs = Arrays.asList(args).stream()
				//为了便于分析优化，把用户请求的细节暂时也显示到日志，对性能影响待考虑
				.map(arg -> {
					if (arg instanceof HttpServletRequest) {
						HttpServletRequest request = (HttpServletRequest) arg;
						return request.getParameterMap();
					} else if (arg instanceof HttpServletResponse) {
						return arg;
					} else if (arg instanceof org.springframework.web.multipart.MultipartFile) {
						// 对于MultipartFile，只记录文件信息，不序列化整个对象
						org.springframework.web.multipart.MultipartFile file = (org.springframework.web.multipart.MultipartFile) arg;
						java.util.Map<String, Object> fileInfo = new java.util.HashMap<>();
						fileInfo.put("fileName", file.getOriginalFilename());
						fileInfo.put("fileSize", file.getSize());
						fileInfo.put("contentType", file.getContentType());
						return fileInfo;
					} else {
						return arg;
					}
				})
				// 过滤掉不能序列化的httpServletRequest和response
				.filter(arg -> (!(arg instanceof HttpServletRequest) && !(arg instanceof HttpServletResponse)))
				.collect(Collectors.toList());
		String params = JSONObject.toJSONString(logArgs);
		// 过滤掉4字节UTF-8字符，防止插入数据库报错
		String filteredParams = OConvertUtils.filter4ByteUtf8(params);
		// 截断到60000字节以内，防止TEXT字段溢出
		try {
		    byte[] bytes = filteredParams != null ? filteredParams.getBytes("UTF-8") : new byte[0];
		    if (bytes.length > 60000) {
		        byte[] truncated = new byte[60000];
		        System.arraycopy(bytes, 0, truncated, 0, 60000);
		        // 重新按UTF-8解码，防止截断到半个字符
		        filteredParams = new String(truncated, "UTF-8");
		    }
		} catch (Exception e) {
		    // 忽略异常，保底不影响主流程
		}
		sysLog.setRequestParam(filteredParams);


		//获取request
		HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
		sysLog.setRequestUrl(request.getRequestURI());
		//设置IP地址
		sysLog.setIp(IPUtils.getIpAddr(request));

		//获取登录用户信息
		LoginUser sysUser = (LoginUser)SecurityUtils.getSubject().getPrincipal();
		if(sysUser!=null){
			sysLog.setUserid(sysUser.getUsername());
			sysLog.setUsername(sysUser.getRealname());

		}
		//耗时
		sysLog.setCostTime(time);
		sysLog.setCreateTime(new Date());
		//保存系统日志
		sysLogService.addAsyncLog(sysLog);
	}
}
