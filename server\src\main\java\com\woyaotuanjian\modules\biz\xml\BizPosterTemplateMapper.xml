<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.woyaotuanjian.modules.biz.mapper.BizPosterTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.woyaotuanjian.modules.biz.entity.BizPosterTemplate">
        <id column="id" property="id" />
        <result column="template_name" property="templateName" />
        <result column="template_desc" property="templateDesc" />
        <result column="template_data" property="templateData" />
        <result column="thumbnail_url" property="thumbnailUrl" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="status" property="status" />
        <result column="sort_order" property="sortOrder" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, template_name, template_desc, template_data, thumbnail_url, width, height, status, sort_order, create_by, create_time, update_by, update_time
    </sql>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE biz_poster_template 
        SET status = #{status}, update_time = #{updateTime}
        <if test="updateBy != null">
            , update_by = #{updateBy}
        </if>
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据状态查询模板列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_poster_template
        WHERE status = #{status}
        ORDER BY sort_order ASC, create_time DESC
    </select>

    <!-- 检查模板名称是否存在 -->
    <select id="checkTemplateNameExists" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM biz_poster_template
        WHERE template_name = #{templateName}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据ID列表查询启用的模板 -->
    <select id="selectEnabledByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM biz_poster_template
        WHERE status = 1
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY sort_order ASC, create_time DESC
    </select>

</mapper>