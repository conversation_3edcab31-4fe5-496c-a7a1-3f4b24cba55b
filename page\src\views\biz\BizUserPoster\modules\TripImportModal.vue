<template>
  <a-modal
    title="导入行程数据"
    :visible="visible"
    :width="800"
    :confirmLoading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
    :maskClosable="false">
    
    <div class="trip-import-content">
      <!-- 步骤指示器 -->
      <a-steps :current="currentStep" size="small" style="margin-bottom: 24px;">
        <a-step title="选择行程" />
        <a-step title="数据预览" />
        <a-step title="变量映射" />
      </a-steps>
      
      <!-- 第一步：选择行程 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="search-bar">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-input
                v-model="searchParams.tripName"
                placeholder="请输入行程名称"
                @pressEnter="searchTrips">
                <a-icon slot="prefix" type="search" />
              </a-input>
            </a-col>
            <a-col :span="8">
              <a-select
                v-model="searchParams.tripTag"
                placeholder="选择行程标签"
                allowClear
                style="width: 100%;">
                <a-select-option value="国内游">国内游</a-select-option>
                <a-select-option value="出境游">出境游</a-select-option>
                <a-select-option value="周边游">周边游</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-button type="primary" @click="searchTrips" :loading="searchLoading">
                搜索
              </a-button>
            </a-col>
          </a-row>
        </div>
        
        <div class="trip-list" v-if="trips.length > 0">
          <a-radio-group v-model="selectedTripId" style="width: 100%;">
            <div class="trip-item-container">
              <div 
                v-for="trip in trips" 
                :key="trip.id"
                class="trip-item"
                :class="{ selected: selectedTripId === trip.id }"
                @click="selectTrip(trip)">
                <a-radio :value="trip.id" style="margin-right: 12px;"></a-radio>
                <div class="trip-thumbnail">
                  <img v-if="trip.imgUrl" :src="trip.imgUrl" :alt="trip.tripName">
                  <div v-else class="no-image">
                    <a-icon type="picture" />
                  </div>
                </div>
                <div class="trip-info">
                  <h4>{{ trip.tripName }}</h4>
                  <p class="trip-desc">{{ trip.advantageDesc || '暂无描述' }}</p>
                  <div class="trip-meta">
                    <span class="trip-days">{{ trip.dayNum }}天</span>
                    <span class="trip-price" v-if="trip.price">¥{{ trip.price }}</span>
                    <span class="trip-tag" v-if="trip.tripTag">{{ trip.tripTag }}</span>
                  </div>
                </div>
              </div>
            </div>
          </a-radio-group>
          
          <!-- 分页 -->
          <div class="pagination-wrapper">
            <a-pagination
              v-model="pagination.current"
              :total="pagination.total"
              :pageSize="pagination.pageSize"
              :showSizeChanger="false"
              :showQuickJumper="true"
              @change="handlePageChange"
              size="small" />
          </div>
        </div>
        
        <div v-else-if="!searchLoading" class="empty-state">
          <a-empty description="暂无行程数据" />
        </div>
        
        <div v-if="searchLoading" class="loading-state">
          <a-spin size="large" />
        </div>
      </div>
      
      <!-- 第二步：数据预览 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="data-preview">
          <h3>行程数据预览</h3>
          <div class="preview-card">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="preview-image">
                  <img v-if="selectedTripData.imgUrl" :src="selectedTripData.imgUrl" :alt="selectedTripData.tripName">
                  <div v-else class="no-image-large">
                    <a-icon type="picture" />
                  </div>
                </div>
              </a-col>
              <a-col :span="16">
                <div class="preview-info">
                  <h2>{{ selectedTripData.tripName }}</h2>
                  <p class="full-name" v-if="selectedTripData.tripFullName">{{ selectedTripData.tripFullName }}</p>
                  <div class="info-grid">
                    <div class="info-item">
                      <label>旅游天数：</label>
                      <span>{{ selectedTripData.dayNum }}天</span>
                    </div>
                    <div class="info-item" v-if="selectedTripData.price">
                      <label>价格：</label>
                      <span>¥{{ selectedTripData.price }}</span>
                    </div>
                    <div class="info-item" v-if="selectedTripData.tripTag">
                      <label>行程标签：</label>
                      <span>{{ selectedTripData.tripTag }}</span>
                    </div>
                    <div class="info-item" v-if="selectedTripData.author">
                      <label>作者：</label>
                      <span>{{ selectedTripData.author }}</span>
                    </div>
                    <div class="info-item" v-if="selectedTripData.code">
                      <label>行程码：</label>
                      <span>{{ selectedTripData.code }}</span>
                    </div>
                  </div>
                  <div class="advantage-desc" v-if="selectedTripData.advantageDesc">
                    <label>特色简介：</label>
                    <p>{{ selectedTripData.advantageDesc }}</p>
                  </div>
                  <div class="trip-tip" v-if="selectedTripData.tripTip">
                    <label>行程提示：</label>
                    <p>{{ selectedTripData.tripTip }}</p>
                  </div>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
      
      <!-- 第三步：变量映射 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="variable-mapping">
          <h3>变量映射设置</h3>
          <p class="mapping-desc">请选择行程数据字段与海报模板变量的对应关系：</p>
          
          <div class="mapping-table">
            <a-table
              :columns="mappingColumns"
              :dataSource="variableMappings"
              :pagination="false"
              size="small"
              rowKey="templateVariable">
              
              <template slot="tripField" slot-scope="text, record">
                <a-select
                  v-model="record.tripField"
                  placeholder="选择对应的行程字段"
                  allowClear
                  style="width: 100%;">
                  <a-select-option 
                    v-for="field in availableTripFields" 
                    :key="field.key" 
                    :value="field.key">
                    {{ field.label }}
                  </a-select-option>
                </a-select>
              </template>
              
              <template slot="preview" slot-scope="text, record">
                <span class="preview-value">
                  {{ getPreviewValue(record.tripField) }}
                </span>
              </template>
              
              <template slot="action" slot-scope="text, record">
                <a-switch
                  v-model="record.enabled"
                  size="small"
                  :disabled="!record.tripField" />
              </template>
            </a-table>
          </div>
          
          <div class="mapping-summary">
            <a-alert
              :message="`已映射 ${enabledMappingsCount} 个变量，将自动填充到海报中`"
              type="info"
              show-icon />
          </div>
        </div>
      </div>
    </div>
    
    <template slot="footer">
      <a-button @click="handleCancel">取消</a-button>
      <a-button v-if="currentStep > 0" @click="prevStep">上一步</a-button>
      <a-button 
        v-if="currentStep < 2" 
        type="primary" 
        @click="nextStep"
        :disabled="!canProceed">
        下一步
      </a-button>
      <a-button 
        v-if="currentStep === 2" 
        type="primary" 
        @click="handleOk"
        :loading="loading">
        确定导入
      </a-button>
    </template>
  </a-modal>
</template>

<script>
// Performance optimizer removed for compatibility

export default {
  name: 'TripImportModal',
  data() {
    return {
      visible: false,
      loading: false,
      searchLoading: false,
      currentStep: 0,
      
      // 搜索参数
      searchParams: {
        tripName: '',
        tripTag: ''
      },
      
      // 行程列表
      trips: [],
      selectedTripId: null,
      selectedTripData: {},
      
      // 分页
      pagination: {
        current: 1,
        pageSize: 5,
        total: 0
      },
      
      // 变量映射
      variableMappings: [],
      
      // 映射表格列定义
      mappingColumns: [
        {
          title: '模板变量',
          dataIndex: 'templateVariable',
          key: 'templateVariable',
          width: 150
        },
        {
          title: '变量描述',
          dataIndex: 'description',
          key: 'description',
          width: 200
        },
        {
          title: '对应行程字段',
          key: 'tripField',
          scopedSlots: { customRender: 'tripField' },
          width: 200
        },
        {
          title: '预览值',
          key: 'preview',
          scopedSlots: { customRender: 'preview' },
          width: 150
        },
        {
          title: '启用',
          key: 'action',
          scopedSlots: { customRender: 'action' },
          width: 80
        }
      ],
      
      // 可用的行程字段
      availableTripFields: [
        { key: 'tripName', label: '行程名称' },
        { key: 'tripFullName', label: '行程完整名称' },
        { key: 'dayNum', label: '旅游天数' },
        { key: 'price', label: '价格' },
        { key: 'imgUrl', label: '主图URL' },
        { key: 'advantageDesc', label: '特色简介' },
        { key: 'tripTag', label: '行程标签' },
        { key: 'tripTip', label: '行程提示' },
        { key: 'author', label: '作者' },
        { key: 'code', label: '行程码' }
      ]
    }
  },
  
  computed: {
    canProceed() {
      if (this.currentStep === 0) {
        return this.selectedTripId !== null
      }
      if (this.currentStep === 1) {
        return Object.keys(this.selectedTripData).length > 0
      }
      return true
    },
    
    enabledMappingsCount() {
      return this.variableMappings.filter(m => m.enabled && m.tripField).length
    }
  },
  
  created() {
    // 创建防抖函数
    // Debounce function removed for compatibility
  },

  methods: {
    show(templateVariables = []) {
      this.visible = true
      this.currentStep = 0
      this.resetData()
      this.initVariableMappings(templateVariables)
      this.loadTrips()
    },
    
    hide() {
      this.visible = false
      this.resetData()
    },
    
    resetData() {
      this.searchParams = {
        tripName: '',
        tripTag: ''
      }
      this.trips = []
      this.selectedTripId = null
      this.selectedTripData = {}
      this.pagination = {
        current: 1,
        pageSize: 5,
        total: 0
      }
      this.variableMappings = []
    },
    
    initVariableMappings(templateVariables) {
      // 初始化变量映射，如果没有传入模板变量，使用默认的
      const defaultVariables = [
        { key: 'tripName', name: '行程名称', type: 'text', description: '海报标题' },
        { key: 'price', name: '价格', type: 'text', description: '行程价格' },
        { key: 'dayNum', name: '天数', type: 'text', description: '旅游天数' },
        { key: 'advantageDesc', name: '特色', type: 'text', description: '行程特色' },
        { key: 'imgUrl', name: '主图', type: 'image', description: '行程主图' }
      ]
      
      const variables = templateVariables.length > 0 ? templateVariables : defaultVariables
      
      this.variableMappings = variables.map(variable => ({
        templateVariable: variable.key,
        description: variable.description || variable.name,
        tripField: this.getDefaultMapping(variable.key),
        enabled: true
      }))
    },
    
    getDefaultMapping(templateVariable) {
      // 根据模板变量名自动匹配行程字段
      const mappings = {
        'tripName': 'tripName',
        'title': 'tripName',
        'name': 'tripName',
        'price': 'price',
        'dayNum': 'dayNum',
        'days': 'dayNum',
        'advantageDesc': 'advantageDesc',
        'feature': 'advantageDesc',
        'imgUrl': 'imgUrl',
        'image': 'imgUrl',
        'tripTag': 'tripTag',
        'tag': 'tripTag',
        'author': 'author',
        'code': 'code'
      }
      
      return mappings[templateVariable] || null
    },
    
    async loadTrips() {
      this.searchLoading = true
      
      // 生成缓存键
      const cacheKey = `trips-${JSON.stringify({
        ...this.searchParams,
        page: this.pagination.current,
        size: this.pagination.pageSize
      })}`
      
      // Cache functionality removed for compatibility
      
      try {
        const params = {
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...this.searchParams
        }
        
        const response = await this.$http.get('/biz/bizTrip/list', { params })
        
        if (response.success) {
          const pageData = response.result
          this.trips = pageData.records || []
          this.pagination.total = pageData.total || 0
          
          // Cache and preload functionality removed for compatibility
        } else {
          this.$message.error(response.message || '加载行程列表失败')
        }
      } catch (error) {
        console.error('Load trips failed:', error)
        this.$message.error('加载行程列表失败')
      } finally {
        this.searchLoading = false
      }
    },
    
    searchTrips() {
      this.pagination.current = 1
      this.loadTrips()
    },
    
    handlePageChange(page) {
      this.pagination.current = page
      this.loadTrips()
    },
    
    selectTrip(trip) {
      this.selectedTripId = trip.id
    },
    
    async nextStep() {
      if (this.currentStep === 0) {
        // 从第一步到第二步，加载选中行程的详细数据
        if (!this.selectedTripId) {
          this.$message.warning('请选择一个行程')
          return
        }
        
        this.loading = true
        try {
          const response = await this.$http.get(`/biz/userPoster/trip-data/${this.selectedTripId}`)
          
          if (response.success) {
            this.selectedTripData = response.result
            this.currentStep = 1
          } else {
            this.$message.error(response.message || '获取行程数据失败')
          }
        } catch (error) {
          console.error('Get trip data failed:', error)
          this.$message.error('获取行程数据失败')
        } finally {
          this.loading = false
        }
      } else if (this.currentStep === 1) {
        // 从第二步到第三步
        this.currentStep = 2
      }
    },
    
    prevStep() {
      if (this.currentStep > 0) {
        this.currentStep--
      }
    },
    
    getPreviewValue(tripField) {
      if (!tripField || !this.selectedTripData) {
        return '-'
      }
      
      const value = this.selectedTripData[tripField]
      if (value === null || value === undefined) {
        return '-'
      }
      
      // 对于图片字段，显示"[图片]"
      if (tripField === 'imgUrl') {
        return value ? '[图片]' : '-'
      }
      
      // 对于价格字段，添加货币符号
      if (tripField === 'price') {
        return value ? `¥${value}` : '-'
      }
      
      // 对于天数字段，添加单位
      if (tripField === 'dayNum') {
        return value ? `${value}天` : '-'
      }
      
      // 对于长文本，截断显示
      if (typeof value === 'string' && value.length > 20) {
        return value.substring(0, 20) + '...'
      }
      
      return value.toString()
    },
    
    async handleOk() {
      if (this.currentStep < 2) {
        this.nextStep()
        return
      }
      
      // 最后一步，执行导入
      const enabledMappings = this.variableMappings.filter(m => m.enabled && m.tripField)
      
      if (enabledMappings.length === 0) {
        this.$message.warning('请至少启用一个变量映射')
        return
      }
      
      // 构建导入数据
      const importData = {}
      enabledMappings.forEach(mapping => {
        const value = this.selectedTripData[mapping.tripField]
        if (value !== null && value !== undefined) {
          importData[mapping.templateVariable] = value
        }
      })
      
      // 添加原始行程数据引用
      importData._tripId = this.selectedTripId
      importData._tripName = this.selectedTripData.tripName
      
      this.$emit('ok', importData)
      this.hide()
    },
    
    handleCancel() {
      this.hide()
    }
  }
}
</script>

<style scoped>
.trip-import-content {
  min-height: 400px;
}

.step-content {
  margin-top: 16px;
}

.search-bar {
  margin-bottom: 16px;
}

.trip-list {
  max-height: 400px;
  overflow-y: auto;
}

.trip-item-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trip-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.trip-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.trip-item.selected {
  border-color: #1890ff;
  background-color: #f6ffed;
}

.trip-thumbnail {
  width: 80px;
  height: 60px;
  background: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  overflow: hidden;
  flex-shrink: 0;
}

.trip-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #ccc;
  font-size: 24px;
}

.trip-info {
  flex: 1;
}

.trip-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.trip-desc {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.trip-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.trip-days {
  color: #1890ff;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 3px;
}

.trip-price {
  color: #f5222d;
  font-weight: 500;
}

.trip-tag {
  color: #52c41a;
  background: #f6ffed;
  padding: 2px 6px;
  border-radius: 3px;
}

.pagination-wrapper {
  margin-top: 16px;
  text-align: center;
}

.empty-state, .loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.data-preview h3 {
  margin-bottom: 16px;
  color: #333;
}

.preview-card {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 20px;
  background: #fafafa;
}

.preview-image {
  width: 100%;
  height: 200px;
  background: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image-large {
  color: #ccc;
  font-size: 48px;
}

.preview-info h2 {
  margin: 0 0 8px 0;
  color: #333;
}

.full-name {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item label {
  font-weight: 500;
  color: #333;
  margin-right: 8px;
  min-width: 80px;
}

.info-item span {
  color: #666;
}

.advantage-desc, .trip-tip {
  margin-top: 16px;
}

.advantage-desc label, .trip-tip label {
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.advantage-desc p, .trip-tip p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.variable-mapping h3 {
  margin-bottom: 8px;
  color: #333;
}

.mapping-desc {
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
}

.mapping-table {
  margin-bottom: 16px;
}

.preview-value {
  color: #666;
  font-size: 12px;
}

.mapping-summary {
  margin-top: 16px;
}
</style>