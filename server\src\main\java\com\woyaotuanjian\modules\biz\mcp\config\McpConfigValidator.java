package com.woyaotuanjian.modules.biz.mcp.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * MCP配置验证器
 * 在应用启动时验证配置的有效性
 */
@Component
@ConditionalOnProperty(name = "mcp.travel.enabled", havingValue = "true", matchIfMissing = true)
@Slf4j
public class McpConfigValidator {

    @Autowired
    private McpConfig mcpConfig;

    /**
     * 应用启动完成后验证配置
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateConfigurationOnStartup() {
        log.info("开始验证MCP服务配置...");
        
        try {
            // 验证配置有效性
            boolean isValid = validateRuntimeConfiguration();
            
            if (isValid) {
                log.info("MCP服务配置验证通过");
                log.info("MCP服务已启用: {}", mcpConfig.isEnabled());
            } else {
                log.error("MCP服务配置验证失败，请检查配置文件");
            }
            
        } catch (Exception e) {
            log.error("MCP服务配置验证过程中发生异常", e);
        }
    }

    /**
     * 运行时配置验证
     */
    public boolean validateRuntimeConfiguration() {
        try {
            log.debug("执行运行时配置验证...");
            
            // 简单验证：检查服务是否启用
            boolean isValid = mcpConfig.isEnabled();
            
            if (isValid) {
                log.debug("运行时配置验证通过");
            } else {
                log.warn("运行时配置验证失败");
            }
            
            return isValid;
            
        } catch (Exception e) {
            log.error("运行时配置验证异常", e);
            return false;
        }
    }

    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable() {
        try {
            return mcpConfig.isEnabled() && validateRuntimeConfiguration();
        } catch (Exception e) {
            log.error("检查服务可用性时发生异常", e);
            return false;
        }
    }

    /**
     * 获取服务状态信息
     */
    public String getServiceStatus() {
        try {
            if (!mcpConfig.isEnabled()) {
                return "DISABLED";
            }

            if (!validateRuntimeConfiguration()) {
                return "INVALID_CONFIG";
            }

            return "AVAILABLE";

        } catch (Exception e) {
            log.error("获取服务状态时发生异常", e);
            return "ERROR";
        }
    }
}