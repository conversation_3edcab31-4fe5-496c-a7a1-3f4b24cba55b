package com.woyaotuanjian.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.woyaotuanjian.modules.system.entity.SysConfig;

/**
 * @Description: 系统配置管理
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
public interface ISysConfigService extends IService<SysConfig> {

    /**
     * 根据配置键获取配置值
     * @param configKey 配置键
     * @return 配置值
     */
    String getConfigValue(String configKey);

    /**
     * 根据配置键获取配置值，如果不存在返回默认值
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    String getConfigValue(String configKey, String defaultValue);

    /**
     * 根据配置键获取整数配置值
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    Integer getIntConfigValue(String configKey, Integer defaultValue);

    /**
     * 根据配置键获取布尔配置值
     * @param configKey 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    Boolean getBooleanConfigValue(String configKey, Boolean defaultValue);

    /**
     * 设置配置值
     * @param configKey 配置键
     * @param configValue 配置值
     * @param configName 配置名称
     * @param configDesc 配置描述
     * @return 是否成功
     */
    boolean setConfigValue(String configKey, String configValue, String configName, String configDesc);

    /**
     * 删除配置
     * @param configKey 配置键
     * @return 是否成功
     */
    boolean deleteConfig(String configKey);

    /**
     * 刷新配置缓存
     */
    void refreshCache();
}