package com.woyaotuanjian.modules.biz.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.system.query.QueryGenerator;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.BizUserPoster;

import com.woyaotuanjian.modules.biz.entity.vo.TripPosterData;
import com.woyaotuanjian.modules.biz.service.IBizUserPosterService;

import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;

/**
 * @Description: 用户海报管理
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Api(tags="用户海报管理")
@RestController
@RequestMapping("/biz/userPoster")
@Slf4j
public class BizUserPosterController {

    @Autowired
    private IBizUserPosterService userPosterService;
    


    /**
     * 分页列表查询
     */
    @AutoLog(value = "用户海报-分页列表查询")
    @ApiOperation(value="用户海报-分页列表查询", notes="用户海报-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(BizUserPoster userPoster,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        try {
            LoginUser sysUser = SysUserUtil.getCurrentUser();
            
            // 保存海报名称用于模糊查询
            String posterName = userPoster.getPosterName();
            
            // 清空posterName字段，避免QueryGenerator进行精确匹配
            userPoster.setPosterName(null);
            
            // 构建查询条件
            QueryWrapper<BizUserPoster> queryWrapper = QueryGenerator.initQueryWrapper(userPoster, req.getParameterMap());
            
            // 添加海报名称模糊查询
            if (posterName != null && !posterName.trim().isEmpty()) {
                queryWrapper.like("poster_name", posterName.trim());
            }
            
            // 只查询当前用户的海报
            queryWrapper.eq("create_by", sysUser.getId());
            
            // 按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");
            
            Page<BizUserPoster> page = new Page<BizUserPoster>(pageNo, pageSize);
            IPage<BizUserPoster> pageList = userPosterService.page(page, queryWrapper);
            
            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("Query failed", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 保存海报
     */
    @AutoLog(value = "用户海报-保存")
    @ApiOperation(value="用户海报-保存", notes="用户海报-保存")
    @PostMapping(value = "/save")
    public Result<?> save(@RequestBody BizUserPoster userPoster) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            
            if (userPoster.getId() == null) {
                // 新增
                userPoster.setCreateBy(sysUser.getId());
                userPoster.setCreateTime(new Date());
                userPosterService.save(userPoster);
                return Result.ok("保存成功！");
            } else {
                // 更新
                // 权限检查
                if (!userPosterService.hasAccessPermission(userPoster.getId(), sysUser.getId())) {
                    return Result.error("无权限修改此海报！");
                }
                
                userPoster.setUpdateTime(new Date());
                userPosterService.updateById(userPoster);
                return Result.ok("更新成功！");
            }
        } catch (Exception e) {
            log.error("Save failed", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }



    /**
     * 获取行程数据用于填充
     */
    @AutoLog(value = "用户海报-获取行程数据")
    @ApiOperation(value="用户海报-获取行程数据", notes="用户海报-获取行程数据")
    @GetMapping(value = "/trip-data/{tripId}")
    public Result<?> getTripData(@PathVariable Long tripId) {
        try {
            TripPosterData tripData = userPosterService.extractTripData(tripId);
            if (tripData == null) {
                return Result.error("行程不存在或无权限访问");
            }
            return Result.ok(tripData);
        } catch (Exception e) {
            log.error("Get trip data failed", e);
            return Result.error("获取行程数据失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "用户海报-通过id删除")
    @ApiOperation(value="用户海报-通过id删除", notes="用户海报-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) Long id) {
        try {
            LoginUser sysUser = SysUserUtil.getCurrentUser();
            
            // 权限检查
            if (!userPosterService.hasAccessPermission(id, sysUser.getId())) {
                return Result.error("无权限删除此海报！");
            }
            
            userPosterService.removeById(id);
            return Result.ok("删除成功!");
        } catch (Exception e) {
            log.error("Delete failed", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "用户海报-批量删除")
    @ApiOperation(value="用户海报-批量删除", notes="用户海报-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        try {
            LoginUser sysUser = SysUserUtil.getCurrentUser();
            
            String[] idArray = ids.split(",");
            for (String idStr : idArray) {
                Long id = Long.parseLong(idStr);
                if (!userPosterService.hasAccessPermission(id, sysUser.getId())) {
                    return Result.error("无权限删除海报ID: " + id);
                }
            }
            
            userPosterService.removeByIds(Arrays.asList(idArray));
            return Result.ok("批量删除成功!");
        } catch (Exception e) {
            log.error("Batch delete failed", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "用户海报-通过id查询")
    @ApiOperation(value="用户海报-通过id查询", notes="用户海报-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        try {
            LoginUser sysUser = SysUserUtil.getCurrentUser();
            Long posterId = Long.parseLong(id);
            
            // 权限检查
            if (!userPosterService.hasAccessPermission(posterId, sysUser.getId())) {
                return Result.error("无权限访问此海报！");
            }
            
            BizUserPoster userPoster = userPosterService.getById(id);
            if (userPoster == null) {
                return Result.error("未找到对应数据");
            }
            return Result.ok(userPoster);
        } catch (Exception e) {
            log.error("Query by id failed", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 复制海报
     */
    @AutoLog(value = "用户海报-复制海报")
    @ApiOperation(value="用户海报-复制海报", notes="用户海报-复制海报")
    @PostMapping(value = "/copy")
    public Result<?> copyPoster(@RequestParam(name="id",required=true) Long id) {
        try {
            LoginUser sysUser = SysUserUtil.getCurrentUser();
            
            // 权限检查
            if (!userPosterService.hasAccessPermission(id, sysUser.getId())) {
                return Result.error("无权限复制此海报！");
            }
            
            BizUserPoster original = userPosterService.getById(id);
            if (original == null) {
                return Result.error("原海报不存在！");
            }
            
            // 创建副本
            BizUserPoster copy = new BizUserPoster();
            copy.setPosterName(original.getPosterName() + "_副本");
            copy.setTemplateId(original.getTemplateId());
            copy.setPosterData(original.getPosterData());
            copy.setWidth(original.getWidth());
            copy.setHeight(original.getHeight());
            copy.setCreateBy(sysUser.getId());
            copy.setCreateTime(new Date());
            
            userPosterService.save(copy);
            return Result.ok(copy);
        } catch (Exception e) {
            log.error("Copy poster failed", e);
            return Result.error("复制失败：" + e.getMessage());
        }
    }
}