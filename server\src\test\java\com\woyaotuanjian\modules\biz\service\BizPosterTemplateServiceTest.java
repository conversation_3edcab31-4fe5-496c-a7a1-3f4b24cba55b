package com.woyaotuanjian.modules.biz.service;

import com.woyaotuanjian.modules.biz.entity.BizPosterTemplate;
import com.woyaotuanjian.modules.biz.exception.InvalidPosterDataException;
import com.woyaotuanjian.modules.biz.exception.PosterTemplateException;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 海报模板服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class BizPosterTemplateServiceTest {

    @Resource
    private IBizPosterTemplateService posterTemplateService;

    @Test
    public void testValidateTemplateData() {
        // 测试空数据
        assertFalse(posterTemplateService.validateTemplateData(null));
        assertFalse(posterTemplateService.validateTemplateData(""));
        assertFalse(posterTemplateService.validateTemplateData("   "));

        // 测试无效JSON
        assertFalse(posterTemplateService.validateTemplateData("invalid json"));

        // 测试缺少必要字段
        assertFalse(posterTemplateService.validateTemplateData("{}"));
        assertFalse(posterTemplateService.validateTemplateData("{\"canvas\": {}}"));

        // 测试有效数据
        String validData = "{\n" +
                "  \"canvas\": {\n" +
                "    \"width\": 750,\n" +
                "    \"height\": 1334,\n" +
                "    \"backgroundColor\": \"#ffffff\"\n" +
                "  },\n" +
                "  \"objects\": [\n" +
                "    {\n" +
                "      \"type\": \"text\",\n" +
                "      \"left\": 100,\n" +
                "      \"top\": 100,\n" +
                "      \"text\": \"示例文本\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"variables\": [\n" +
                "    {\n" +
                "      \"key\": \"title\",\n" +
                "      \"name\": \"标题\",\n" +
                "      \"type\": \"text\"\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        assertTrue(posterTemplateService.validateTemplateData(validData));
    }

    @Test
    public void testSaveTemplateValidation() {
        // 测试空对象
        assertThrows(PosterTemplateException.class, () -> {
            posterTemplateService.save(null);
        });

        // 测试空名称
        BizPosterTemplate template = new BizPosterTemplate();
        assertThrows(PosterTemplateException.class, () -> {
            posterTemplateService.save(template);
        });

        // 测试无效数据
        template.setTemplateName("测试模板");
        template.setTemplateData("invalid json");
        assertThrows(InvalidPosterDataException.class, () -> {
            posterTemplateService.save(template);
        });
    }

    @Test
    public void testCopyTemplate() {
        // 创建原始模板
        BizPosterTemplate original = new BizPosterTemplate();
        original.setTemplateName("原始模板");
        original.setTemplateDesc("原始描述");
        original.setTemplateData("{\n" +
                "  \"canvas\": {\n" +
                "    \"width\": 750,\n" +
                "    \"height\": 1334\n" +
                "  },\n" +
                "  \"objects\": []\n" +
                "}");
        original.setWidth(750);
        original.setHeight(1334);

        // 复制模板
        BizPosterTemplate copy = posterTemplateService.copyTemplate(original);

        // 验证复制结果
        assertNotNull(copy);
        assertEquals("原始模板_副本", copy.getTemplateName());
        assertEquals(original.getTemplateDesc(), copy.getTemplateDesc());
        assertEquals(original.getTemplateData(), copy.getTemplateData());
        assertEquals(original.getWidth(), copy.getWidth());
        assertEquals(original.getHeight(), copy.getHeight());
        assertEquals(Integer.valueOf(1), copy.getStatus());
        assertEquals(Integer.valueOf(0), copy.getSortOrder());
        assertNotNull(copy.getCreateTime());
    }
}