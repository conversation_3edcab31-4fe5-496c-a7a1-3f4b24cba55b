package com.woyaotuanjian.modules.biz.mcp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * MCP配置类
 * 管理MCP服务的配置参数
 */
@Configuration
@ConfigurationProperties(prefix = "mcp.travel")
@Data
public class McpConfig {

    /**
     * 是否启用MCP服务
     */
    private boolean enabled = true;

    /**
     * 最大结果数量
     */
    private int maxResults = 100;

    /**
     * 默认页面大小
     */
    private int defaultPageSize = 20;

    /**
     * 是否启用缓存
     */
    private boolean cacheEnabled = false;

    /**
     * 超时时间（秒）
     */
    private int timeoutSeconds = 30;

    /**
     * 请求超时时间（毫秒）
     */
    private long requestTimeout = 30000;

    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;

    /**
     * 错误重试次数
     */
    private int retryCount = 3;

    /**
     * 重试间隔（毫秒）
     */
    private long retryInterval = 1000;

    /**
     * 是否启用性能监控
     */
    private boolean performanceMonitoring = true;

    /**
     * 慢查询阈值（毫秒）
     */
    private long slowQueryThreshold = 5000;

    /**
     * 限流配置
     */
    private RateLimit rateLimit = new RateLimit();

    /**
     * 日志配置
     */
    private Logging logging = new Logging();

    @Data
    public static class RateLimit {
        /**
         * 是否启用限流
         */
        private boolean enabled = true;

        /**
         * 每分钟请求数限制
         */
        private int requestsPerMinute = 60;
    }

    @Data
    public static class Logging {
        /**
         * 是否启用日志
         */
        private boolean enabled = true;

        /**
         * 日志级别
         */
        private String level = "INFO";
    }
}