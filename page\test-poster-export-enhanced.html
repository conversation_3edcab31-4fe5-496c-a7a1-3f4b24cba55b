<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强版海报导出功能测试</title>
    <script src="https://unpkg.com/fabric@6.7.1/dist/fabric.min.js"></script>
    <script src="https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script src="https://unpkg.com/jspdf@3.0.1/dist/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 28px;
        }
        .header p {
            color: #666;
            margin: 10px 0 0 0;
        }
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            padding: 30px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 2px dashed #ddd;
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 10px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        button:hover {
            border-color: #1890ff;
            color: #1890ff;
            transform: translateY(-1px);
        }
        button.primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        button.primary:hover {
            background: #40a9ff;
        }
        button.success {
            background: #52c41a;
            color: white;
            border-color: #52c41a;
        }
        button.success:hover {
            background: #73d13d;
        }
        button.warning {
            background: #faad14;
            color: white;
            border-color: #faad14;
        }
        button.warning:hover {
            background: #ffc53d;
        }
        .export-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .export-options {
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            background: #fafafa;
        }
        .export-options h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #e8e8e8;
            padding-bottom: 10px;
        }
        .format-options {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .format-options label {
            display: flex;
            align-items: center;
            gap: 6px;
            cursor: pointer;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            transition: all 0.3s;
        }
        .format-options label:hover {
            border-color: #1890ff;
            background: #f0f8ff;
        }
        .format-options input[type="radio"]:checked + span {
            color: #1890ff;
            font-weight: bold;
        }
        .quality-slider {
            margin: 15px 0;
        }
        .quality-slider label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #1890ff;
            cursor: pointer;
        }
        .progress {
            margin: 20px 0;
            height: 24px;
            background: #f0f0f0;
            border-radius: 12px;
            overflow: hidden;
            display: none;
            position: relative;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #1890ff, #40a9ff);
            transition: width 0.3s;
            border-radius: 12px;
        }
        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #333;
            font-size: 12px;
            font-weight: bold;
        }
        .log {
            margin-top: 30px;
            padding: 20px;
            background: #1f1f1f;
            color: #00ff00;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            padding: 15px;
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .checkbox-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin: 15px 0;
        }
        .checkbox-group label {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 增强版海报导出功能测试</h1>
            <p>测试海报生成和多格式导出功能，包括进度跟踪和错误处理</p>
        </div>
        
        <div class="controls">
            <button onclick="addText()" class="primary">📝 添加文本</button>
            <button onclick="addImage()" class="success">🖼️ 添加图片</button>
            <button onclick="addRect()" class="warning">⬜ 添加矩形</button>
            <button onclick="addCircle()">⭕ 添加圆形</button>
            <button onclick="addGradientRect()">🌈 渐变矩形</button>
            <button onclick="clearCanvas()">🗑️ 清空画布</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="posterCanvas" width="750" height="1334" style="border: 2px solid #ddd; background: white; box-shadow: 0 4px 12px rgba(0,0,0,0.1);"></canvas>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="objectCount">0</div>
                <div class="stat-label">画布对象</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="canvasSize">750×1334</div>
                <div class="stat-label">画布尺寸</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="exportCount">0</div>
                <div class="stat-label">导出次数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="lastExportTime">-</div>
                <div class="stat-label">上次导出</div>
            </div>
        </div>
        
        <div class="export-panel">
            <div class="export-options">
                <h3>🚀 导出设置</h3>
                
                <div class="format-options">
                    <label>
                        <input type="radio" name="format" value="png" checked>
                        <span>PNG</span>
                    </label>
                    <label>
                        <input type="radio" name="format" value="jpg">
                        <span>JPG</span>
                    </label>
                    <label>
                        <input type="radio" name="format" value="pdf">
                        <span>PDF</span>
                    </label>
                </div>
                
                <div class="quality-slider">
                    <label>图片质量: <span id="qualityValue">1.0</span></label>
                    <input type="range" id="qualitySlider" min="0.1" max="1" step="0.1" value="1" onchange="updateQuality()">
                </div>
                
                <div class="checkbox-group">
                    <label>
                        <input type="checkbox" id="withBackground" checked>
                        <span>包含背景色</span>
                    </label>
                    <label>
                        <input type="checkbox" id="highRes">
                        <span>高分辨率导出 (3x)</span>
                    </label>
                    <label>
                        <input type="checkbox" id="autoDownload" checked>
                        <span>自动下载</span>
                    </label>
                </div>
                
                <div class="controls">
                    <button class="primary" onclick="exportPoster()">📥 导出海报</button>
                    <button class="success" onclick="batchExport()">📦 批量导出</button>
                    <button onclick="previewExport()">👁️ 预览效果</button>
                    <button onclick="testHtml2Canvas()">🧪 测试HTML2Canvas</button>
                </div>
            </div>
            
            <div class="export-options">
                <h3>📊 导出信息</h3>
                <p><strong>预估文件大小:</strong> <span id="estimatedSize">计算中...</span></p>
                <p><strong>当前分辨率:</strong> <span id="currentResolution">750×1334</span></p>
                <p><strong>导出分辨率:</strong> <span id="exportResolution">750×1334</span></p>
                <p><strong>压缩质量:</strong> <span id="compressionInfo">100%</span></p>
                
                <div class="progress" id="progress">
                    <div class="progress-bar" id="progressBar"></div>
                    <div class="progress-text" id="progressText">0%</div>
                </div>
                
                <div class="controls">
                    <button onclick="generateThumbnail()">🖼️ 生成缩略图</button>
                    <button onclick="testPerformance()">⚡ 性能测试</button>
                    <button onclick="clearLog()">🧹 清空日志</button>
                </div>
            </div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let canvas;
        let fabricCanvas;
        let exportCount = 0;
        
        // 初始化画布
        function initCanvas() {
            canvas = document.getElementById('posterCanvas');
            fabricCanvas = new fabric.Canvas('posterCanvas', {
                width: 750,
                height: 1334,
                backgroundColor: '#ffffff'
            });
            
            // 监听画布变化
            fabricCanvas.on('object:added', updateStats);
            fabricCanvas.on('object:removed', updateStats);
            fabricCanvas.on('object:modified', updateStats);
            
            log('✅ 画布初始化完成');
            updateStats();
            updateExportInfo();
        }
        
        // 更新统计信息
        function updateStats() {
            const objectCount = fabricCanvas.getObjects().length;
            document.getElementById('objectCount').textContent = objectCount;
            updateExportInfo();
        }
        
        // 更新导出信息
        function updateExportInfo() {
            const quality = parseFloat(document.getElementById('qualitySlider').value);
            const highRes = document.getElementById('highRes').checked;
            
            let multiplier = 1;
            if (highRes) {
                multiplier = 3;
            } else if (quality >= 0.8) {
                multiplier = 2;
            } else if (quality >= 0.5) {
                multiplier = 1.5;
            }
            
            const exportWidth = 750 * multiplier;
            const exportHeight = 1334 * multiplier;
            
            document.getElementById('exportResolution').textContent = `${exportWidth}×${exportHeight}`;
            document.getElementById('compressionInfo').textContent = `${Math.round(quality * 100)}%`;
            
            // 估算文件大小
            estimateFileSize();
        }
        
        // 估算文件大小
        function estimateFileSize() {
            if (!fabricCanvas || fabricCanvas.getObjects().length === 0) {
                document.getElementById('estimatedSize').textContent = '0 KB';
                return;
            }
            
            try {
                const testDataURL = fabricCanvas.toDataURL({
                    format: 'png',
                    quality: 0.1,
                    multiplier: 0.1
                });
                
                const base64Length = testDataURL.split(',')[1].length;
                const estimatedBytes = base64Length * 0.75;
                
                const quality = parseFloat(document.getElementById('qualitySlider').value);
                const highRes = document.getElementById('highRes').checked;
                
                let multiplier = 1;
                if (highRes) multiplier *= 9;
                else if (quality >= 0.8) multiplier *= 4;
                else if (quality >= 0.5) multiplier *= 2.25;
                
                multiplier *= (quality / 0.1);
                
                const finalSize = estimatedBytes * multiplier;
                
                let sizeText;
                if (finalSize < 1024) {
                    sizeText = Math.round(finalSize) + ' B';
                } else if (finalSize < 1024 * 1024) {
                    sizeText = Math.round(finalSize / 1024) + ' KB';
                } else {
                    sizeText = (finalSize / (1024 * 1024)).toFixed(1) + ' MB';
                }
                
                document.getElementById('estimatedSize').textContent = sizeText;
            } catch (error) {
                document.getElementById('estimatedSize').textContent = '计算失败';
            }
        }
        
        // 日志函数
        function log(message) {
            const logElement = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logElement.innerHTML += `<span style="color: #888;">[${time}]</span> ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('📝 日志已清空');
        }
        
        // 更新质量显示
        function updateQuality() {
            const slider = document.getElementById('qualitySlider');
            const value = document.getElementById('qualityValue');
            value.textContent = slider.value;
            updateExportInfo();
        }
        
        // 显示进度
        function showProgress(percent, text = '') {
            const progress = document.getElementById('progress');
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');
            
            progress.style.display = 'block';
            progressBar.style.width = percent + '%';
            progressText.textContent = text || `${percent}%`;
            
            if (percent >= 100) {
                setTimeout(() => {
                    progress.style.display = 'none';
                }, 2000);
            }
        }
        
        // 添加文本
        function addText() {
            const texts = ['精美海报', '旅游推荐', '限时优惠', '立即预订', '品质之选'];
            const randomText = texts[Math.floor(Math.random() * texts.length)];
            
            const text = new fabric.Textbox(randomText, {
                left: Math.random() * 500 + 100,
                top: Math.random() * 800 + 100,
                width: 200,
                fontSize: 24 + Math.random() * 20,
                fill: `hsl(${Math.random() * 360}, 70%, 50%)`,
                fontFamily: 'Arial',
                fontWeight: Math.random() > 0.5 ? 'bold' : 'normal'
            });
            
            fabricCanvas.add(text);
            fabricCanvas.setActiveObject(text);
            log(`📝 添加文本: "${randomText}"`);
        }
        
        // 添加图片
        function addImage() {
            const colors = ['#e6f7ff', '#f6ffed', '#fff2e8', '#f9f0ff', '#fff1f0'];
            const randomColor = colors[Math.floor(Math.random() * colors.length)];
            
            const imageUrl = 'data:image/svg+xml;base64,' + btoa(`
                <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:${randomColor};stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
                        </linearGradient>
                    </defs>
                    <rect width="200" height="150" fill="url(#grad)" stroke="#1890ff" stroke-width="2" rx="8"/>
                    <text x="100" y="80" text-anchor="middle" fill="#1890ff" font-size="16" font-weight="bold">示例图片</text>
                    <circle cx="50" cy="40" r="15" fill="#52c41a" opacity="0.7"/>
                    <circle cx="150" cy="40" r="15" fill="#faad14" opacity="0.7"/>
                </svg>
            `);
            
            fabric.Image.fromURL(imageUrl, (img) => {
                img.set({
                    left: Math.random() * 400 + 100,
                    top: Math.random() * 800 + 100,
                    scaleX: 0.8 + Math.random() * 0.4,
                    scaleY: 0.8 + Math.random() * 0.4
                });
                
                fabricCanvas.add(img);
                fabricCanvas.setActiveObject(img);
                log('🖼️ 添加示例图片');
            });
        }
        
        // 添加矩形
        function addRect() {
            const rect = new fabric.Rect({
                left: Math.random() * 500 + 100,
                top: Math.random() * 800 + 100,
                width: 100 + Math.random() * 100,
                height: 80 + Math.random() * 80,
                fill: `hsl(${Math.random() * 360}, 60%, 60%)`,
                stroke: `hsl(${Math.random() * 360}, 80%, 40%)`,
                strokeWidth: 2,
                rx: Math.random() * 20,
                ry: Math.random() * 20
            });
            
            fabricCanvas.add(rect);
            fabricCanvas.setActiveObject(rect);
            log('⬜ 添加矩形');
        }
        
        // 添加圆形
        function addCircle() {
            const circle = new fabric.Circle({
                left: Math.random() * 500 + 100,
                top: Math.random() * 800 + 100,
                radius: 30 + Math.random() * 40,
                fill: `hsl(${Math.random() * 360}, 60%, 60%)`,
                stroke: `hsl(${Math.random() * 360}, 80%, 40%)`,
                strokeWidth: 2
            });
            
            fabricCanvas.add(circle);
            fabricCanvas.setActiveObject(circle);
            log('⭕ 添加圆形');
        }
        
        // 添加渐变矩形
        function addGradientRect() {
            const rect = new fabric.Rect({
                left: Math.random() * 500 + 100,
                top: Math.random() * 800 + 100,
                width: 150,
                height: 100,
                fill: new fabric.Gradient({
                    type: 'linear',
                    coords: { x1: 0, y1: 0, x2: 150, y2: 100 },
                    colorStops: [
                        { offset: 0, color: `hsl(${Math.random() * 360}, 70%, 60%)` },
                        { offset: 1, color: `hsl(${Math.random() * 360}, 70%, 40%)` }
                    ]
                })
            });
            
            fabricCanvas.add(rect);
            fabricCanvas.setActiveObject(rect);
            log('🌈 添加渐变矩形');
        }
        
        // 清空画布
        function clearCanvas() {
            fabricCanvas.clear();
            fabricCanvas.setBackgroundColor('#ffffff', fabricCanvas.renderAll.bind(fabricCanvas));
            log('🗑️ 画布已清空');
        }
        
        // 导出海报
        async function exportPoster() {
            try {
                if (fabricCanvas.getObjects().length === 0) {
                    log('⚠️ 画布为空，请先添加内容');
                    return;
                }
                
                showProgress(10, '准备导出...');
                log('🚀 开始导出海报...');
                
                const format = document.querySelector('input[name="format"]:checked').value;
                const quality = parseFloat(document.getElementById('qualitySlider').value);
                const withBackground = document.getElementById('withBackground').checked;
                const autoDownload = document.getElementById('autoDownload').checked;
                
                // 取消选择状态
                fabricCanvas.discardActiveObject();
                fabricCanvas.renderAll();
                
                showProgress(30, '渲染中...');
                
                let downloadUrl, fileName;
                
                if (format === 'pdf') {
                    const result = await exportToPDF();
                    downloadUrl = result.url;
                    fileName = result.fileName;
                } else {
                    const result = await exportToImage(format, quality, withBackground);
                    downloadUrl = result.url;
                    fileName = result.fileName;
                }
                
                showProgress(80, '准备下载...');
                
                if (autoDownload) {
                    downloadFile(downloadUrl, fileName);
                }
                
                showProgress(100, '导出完成!');
                
                exportCount++;
                document.getElementById('exportCount').textContent = exportCount;
                document.getElementById('lastExportTime').textContent = new Date().toLocaleTimeString();
                
                log(`✅ 导出完成: ${fileName}`);
                
            } catch (error) {
                log(`❌ 导出失败: ${error.message}`);
                console.error('Export failed:', error);
            }
        }
        
        // 批量导出
        async function batchExport() {
            try {
                if (fabricCanvas.getObjects().length === 0) {
                    log('⚠️ 画布为空，请先添加内容');
                    return;
                }
                
                log('📦 开始批量导出 (PNG + JPG + PDF)...');
                const formats = ['png', 'jpg', 'pdf'];
                const quality = parseFloat(document.getElementById('qualitySlider').value);
                const withBackground = document.getElementById('withBackground').checked;
                const autoDownload = document.getElementById('autoDownload').checked;
                
                for (let i = 0; i < formats.length; i++) {
                    const format = formats[i];
                    const progress = Math.round((i / formats.length) * 80) + 10;
                    
                    showProgress(progress, `导出 ${format.toUpperCase()}...`);
                    log(`📄 正在导出 ${format.toUpperCase()} 格式...`);
                    
                    try {
                        let result;
                        if (format === 'pdf') {
                            result = await exportToPDF();
                        } else {
                            result = await exportToImage(format, quality, withBackground);
                        }
                        
                        if (autoDownload) {
                            downloadFile(result.url, result.fileName);
                        }
                        
                        log(`✅ ${format.toUpperCase()} 导出成功: ${result.fileName}`);
                        
                        // 添加延迟避免浏览器阻止多个下载
                        if (i < formats.length - 1) {
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                        
                    } catch (error) {
                        log(`❌ ${format.toUpperCase()} 导出失败: ${error.message}`);
                    }
                }
                
                showProgress(100, '批量导出完成!');
                
                exportCount += formats.length;
                document.getElementById('exportCount').textContent = exportCount;
                document.getElementById('lastExportTime').textContent = new Date().toLocaleTimeString();
                
                log('🎉 批量导出完成!');
                
            } catch (error) {
                log(`❌ 批量导出失败: ${error.message}`);
                console.error('Batch export failed:', error);
            }
        }
        
        // 导出为图片
        async function exportToImage(format, quality, withBackground) {
            return new Promise((resolve, reject) => {
                try {
                    const highRes = document.getElementById('highRes').checked;
                    
                    let multiplier = 1;
                    if (highRes) {
                        multiplier = 3;
                    } else if (quality >= 0.8) {
                        multiplier = 2;
                    } else if (quality >= 0.5) {
                        multiplier = 1.5;
                    }
                    
                    const exportOptions = {
                        format: format === 'jpg' ? 'jpeg' : 'png',
                        quality: quality,
                        multiplier: multiplier,
                        enableRetinaScaling: true
                    };
                    
                    let dataURL;
                    
                    if (!withBackground && format === 'png') {
                        // 透明背景
                        const originalBg = fabricCanvas.backgroundColor;
                        fabricCanvas.setBackgroundColor('', fabricCanvas.renderAll.bind(fabricCanvas));
                        dataURL = fabricCanvas.toDataURL(exportOptions);
                        fabricCanvas.setBackgroundColor(originalBg, fabricCanvas.renderAll.bind(fabricCanvas));
                    } else {
                        dataURL = fabricCanvas.toDataURL(exportOptions);
                    }
                    
                    const fileName = `海报_${Date.now()}.${format}`;
                    
                    resolve({
                        url: dataURL,
                        fileName: fileName
                    });
                } catch (error) {
                    reject(error);
                }
            });
        }
        
        // 导出为PDF
        async function exportToPDF() {
            return new Promise((resolve, reject) => {
                try {
                    const dataURL = fabricCanvas.toDataURL({
                        format: 'png',
                        quality: 1.0,
                        multiplier: 2,
                        enableRetinaScaling: true
                    });
                    
                    const { jsPDF } = window.jspdf;
                    const pxToMm = 0.264583;
                    const pdfWidth = 750 * pxToMm;
                    const pdfHeight = 1334 * pxToMm;
                    
                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'mm',
                        format: [pdfWidth, pdfHeight],
                        compress: true
                    });
                    
                    pdf.addImage(dataURL, 'PNG', 0, 0, pdfWidth, pdfHeight, undefined, 'FAST');
                    
                    pdf.setProperties({
                        title: '海报导出测试',
                        subject: '海报生成系统测试',
                        author: '海报生成系统',
                        creator: '海报生成系统'
                    });
                    
                    const pdfBlob = pdf.output('blob');
                    const pdfUrl = URL.createObjectURL(pdfBlob);
                    
                    const fileName = `海报_${Date.now()}.pdf`;
                    
                    resolve({
                        url: pdfUrl,
                        fileName: fileName
                    });
                } catch (error) {
                    reject(error);
                }
            });
        }
        
        // 预览导出效果
        function previewExport() {
            try {
                if (fabricCanvas.getObjects().length === 0) {
                    log('⚠️ 画布为空，无法预览');
                    return;
                }
                
                const format = document.querySelector('input[name="format"]:checked').value;
                const quality = parseFloat(document.getElementById('qualitySlider').value);
                
                const dataURL = fabricCanvas.toDataURL({
                    format: format === 'jpg' ? 'jpeg' : 'png',
                    quality: quality,
                    multiplier: 0.5
                });
                
                const previewWindow = window.open('', '_blank', 'width=800,height=600');
                previewWindow.document.write(`
                    <html>
                        <head>
                            <title>导出预览</title>
                            <style>
                                body { margin:0; padding:20px; background:#f0f0f0; text-align:center; font-family: Arial, sans-serif; }
                                h2 { color: #333; }
                                .info { background: white; padding: 15px; border-radius: 8px; margin: 20px auto; max-width: 600px; }
                                img { max-width:100%; border:2px solid #ddd; background:white; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
                            </style>
                        </head>
                        <body>
                            <h2>🎨 导出预览</h2>
                            <div class="info">
                                <p><strong>格式:</strong> ${format.toUpperCase()}</p>
                                <p><strong>质量:</strong> ${Math.round(quality * 100)}%</p>
                                <p><strong>预览分辨率:</strong> 50%</p>
                            </div>
                            <img src="${dataURL}" />
                        </body>
                    </html>
                `);
                
                log('👁️ 预览窗口已打开');
            } catch (error) {
                log(`❌ 预览失败: ${error.message}`);
            }
        }
        
        // 测试HTML2Canvas
        async function testHtml2Canvas() {
            try {
                log('🧪 开始HTML2Canvas测试...');
                showProgress(20, 'HTML2Canvas测试中...');
                
                const canvasElement = document.getElementById('posterCanvas');
                const screenshot = await html2canvas(canvasElement, {
                    backgroundColor: '#ffffff',
                    scale: 1,
                    useCORS: true,
                    allowTaint: false,
                    logging: false
                });
                
                showProgress(60, '生成截图...');
                
                const dataURL = screenshot.toDataURL('image/png', 1.0);
                const fileName = `HTML2Canvas_测试_${Date.now()}.png`;
                
                if (document.getElementById('autoDownload').checked) {
                    downloadFile(dataURL, fileName);
                }
                
                showProgress(100, 'HTML2Canvas测试完成!');
                log('✅ HTML2Canvas测试完成');
                
            } catch (error) {
                log(`❌ HTML2Canvas测试失败: ${error.message}`);
                console.error('HTML2Canvas test failed:', error);
            }
        }
        
        // 生成缩略图
        async function generateThumbnail() {
            try {
                if (fabricCanvas.getObjects().length === 0) {
                    log('⚠️ 画布为空，无法生成缩略图');
                    return;
                }
                
                log('🖼️ 生成缩略图...');
                showProgress(30, '生成缩略图...');
                
                const dataURL = fabricCanvas.toDataURL({
                    format: 'png',
                    quality: 0.8,
                    multiplier: 0.2
                });
                
                const fileName = `缩略图_${Date.now()}.png`;
                
                if (document.getElementById('autoDownload').checked) {
                    downloadFile(dataURL, fileName);
                }
                
                showProgress(100, '缩略图生成完成!');
                log('✅ 缩略图生成完成');
                
            } catch (error) {
                log(`❌ 缩略图生成失败: ${error.message}`);
            }
        }
        
        // 性能测试
        async function testPerformance() {
            log('⚡ 开始性能测试...');
            
            const startTime = performance.now();
            
            try {
                // 测试不同质量和分辨率的导出性能
                const tests = [
                    { quality: 0.3, multiplier: 1, name: '低质量1x' },
                    { quality: 0.7, multiplier: 1.5, name: '中质量1.5x' },
                    { quality: 1.0, multiplier: 2, name: '高质量2x' }
                ];
                
                for (let i = 0; i < tests.length; i++) {
                    const test = tests[i];
                    const testStart = performance.now();
                    
                    showProgress((i / tests.length) * 100, `测试 ${test.name}...`);
                    
                    fabricCanvas.toDataURL({
                        format: 'png',
                        quality: test.quality,
                        multiplier: test.multiplier
                    });
                    
                    const testTime = performance.now() - testStart;
                    log(`📊 ${test.name}: ${testTime.toFixed(2)}ms`);
                }
                
                const totalTime = performance.now() - startTime;
                showProgress(100, '性能测试完成!');
                log(`🏁 性能测试完成，总耗时: ${totalTime.toFixed(2)}ms`);
                
            } catch (error) {
                log(`❌ 性能测试失败: ${error.message}`);
            }
        }
        
        // 下载文件
        function downloadFile(url, fileName) {
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            if (url.startsWith('blob:')) {
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initCanvas();
            
            // 监听设置变化
            document.getElementById('qualitySlider').addEventListener('input', updateExportInfo);
            document.getElementById('highRes').addEventListener('change', updateExportInfo);
            document.querySelectorAll('input[name="format"]').forEach(radio => {
                radio.addEventListener('change', updateExportInfo);
            });
            
            // 添加一些示例内容
            setTimeout(() => {
                addText();
                addRect();
                addCircle();
                addImage();
                log('🎨 示例内容已添加');
            }, 500);
        });
    </script>
</body>
</html>