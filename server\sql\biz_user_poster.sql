-- 用户海报表
CREATE TABLE `biz_user_poster` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `poster_name` varchar(100) NOT NULL COMMENT '海报名称',
  `template_id` bigint(20) DEFAULT NULL COMMENT '模板ID',
  `poster_data` longtext NOT NULL COMMENT '海报配置数据(JSON)',
  `poster_url` varchar(500) DEFAULT NULL COMMENT '生成的海报图片URL',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `width` int(11) NOT NULL DEFAULT '750' COMMENT '画布宽度',
  `height` int(11) NOT NULL DEFAULT '1334' COMMENT '画布高度',
  `create_by` int(11) NOT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_create_by` (`create_by`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户海报表';