-- 海报功能菜单权限配置
-- 插入海报管理一级菜单
INSERT INTO `sys_permission` (
    `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `hidden`, `description`, `create_by`, `create_time`, 
    `del_flag`, `rule_flag`, `status`
) VALUES (
    0, '海报管理', '/poster', 'layouts/RouteView', NULL, '/poster/template', 
    0, NULL, '1', 8, 0, 'picture', 
    1, 0, 0, '海报生成和管理功能', 'admin', NOW(), 
    0, 0, '1'
);

-- 获取刚插入的海报管理菜单ID
SET @poster_parent_id = LAST_INSERT_ID();

-- 插入海报模板管理子菜单
INSERT INTO `sys_permission` (
    `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `hidden`, `description`, `create_by`, `create_time`, 
    `del_flag`, `rule_flag`, `status`
) VALUES (
    @poster_parent_id, '海报模板管理', '/poster/template', 'biz/BizPosterTemplate/BizPosterTemplateList', 'BizPosterTemplateList', NULL, 
    1, 'poster:template:list', '1', 1, 0, 'layout', 
    1, 1, 0, '海报模板列表和管理', 'admin', NOW(), 
    0, 0, '1'
);

-- 获取海报模板管理菜单ID
SET @template_menu_id = LAST_INSERT_ID();

-- 插入海报模板管理相关按钮权限
INSERT INTO `sys_permission` (
    `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `hidden`, `description`, `create_by`, `create_time`, 
    `del_flag`, `rule_flag`, `status`
) VALUES 
(@template_menu_id, '新增模板', NULL, NULL, NULL, NULL, 2, 'poster:template:add', '1', 1, 0, NULL, 1, 1, 0, '新增海报模板', 'admin', NOW(), 0, 0, '1'),
(@template_menu_id, '编辑模板', NULL, NULL, NULL, NULL, 2, 'poster:template:edit', '1', 2, 0, NULL, 1, 1, 0, '编辑海报模板', 'admin', NOW(), 0, 0, '1'),
(@template_menu_id, '删除模板', NULL, NULL, NULL, NULL, 2, 'poster:template:delete', '1', 3, 0, NULL, 1, 1, 0, '删除海报模板', 'admin', NOW(), 0, 0, '1'),
(@template_menu_id, '查看模板详情', NULL, NULL, NULL, NULL, 2, 'poster:template:detail', '1', 4, 0, NULL, 1, 1, 0, '查看海报模板详情', 'admin', NOW(), 0, 0, '1');

-- 插入用户海报管理子菜单
INSERT INTO `sys_permission` (
    `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `hidden`, `description`, `create_by`, `create_time`, 
    `del_flag`, `rule_flag`, `status`
) VALUES (
    @poster_parent_id, '我的海报', '/poster/user', 'biz/BizUserPoster/BizUserPosterList', 'BizUserPosterList', NULL, 
    1, 'poster:user:list', '1', 2, 0, 'file-image', 
    1, 1, 0, '用户海报列表和管理', 'admin', NOW(), 
    0, 0, '1'
);

-- 获取用户海报管理菜单ID
SET @user_poster_menu_id = LAST_INSERT_ID();

-- 插入用户海报管理相关按钮权限
INSERT INTO `sys_permission` (
    `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `hidden`, `description`, `create_by`, `create_time`, 
    `del_flag`, `rule_flag`, `status`
) VALUES 
(@user_poster_menu_id, '创建海报', NULL, NULL, NULL, NULL, 2, 'poster:user:add', '1', 1, 0, NULL, 1, 1, 0, '创建新海报', 'admin', NOW(), 0, 0, '1'),
(@user_poster_menu_id, '编辑海报', NULL, NULL, NULL, NULL, 2, 'poster:user:edit', '1', 2, 0, NULL, 1, 1, 0, '编辑海报', 'admin', NOW(), 0, 0, '1'),
(@user_poster_menu_id, '删除海报', NULL, NULL, NULL, NULL, 2, 'poster:user:delete', '1', 3, 0, NULL, 1, 1, 0, '删除海报', 'admin', NOW(), 0, 0, '1'),
(@user_poster_menu_id, '生成海报', NULL, NULL, NULL, NULL, 2, 'poster:user:generate', '1', 4, 0, NULL, 1, 1, 0, '生成海报图片', 'admin', NOW(), 0, 0, '1'),
(@user_poster_menu_id, '导入行程数据', NULL, NULL, NULL, NULL, 2, 'poster:user:import', '1', 5, 0, NULL, 1, 1, 0, '导入行程数据', 'admin', NOW(), 0, 0, '1');

-- 插入海报模板编辑页面路由（隐藏菜单，通过参数访问）
INSERT INTO `sys_permission` (
    `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `hidden`, `description`, `create_by`, `create_time`, 
    `del_flag`, `rule_flag`, `status`
) VALUES (
    @poster_parent_id, '海报模板编辑', '/poster/template/edit', 'biz/BizPosterTemplate/modules/PosterTemplateEditor', 'PosterTemplateEditor', NULL, 
    1, 'poster:template:edit', '1', 3, 0, NULL, 
    1, 1, 1, '海报模板编辑页面', 'admin', NOW(), 
    0, 0, '1'
);

-- 插入用户海报编辑页面路由（隐藏菜单，通过参数访问）
INSERT INTO `sys_permission` (
    `parent_id`, `name`, `url`, `component`, `component_name`, `redirect`, 
    `menu_type`, `perms`, `perms_type`, `sort_no`, `always_show`, `icon`, 
    `is_route`, `is_leaf`, `hidden`, `description`, `create_by`, `create_time`, 
    `del_flag`, `rule_flag`, `status`
) VALUES (
    @poster_parent_id, '海报编辑', '/poster/user/edit', 'biz/BizUserPoster/PosterEditor', 'PosterEditor', NULL, 
    1, 'poster:user:edit', '1', 4, 0, NULL, 
    1, 1, 1, '用户海报编辑页面', 'admin', NOW(), 
    0, 0, '1'
);