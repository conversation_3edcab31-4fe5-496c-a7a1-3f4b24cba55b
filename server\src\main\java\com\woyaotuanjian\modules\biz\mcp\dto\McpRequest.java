package com.woyaotuanjian.modules.biz.mcp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * MCP请求基础结构
 * 遵循JSON-RPC 2.0规范
 */
@Data
public class McpRequest {
    
    /**
     * JSON-RPC版本，固定为"2.0"
     */
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    /**
     * 请求方法名
     */
    private String method;
    
    /**
     * 请求参数
     */
    private Map<String, Object> params;
    
    /**
     * 请求ID，用于关联请求和响应
     */
    private String id;
}