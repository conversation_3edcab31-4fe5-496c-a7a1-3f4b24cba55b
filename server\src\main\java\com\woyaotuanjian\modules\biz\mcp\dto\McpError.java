package com.woyaotuanjian.modules.biz.mcp.dto;

import lombok.Data;

/**
 * MCP错误信息结构
 * 遵循JSON-RPC 2.0错误规范
 */
@Data
public class McpError {
    
    /**
     * 错误码
     */
    private int code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 错误详细数据（可选）
     */
    private Object data;
    
    public McpError(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public McpError(int code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    // 标准错误码常量
    public static final int PARSE_ERROR = -32700;
    public static final int INVALID_REQUEST = -32600;
    public static final int METHOD_NOT_FOUND = -32601;
    public static final int INVALID_PARAMS = -32602;
    public static final int INTERNAL_ERROR = -32603;
    
    // 预定义错误
    public static McpError parseError() {
        return new McpError(PARSE_ERROR, "Parse error");
    }
    
    public static McpError invalidRequest() {
        return new McpError(INVALID_REQUEST, "Invalid Request");
    }
    
    public static McpError methodNotFound() {
        return new McpError(METHOD_NOT_FOUND, "Method not found");
    }
    
    public static McpError invalidParams() {
        return new McpError(INVALID_PARAMS, "Invalid params");
    }
    
    public static McpError invalidParams(String message) {
        return new McpError(INVALID_PARAMS, message);
    }
    
    public static McpError internalError() {
        return new McpError(INTERNAL_ERROR, "Internal error");
    }
    
    public static McpError internalError(String message) {
        return new McpError(INTERNAL_ERROR, message);
    }
}