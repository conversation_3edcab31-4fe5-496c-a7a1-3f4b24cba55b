<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="模板名称">
              <a-input placeholder="请输入模板名称" @keyup.enter="searchQuery" v-model="queryParam.templateName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="状态">
              <a-select placeholder="请选择状态" v-model="queryParam.status" allowClear>
                <a-select-option value="1">启用</a-select-option>
                <a-select-option value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a-button type="primary" @click="handleAdd" icon="plus" style="margin-left: 8px">新增模板</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 表格区域 -->
    <a-table
      ref="table"
      size="middle"
      bordered
      rowKey="id"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange">
      
      <template slot="thumbnail" slot-scope="text">
        <img :src="text" style="width: 60px; height: 80px; object-fit: cover;" v-if="text"/>
        <span v-else>暂无缩略图</span>
      </template>
      
      <template slot="status" slot-scope="text">
        <a-badge :status="text == 1 ? 'success' : 'default'" :text="text == 1 ? '启用' : '禁用'" />
      </template>
      
      <template slot="action" slot-scope="text, record">
        <a @click="handleUseTemplate(record)">用此模板制作</a>
        <a-divider type="vertical" />
        <a @click="handleEdit(record)">编辑</a>
        <a-divider type="vertical" />
        <a @click="handleCopy(record)">复制</a>
        <a-divider type="vertical" />
        <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a>删除</a>
        </a-popconfirm>
      </template>
    </a-table>
    
    <!-- 已移除内置弹窗，使用路由跳转到编辑页 -->
  </a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
// Poster navigation removed for compatibility

export default {
  name: 'BizPosterTemplateList',
  mixins: [JeecgListMixin],
  data() {
    return {
      description: '海报模板管理页面',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '缩略图',
          align: 'center',
          dataIndex: 'thumbnailUrl',
          width: 100,
          scopedSlots: { customRender: 'thumbnail' }
        },
        {
          title: '模板名称',
          align: 'center',
          dataIndex: 'templateName',
          width: 200
        },
        {
          title: '模板描述',
          align: 'center',
          dataIndex: 'templateDesc',
          width: 300,
          ellipsis: true
        },
        {
          title: '尺寸',
          align: 'center',
          dataIndex: 'width',
          width: 120,
          customRender: (text, record) => `${record.width}×${record.height}`
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status',
          width: 80,
          scopedSlots: { customRender: 'status' }
        },
        {
          title: '排序',
          align: 'center',
          dataIndex: 'sortOrder',
          width: 80
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime',
          width: 150
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/biz/posterTemplate/list',
        delete: '/biz/posterTemplate/delete',
        deleteBatch: '/biz/posterTemplate/deleteBatch'
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  computed: {},
  methods: {
    initDictConfig() {
      // 初始化字典配置
    },
    
    handleAdd() {
      // 跳转到模板编辑器页面
      this.$router.push({ path: '/poster/template/edit', query: { mode: 'add' } })
    },
    
    handleEdit(record) {
      // 跳转到模板编辑器页面
      this.$router.push({ path: '/poster/template/edit', query: { id: record.id, mode: 'edit' } })
    },
    
    handleCopy(record) {
      this.$confirm({
        title: '确认复制',
        content: '确定要复制这个模板吗？',
        onOk: () => {
          this.copyTemplate(record.id)
        }
      })
    },
    
    handleUseTemplate(record) {
      this.$router.push({ path: '/poster/user/edit', query: { templateId: record.id } })
    },
    
    copyTemplate(id) {
      this.loading = true
      this.$http.post('/biz/posterTemplate/copy', null, {
        params: { id }
      }).then(res => {
        if (res.success) {
          this.$message.success('复制成功')
          this.loadData()
        } else {
          this.$message.error(res.message || '复制失败')
        }
      }).catch(err => {
        this.$message.error('复制失败：' + err.message)
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style scoped>
</style>