package com.woyaotuanjian.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.modules.system.entity.SysConfig;
import com.woyaotuanjian.modules.system.mapper.SysConfigMapper;
import com.woyaotuanjian.modules.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * @Description: 系统配置管理
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Service
@Slf4j
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements ISysConfigService {

    @Override
    @Cacheable(value = "sysConfig", key = "#configKey")
    public String getConfigValue(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return null;
        }
        return baseMapper.getConfigValueByKey(configKey);
    }

    @Override
    public String getConfigValue(String configKey, String defaultValue) {
        String value = getConfigValue(configKey);
        return StringUtils.hasText(value) ? value : defaultValue;
    }

    @Override
    public Integer getIntConfigValue(String configKey, Integer defaultValue) {
        String value = getConfigValue(configKey);
        if (!StringUtils.hasText(value)) {
            return defaultValue;
        }
        try {
            return Integer.valueOf(value);
        } catch (NumberFormatException e) {
            log.warn("配置值转换为整数失败: key={}, value={}", configKey, value);
            return defaultValue;
        }
    }

    @Override
    public Boolean getBooleanConfigValue(String configKey, Boolean defaultValue) {
        String value = getConfigValue(configKey);
        if (!StringUtils.hasText(value)) {
            return defaultValue;
        }
        return "true".equalsIgnoreCase(value) || "1".equals(value);
    }

    @Override
    @CacheEvict(value = "sysConfig", key = "#configKey")
    public boolean setConfigValue(String configKey, String configValue, String configName, String configDesc) {
        if (!StringUtils.hasText(configKey)) {
            return false;
        }

        try {
            LoginUser currentUser = SysUserUtil.getCurrentUser();
            
            QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("config_key", configKey);
            SysConfig existingConfig = getOne(queryWrapper);

            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setConfigValue(configValue);
                if (StringUtils.hasText(configName)) {
                    existingConfig.setConfigName(configName);
                }
                if (StringUtils.hasText(configDesc)) {
                    existingConfig.setConfigDesc(configDesc);
                }
                existingConfig.setUpdateBy(currentUser.getId());
                existingConfig.setUpdateTime(new Date());
                return updateById(existingConfig);
            } else {
                // 创建新配置
                SysConfig newConfig = new SysConfig();
                newConfig.setConfigKey(configKey);
                newConfig.setConfigValue(configValue);
                newConfig.setConfigName(StringUtils.hasText(configName) ? configName : configKey);
                newConfig.setConfigDesc(configDesc);
                newConfig.setConfigType("custom");
                newConfig.setIsSystem(0);
                newConfig.setStatus(1);
                newConfig.setCreateBy(currentUser.getId());
                newConfig.setCreateTime(new Date());
                return save(newConfig);
            }
        } catch (Exception e) {
            log.error("设置配置值失败: key={}, value={}", configKey, configValue, e);
            return false;
        }
    }

    @Override
    @CacheEvict(value = "sysConfig", key = "#configKey")
    public boolean deleteConfig(String configKey) {
        if (!StringUtils.hasText(configKey)) {
            return false;
        }

        try {
            QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("config_key", configKey);
            queryWrapper.eq("is_system", 0); // 只能删除非系统配置
            return remove(queryWrapper);
        } catch (Exception e) {
            log.error("删除配置失败: key={}", configKey, e);
            return false;
        }
    }

    @Override
    @CacheEvict(value = "sysConfig", allEntries = true)
    public void refreshCache() {
        log.info("系统配置缓存已刷新");
    }
}