package com.woyaotuanjian.modules.biz.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.system.query.QueryGenerator;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.common.util.OConvertUtils;
import com.woyaotuanjian.modules.biz.entity.BizMaterial;
import com.woyaotuanjian.modules.biz.service.IBizMaterialService;
import com.woyaotuanjian.modules.biz.service.IBizCompanyService;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import com.woyaotuanjian.modules.biz.util.YdUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 图文素材库
 * @Author: jeecg-boot
 * @Date: 2024-01-01
 * @Version: V1.0
 */
@Api(tags="图文素材库")
@RestController
@RequestMapping("/biz/bizMaterial")
@Slf4j
public class BizMaterialController {

    @Autowired
    private IBizMaterialService bizMaterialService;
    
    @Autowired
    private IBizCompanyService companyService;

    /**
     * 分页列表查询
     *
     * @param bizMaterial
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "图文素材库-分页列表查询")
    @ApiOperation(value="图文素材库-分页列表查询", notes="图文素材库-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(BizMaterial bizMaterial,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   @RequestParam(name="onlyCheckedCom", defaultValue="1") Integer onlyCheckedCom,
                                   HttpServletRequest req) {
        try {
            Map<String, Object> param = YdUtil.sqlMap(req.getParameterMap());
            LoginUser sysUser = SysUserUtil.getCurrentUser();

            // 使用与trip相同的数据过滤逻辑
            companyService.dataListFilter(param, sysUser, onlyCheckedCom);

            // 我的数据优先
            param.put("currentUserId", sysUser.getId());

            // 保存素材名称用于模糊查询
            String materialName = bizMaterial.getMaterialName();
            
            // 清空materialName字段，避免QueryGenerator进行精确匹配
            bizMaterial.setMaterialName(null);
            
            // 构建查询条件
            QueryWrapper<BizMaterial> queryWrapper = QueryGenerator.initQueryWrapper(bizMaterial, req.getParameterMap());
            
            // 添加素材名称模糊查询
            if (materialName != null && !materialName.trim().isEmpty()) {
                queryWrapper.like("material_name", materialName.trim());
            }
            
            // 应用数据权限过滤（基于companyService的过滤结果）
            applyDataPermissionFromParams(queryWrapper, param, sysUser);
            
            // 默认按创建时间倒序排列
            queryWrapper.orderByDesc("create_time");
            
            Page<BizMaterial> page = new Page<BizMaterial>(pageNo, pageSize);
            IPage<BizMaterial> pageList = bizMaterialService.page(page, queryWrapper);
            
            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("Query failed", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 添加
     *
     * @param bizMaterial
     * @return
     */
    @AutoLog(value = "图文素材库-添加")
    @ApiOperation(value="图文素材库-添加", notes="图文素材库-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BizMaterial bizMaterial) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // 设置创建信息
        bizMaterial.setCreateBy(sysUser.getUsername());
        bizMaterial.setCreateTime(new Date());
        bizMaterial.setSysUserId(sysUser.getId().longValue());
        bizMaterial.setSysUserName(sysUser.getRealname());
        bizMaterial.setSysOrgCode(sysUser.getOrgCode());
        bizMaterial.setComId(sysUser.getComId());
        
        // 默认状态为启用
        if (bizMaterial.getStatus() == null) {
            bizMaterial.setStatus(1);
        }
        
        bizMaterialService.save(bizMaterial);
        return Result.ok("添加成功！");
    }

    /**
     * 编辑
     *
     * @param bizMaterial
     * @return
     */
    @AutoLog(value = "图文素材库-编辑")
    @ApiOperation(value="图文素材库-编辑", notes="图文素材库-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody BizMaterial bizMaterial) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // 权限检查
        BizMaterial existingMaterial = bizMaterialService.getById(bizMaterial.getId());
        if (existingMaterial == null) {
            return Result.error("素材不存在！");
        }
        
        if (!hasEditPermission(sysUser, existingMaterial)) {
            return Result.error("无权限编辑此素材！");
        }
        
        // 设置更新信息
        bizMaterial.setUpdateBy(sysUser.getUsername());
        bizMaterial.setUpdateTime(new Date());
        
        bizMaterialService.updateById(bizMaterial);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "图文素材库-通过id删除")
    @ApiOperation(value="图文素材库-通过id删除", notes="图文素材库-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) Long id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // 权限检查
        BizMaterial existingMaterial = bizMaterialService.getById(id);
        if (existingMaterial == null) {
            return Result.error("素材不存在！");
        }
        
        if (!hasEditPermission(sysUser, existingMaterial)) {
            return Result.error("无权限删除此素材！");
        }
        
        bizMaterialService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "图文素材库-批量删除")
    @ApiOperation(value="图文素材库-批量删除", notes="图文素材库-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        if(OConvertUtils.isEmpty(ids)) {
            return Result.error("参数不识别！");
        }
        
        String[] idArray = ids.split(",");
        List<Long> idList = Arrays.stream(idArray)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        
        // 权限检查
        List<BizMaterial> materials = bizMaterialService.listByIds(idList);
        for (BizMaterial material : materials) {
            if (!hasEditPermission(sysUser, material)) {
                return Result.error("无权限删除素材：" + material.getMaterialName());
            }
        }
        
        bizMaterialService.removeByIds(idList);
        return Result.ok("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "图文素材库-通过id查询")
    @ApiOperation(value="图文素材库-通过id查询", notes="图文素材库-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) Long id) {
        BizMaterial bizMaterial = bizMaterialService.getById(id);
        if(bizMaterial==null) {
            return Result.error("未找到对应数据");
        }
        return Result.ok(bizMaterial);
    }

    /**
     * 复制素材
     *
     * @param bizMaterial
     * @return
     */
    @AutoLog(value = "图文素材库-复制")
    @ApiOperation(value="图文素材库-复制", notes="图文素材库-复制")
    @PostMapping(value = "/copy")
    public Result<?> copy(@RequestBody BizMaterial bizMaterial) {
        try {
            BizMaterial original = bizMaterialService.getById(bizMaterial.getId());
            if (original == null) {
                return Result.error("原始素材不存在！");
            }
            
            BizMaterial copy = bizMaterialService.copyMaterial(original);
            return Result.ok(copy);
        } catch (Exception e) {
            log.error("复制素材失败", e);
            return Result.error("复制失败：" + e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param request
     * @param bizMaterial
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, BizMaterial bizMaterial) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // 保存素材名称用于模糊查询
        String materialName = bizMaterial.getMaterialName();
        
        // 清空materialName字段，避免QueryGenerator进行精确匹配
        bizMaterial.setMaterialName(null);
        
        // 构建查询条件
        QueryWrapper<BizMaterial> queryWrapper = QueryGenerator.initQueryWrapper(bizMaterial, request.getParameterMap());
        
        // 添加素材名称模糊查询
        if (materialName != null && !materialName.trim().isEmpty()) {
            queryWrapper.like("material_name", materialName.trim());
        }
        
        applyDataPermission(queryWrapper, sysUser);
        queryWrapper.orderByDesc("create_time");
        
        List<BizMaterial> queryList = bizMaterialService.list(queryWrapper);
        
        // 过滤敏感信息
        List<BizMaterial> exportList = queryList.stream().map(item -> {
            BizMaterial export = new BizMaterial();
            export.setMaterialName(item.getMaterialName());
            export.setMaterialType(item.getMaterialType());
            export.setTags(item.getTags());
            export.setCreateTime(item.getCreateTime());
            export.setSysUserName(item.getSysUserName());
            return export;
        }).collect(Collectors.toList());

        // 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "图文素材库列表");
        mv.addObject(NormalExcelConstants.CLASS, BizMaterial.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("图文素材库数据", "导出人:" + sysUser.getRealname(), "图文素材库"));
        mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            
            try {
                List<BizMaterial> list = ExcelImportUtil.importExcel(file.getInputStream(), BizMaterial.class, params);
                LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
                
                for (BizMaterial bizMaterial : list) {
                    // 设置创建信息
                    bizMaterial.setCreateBy(sysUser.getUsername());
                    bizMaterial.setCreateTime(new Date());
                    bizMaterial.setSysUserId(sysUser.getId().longValue());
                    bizMaterial.setSysUserName(sysUser.getRealname());
                    bizMaterial.setSysOrgCode(sysUser.getOrgCode());
                    bizMaterial.setComId(sysUser.getComId());
                    
                    if (bizMaterial.getStatus() == null) {
                        bizMaterial.setStatus(1);
                    }
                }
                
                bizMaterialService.saveBatch(list);
                return Result.ok("文件导入成功！数据行数:" + list.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

    /**
     * 应用数据权限
     */
    private void applyDataPermission(QueryWrapper<BizMaterial> queryWrapper, LoginUser loginUser) {
        if (loginUser.getRoleCode().equals(RoleConstant.ADMIN)) {
            // 管理员可以查看所有数据
            return;
        } else if (loginUser.getRoleCode().startsWith(RoleConstant.B1)) {
            // biz或bizSuper角色，只能查看本公司数据
            queryWrapper.and(wrapper -> wrapper.eq("com_id", loginUser.getComId()).or().eq("com_id", 0));
        } else if (loginUser.getRoleCode().startsWith(RoleConstant.B2)) {
            // biz角色，只能查看自己创建的数据和公共数据
            queryWrapper.and(wrapper -> wrapper.eq("sys_user_id", loginUser.getId().longValue()).or().eq("com_id", 0));
        }
    }

    /**
     * 生成素材访问密钥
     *
     * @param id
     * @return
     */
    @AutoLog(value = "图文素材库-生成访问密钥")
    @ApiOperation(value="图文素材库-生成访问密钥", notes="图文素材库-生成访问密钥")
    @GetMapping(value = "/generateAccessKey")
    public Result<?> generateAccessKey(@RequestParam(name="id",required=true) Long id) {
        try {
            BizMaterial material = bizMaterialService.getById(id);
            if (material == null) {
                return Result.error("素材不存在！");
            }
            
            // 生成6位访问密钥（使用素材ID + 创建时间进行计算）
            String salt = "mat2024";
            String rawKey = id + "_" + material.getCreateTime().getTime() + "_" + salt;
            String md5Hash = org.apache.commons.codec.digest.DigestUtils.md5Hex(rawKey);
            // 取MD5的前6位作为访问密钥
            String accessKey = md5Hash.substring(0, 6);
            
            return Result.ok(accessKey);
        } catch (Exception e) {
            log.error("生成访问密钥失败", e);
            return Result.error("生成访问密钥失败");
        }
    }

    /**
     * 检查编辑权限
     */
    private boolean hasEditPermission(LoginUser loginUser, BizMaterial material) {
        if (loginUser.getRoleCode().equals(RoleConstant.ADMIN)) {
            return true;
        } else if (loginUser.getRoleCode().startsWith(RoleConstant.B1)) {
            // biz或bizSuper角色，可以编辑本公司数据（com_id != 0）
            boolean isNotPublic = !Long.valueOf(0L).equals(material.getComId());
            boolean isSameCompany = loginUser.getComId().equals(material.getComId());
            return isNotPublic && isSameCompany;
        } else if (loginUser.getRoleCode().startsWith(RoleConstant.B2)) {
            // biz角色，只能编辑自己创建的数据
            return loginUser.getId().longValue() == material.getSysUserId().longValue();
        }
        
        return false;
    }



    /**
     * 基于companyService过滤结果应用数据权限
     */
    private void applyDataPermissionFromParams(QueryWrapper<BizMaterial> queryWrapper, Map<String, Object> param, LoginUser loginUser) {
        if (loginUser.getRoleCode().equals(RoleConstant.ADMIN)) {
            // 管理员可以查看所有数据
            return;
        } else if (loginUser.getRoleCode().startsWith(RoleConstant.B1)) {
            // B1角色：查看本公司数据和公共数据
            if (param.containsKey("comGroup")) {
                String comGroup = (String) param.get("comGroup");
                if (comGroup != null && !comGroup.isEmpty()) {
                    // 解析comGroup中的公司ID列表，comGroup格式如：'0','123','456'
                    String[] comIds = comGroup.replace("'", "").split(",");
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < comIds.length; i++) {
                            if (i == 0) {
                                wrapper.eq("com_id", Long.parseLong(comIds[i].trim()));
                            } else {
                                wrapper.or().eq("com_id", Long.parseLong(comIds[i].trim()));
                            }
                        }
                    });
                } else {
                    // 默认只看本公司和公共数据
                    queryWrapper.and(wrapper -> wrapper.eq("com_id", loginUser.getComId()).or().eq("com_id", 0));
                }
            } else {
                // 默认只看本公司和公共数据
                queryWrapper.and(wrapper -> wrapper.eq("com_id", loginUser.getComId()).or().eq("com_id", 0));
            }
        } else if (loginUser.getRoleCode().startsWith(RoleConstant.B2)) {
            // B2角色：查看自己创建的数据 + 勾选公司的数据 + 公共数据
            queryWrapper.and(wrapper -> {
                // 自己创建的数据
                wrapper.eq("sys_user_id", loginUser.getId().longValue());
                
                // 勾选公司的数据
                if (param.containsKey("comGroup")) {
                    String comGroup = (String) param.get("comGroup");
                    if (comGroup != null && !comGroup.isEmpty()) {
                        // 解析comGroup中的公司ID列表，comGroup格式如：'0','123','456'
                        String[] comIds = comGroup.replace("'", "").split(",");
                        for (int i = 0; i < comIds.length; i++) {
                            wrapper.or().eq("com_id", Long.parseLong(comIds[i].trim()));
                        }
                    }
                } else {
                    // 如果没有comGroup，至少包含公共数据
                    wrapper.or().eq("com_id", 0);
                }
            });
        }
    }
} 