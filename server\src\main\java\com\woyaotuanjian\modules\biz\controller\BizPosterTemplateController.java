package com.woyaotuanjian.modules.biz.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.constant.RoleConstant;
import com.woyaotuanjian.common.system.query.QueryGenerator;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.BizPosterTemplate;
import com.woyaotuanjian.modules.biz.service.IBizPosterTemplateService;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 海报模板管理
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Api(tags="海报模板管理")
@RestController
@RequestMapping("/biz/posterTemplate")
@Slf4j
public class BizPosterTemplateController {

    @Autowired
    private IBizPosterTemplateService posterTemplateService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "海报模板-分页列表查询")
    @ApiOperation(value="海报模板-分页列表查询", notes="海报模板-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(BizPosterTemplate posterTemplate,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                   HttpServletRequest req) {
        try {
            // 保存模板名称用于模糊查询
            String templateName = posterTemplate.getTemplateName();
            
            // 清空templateName字段，避免QueryGenerator进行精确匹配
            posterTemplate.setTemplateName(null);
            
            // 构建查询条件
            QueryWrapper<BizPosterTemplate> queryWrapper = QueryGenerator.initQueryWrapper(posterTemplate, req.getParameterMap());
            
            // 添加模板名称模糊查询
            if (templateName != null && !templateName.trim().isEmpty()) {
                queryWrapper.like("template_name", templateName.trim());
            }
            
            // 只查询启用状态的模板（如果没有指定状态）
            if (posterTemplate.getStatus() == null) {
                queryWrapper.eq("status", 1);
            }
            
            // 按排序字段和创建时间排序
            queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");
            
            Page<BizPosterTemplate> page = new Page<BizPosterTemplate>(pageNo, pageSize);
            IPage<BizPosterTemplate> pageList = posterTemplateService.page(page, queryWrapper);
            
            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("Query failed", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 添加
     */
    @AutoLog(value = "海报模板-添加")
    @ApiOperation(value="海报模板-添加", notes="海报模板-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody BizPosterTemplate posterTemplate) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            
            // 只有管理员可以创建模板
            if (!RoleConstant.ADMIN.equals(sysUser.getRoleCode())) {
                return Result.error("只有管理员可以创建海报模板！");
            }
            
            // 设置创建信息
            posterTemplate.setCreateBy(sysUser.getId());
            posterTemplate.setCreateTime(new Date());
            
            posterTemplateService.save(posterTemplate);
            return Result.ok("添加成功！");
        } catch (Exception e) {
            log.error("Add failed", e);
            return Result.error("添加失败：" + e.getMessage());
        }
    }

    /**
     * 编辑
     */
    @AutoLog(value = "海报模板-编辑")
    @ApiOperation(value="海报模板-编辑", notes="海报模板-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody BizPosterTemplate posterTemplate) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            
            // 只有管理员可以编辑模板
            if (!RoleConstant.ADMIN.equals(sysUser.getRoleCode())) {
                return Result.error("只有管理员可以编辑海报模板！");
            }
            
            // 权限检查
            BizPosterTemplate existingTemplate = posterTemplateService.getById(posterTemplate.getId());
            if (existingTemplate == null) {
                return Result.error("模板不存在！");
            }
            
            // 设置更新信息
            posterTemplate.setUpdateBy(sysUser.getId());
            posterTemplate.setUpdateTime(new Date());
            
            posterTemplateService.updateById(posterTemplate);
            return Result.ok("编辑成功!");
        } catch (Exception e) {
            log.error("Edit failed", e);
            return Result.error("编辑失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "海报模板-通过id删除")
    @ApiOperation(value="海报模板-通过id删除", notes="海报模板-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) Long id) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            
            // 只有管理员可以删除模板
            if (!RoleConstant.ADMIN.equals(sysUser.getRoleCode())) {
                return Result.error("只有管理员可以删除海报模板！");
            }
            
            // 权限检查
            BizPosterTemplate existingTemplate = posterTemplateService.getById(id);
            if (existingTemplate == null) {
                return Result.error("模板不存在！");
            }
            
            posterTemplateService.removeById(id);
            return Result.ok("删除成功!");
        } catch (Exception e) {
            log.error("Delete failed", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "海报模板-批量删除")
    @ApiOperation(value="海报模板-批量删除", notes="海报模板-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            
            // 只有管理员可以删除模板
            if (!RoleConstant.ADMIN.equals(sysUser.getRoleCode())) {
                return Result.error("只有管理员可以删除海报模板！");
            }
            
            this.posterTemplateService.removeByIds(Arrays.asList(ids.split(",")));
            return Result.ok("批量删除成功!");
        } catch (Exception e) {
            log.error("Batch delete failed", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "海报模板-通过id查询")
    @ApiOperation(value="海报模板-通过id查询", notes="海报模板-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
        try {
            BizPosterTemplate posterTemplate = posterTemplateService.getById(id);
            if (posterTemplate == null) {
                return Result.error("未找到对应数据");
            }
            return Result.ok(posterTemplate);
        } catch (Exception e) {
            log.error("Query by id failed", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 复制模板
     */
    @AutoLog(value = "海报模板-复制模板")
    @ApiOperation(value="海报模板-复制模板", notes="海报模板-复制模板")
    @PostMapping(value = "/copy")
    public Result<?> copyTemplate(@RequestParam(name="id",required=true) Long id) {
        try {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            
            // 只有管理员可以复制模板
            if (!RoleConstant.ADMIN.equals(sysUser.getRoleCode())) {
                return Result.error("只有管理员可以复制海报模板！");
            }
            
            BizPosterTemplate original = posterTemplateService.getById(id);
            if (original == null) {
                return Result.error("原模板不存在！");
            }
            
            BizPosterTemplate copy = posterTemplateService.copyTemplate(original);
            return Result.ok(copy);
        } catch (Exception e) {
            log.error("Copy template failed", e);
            return Result.error("复制失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有启用的模板（用于用户选择）
     */
    @AutoLog(value = "海报模板-获取启用模板列表")
    @ApiOperation(value="海报模板-获取启用模板列表", notes="海报模板-获取启用模板列表")
    @GetMapping(value = "/listEnabled")
    public Result<?> listEnabled() {
        try {
            QueryWrapper<BizPosterTemplate> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");
            
            return Result.ok(posterTemplateService.list(queryWrapper));
        } catch (Exception e) {
            log.error("List enabled templates failed", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}