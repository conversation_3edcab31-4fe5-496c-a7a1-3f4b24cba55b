package com.woyaotuanjian.modules.biz.controller;

import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.dto.RouteQueryRequest;
import com.woyaotuanjian.modules.biz.dto.RouteQueryResponse;
import com.woyaotuanjian.modules.biz.entity.BizTrip;
import com.woyaotuanjian.modules.biz.entity.BizTripRoute;
import com.woyaotuanjian.modules.biz.service.IBizTripRouteService;
import com.woyaotuanjian.modules.biz.service.IBizTripService;
import com.woyaotuanjian.modules.biz.service.IRouteQueryService;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 行程路线配置
 */
@Slf4j
@Api(tags = "行程路线配置")
@RestController
@RequestMapping("/biz/bizTripRoute")
public class BizTripRouteController {

    @Autowired
    private IBizTripRouteService bizTripRouteService;
    
    @Autowired
    private IBizTripService bizTripService;
    
    @Autowired
    private IRouteQueryService routeQueryService;

    /**
     * 根据行程ID或行程码获取路线配置列表
     */
    @AutoLog(value = "行程路线配置-根据行程ID或行程码查询")
    @ApiOperation(value = "行程路线配置-根据行程ID或行程码查询", notes = "行程路线配置-根据行程ID或行程码查询")
    @GetMapping(value = "/listByTripId")
    public Result listByTripId(@RequestParam(name = "tripId", required = true) String tripId) {
        try {
            Long actualTripId = getTripIdFromParam(tripId);
            if (actualTripId == null) {
                return Result.error("无效的行程ID或行程码");
            }
            
            List<BizTripRoute> list = bizTripRouteService.getByTripId(actualTripId);
            return Result.ok(list);
        } catch (Exception e) {
            log.error("查询失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 从参数中获取实际的tripId
     * @param tripIdParam 可能是数字ID或字符串code
     * @return 实际的tripId，如果找不到则返回null
     */
    private Long getTripIdFromParam(String tripIdParam) {
        try {
            // 尝试直接解析为Long，如果成功说明是数字ID
            return Long.parseLong(tripIdParam);
        } catch (NumberFormatException e) {
            // 如果解析失败，说明是字符串code，需要通过code查询trip
            try {
                BizTrip trip = bizTripService.getTripByCode(tripIdParam);
                return trip != null ? trip.getId() : null;
            } catch (Exception ex) {
                log.warn("根据code查询行程失败: {}", tripIdParam, ex);
                return null;
            }
        }
    }

    /**
     * 保存或更新路线配置
     */
    @AutoLog(value = "行程路线配置-保存或更新")
    @ApiOperation(value = "行程路线配置-保存或更新", notes = "行程路线配置-保存或更新")
    @PostMapping(value = "/saveOrUpdate")
    public Result saveOrUpdate(@RequestBody BizTripRoute bizTripRoute) {
        try {
            LoginUser loginUser = SysUserUtil.getCurrentUser();
            bizTripRoute.setCreateBy(loginUser.getId().toString());
            bizTripRoute.setUpdateBy(loginUser.getId().toString());
            
            boolean success = bizTripRouteService.saveOrUpdateRoute(bizTripRoute);
            if (success) {
                return Result.ok("保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存失败", e);
            return Result.error("保存失败：" + e.getMessage());
        }
    }

    /**
     * 通过id删除路线配置
     */
    @AutoLog(value = "行程路线配置-通过id删除")
    @ApiOperation(value = "行程路线配置-通过id删除", notes = "行程路线配置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result delete(@RequestParam(name = "id", required = true) String id) {
        try {
            boolean success = bizTripRouteService.removeById(id);
            if (success) {
                return Result.ok("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 删除行程的所有路线配置
     */
    @AutoLog(value = "行程路线配置-删除行程所有路线")
    @ApiOperation(value = "行程路线配置-删除行程所有路线", notes = "行程路线配置-删除行程所有路线")
    @DeleteMapping(value = "/deleteByTripId")
    public Result deleteByTripId(@RequestParam(name = "tripId", required = true) Long tripId) {
        try {
            boolean success = bizTripRouteService.deleteByTripId(tripId);
            if (success) {
                return Result.ok("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量查询路线
     */
    @AutoLog(value = "行程路线配置-批量查询路线")
    @ApiOperation(value = "行程路线配置-批量查询路线", notes = "行程路线配置-批量查询路线")
    @PostMapping(value = "/batchQueryRoutes")
    public Result batchQueryRoutes(@RequestBody List<RouteQueryRequest> requests) {
        try {
            List<RouteQueryResponse> responses = routeQueryService.batchQueryRoutes(requests);
            return Result.ok(responses);
        } catch (Exception e) {
            log.error("批量查询路线失败", e);
            return Result.error("批量查询路线失败：" + e.getMessage());
        }
    }
} 