package com.woyaotuanjian.modules.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.common.exception.GlobalException;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.BizPosterTemplate;
import com.woyaotuanjian.modules.biz.exception.InvalidPosterDataException;
import com.woyaotuanjian.modules.biz.exception.PosterTemplateException;
import com.woyaotuanjian.modules.biz.exception.PosterTemplateNotFoundException;
import com.woyaotuanjian.modules.biz.mapper.BizPosterTemplateMapper;
import com.woyaotuanjian.modules.biz.service.IBizPosterTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 海报模板
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Service
@Slf4j
public class BizPosterTemplateServiceImpl extends ServiceImpl<BizPosterTemplateMapper, BizPosterTemplate> implements IBizPosterTemplateService {

    @Override
    public BizPosterTemplate copyTemplate(BizPosterTemplate original) {
        // 获取当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        
        // 创建新的模板对象
        BizPosterTemplate copy = new BizPosterTemplate();
        
        // 复制基本信息
        copy.setTemplateName(original.getTemplateName() + "_副本");
        copy.setTemplateDesc(original.getTemplateDesc());
        copy.setTemplateData(original.getTemplateData());
        copy.setWidth(original.getWidth());
        copy.setHeight(original.getHeight());
        copy.setStatus(1); // 默认启用
        copy.setSortOrder(0); // 默认排序
        
        // 设置创建信息
        copy.setCreateBy(sysUser.getId());
        copy.setCreateTime(new Date());
        
        // 保存到数据库
        this.save(copy);
        
        return copy;
    }

    @Override
    public boolean validateTemplateData(String templateData) {
        if (!StringUtils.hasText(templateData)) {
            log.warn("模板数据为空");
            return false;
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(templateData);
            
            // 验证必要的字段
            if (!jsonObject.containsKey("canvas")) {
                log.warn("模板数据缺少canvas字段");
                return false;
            }
            
            if (!jsonObject.containsKey("objects")) {
                log.warn("模板数据缺少objects字段");
                return false;
            }
            
            // 验证画布配置
            JSONObject canvas = jsonObject.getJSONObject("canvas");
            if (canvas == null) {
                log.warn("画布配置为空");
                return false;
            }
            
            if (!canvas.containsKey("width") || !canvas.containsKey("height")) {
                log.warn("画布数据缺少width或height字段");
                return false;
            }
            
            // 验证画布尺寸
            Integer width = canvas.getInteger("width");
            Integer height = canvas.getInteger("height");
            if (width == null || height == null || width <= 0 || height <= 0) {
                log.warn("画布尺寸无效: width={}, height={}", width, height);
                return false;
            }
            
            // 验证对象数组
            JSONArray objects = jsonObject.getJSONArray("objects");
            if (objects != null) {
                for (int i = 0; i < objects.size(); i++) {
                    JSONObject obj = objects.getJSONObject(i);
                    if (obj == null || !obj.containsKey("type")) {
                        log.warn("对象{}缺少type字段", i);
                        return false;
                    }
                }
            }
            
            // 验证变量配置（如果存在）
            if (jsonObject.containsKey("variables")) {
                JSONArray variables = jsonObject.getJSONArray("variables");
                if (variables != null) {
                    for (int i = 0; i < variables.size(); i++) {
                        JSONObject variable = variables.getJSONObject(i);
                        if (variable == null || !variable.containsKey("key") || !variable.containsKey("name")) {
                            log.warn("变量{}缺少必要字段", i);
                            return false;
                        }
                    }
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("模板数据JSON格式错误: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String generateThumbnail(Long templateId) {
        // TODO: 实现缩略图生成逻辑
        // 这里可以调用图片处理服务生成模板的缩略图
        log.info("生成模板缩略图: templateId={}", templateId);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(BizPosterTemplate entity) {
        // 参数验证
        if (entity == null) {
            throw new PosterTemplateException("模板对象不能为空");
        }
        
        // 验证模板名称
        if (!StringUtils.hasText(entity.getTemplateName())) {
            throw new PosterTemplateException("模板名称不能为空");
        }
        
        // 检查模板名称是否重复
        if (isTemplateNameExists(entity.getTemplateName(), null)) {
            throw new PosterTemplateException("模板名称已存在");
        }
        
        // 保存前验证模板数据
        if (!validateTemplateData(entity.getTemplateData())) {
            throw new InvalidPosterDataException("模板数据格式不正确");
        }
        
        // 设置默认值
        if (entity.getStatus() == null) {
            entity.setStatus(1);
        }
        if (entity.getSortOrder() == null) {
            entity.setSortOrder(0);
        }
        if (entity.getWidth() == null) {
            entity.setWidth(750);
        }
        if (entity.getHeight() == null) {
            entity.setHeight(1334);
        }
        
        // 设置创建信息
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(new Date());
        }
        if (entity.getCreateBy() == null) {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                entity.setCreateBy(sysUser.getId());
            }
        }
        
        return super.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(BizPosterTemplate entity) {
        // 参数验证
        if (entity == null || entity.getId() == null) {
            throw new PosterTemplateException("模板ID不能为空");
        }
        
        // 检查模板是否存在
        BizPosterTemplate existingTemplate = this.getById(entity.getId());
        if (existingTemplate == null) {
            throw new PosterTemplateNotFoundException(entity.getId());
        }
        
        // 验证模板名称（如果有修改）
        if (StringUtils.hasText(entity.getTemplateName()) && 
            !entity.getTemplateName().equals(existingTemplate.getTemplateName())) {
            if (isTemplateNameExists(entity.getTemplateName(), entity.getId())) {
                throw new PosterTemplateException("模板名称已存在");
            }
        }
        
        // 更新前验证模板数据
        if (StringUtils.hasText(entity.getTemplateData()) && 
            !validateTemplateData(entity.getTemplateData())) {
            throw new InvalidPosterDataException("模板数据格式不正确");
        }
        
        // 设置更新信息
        entity.setUpdateTime(new Date());
        if (entity.getUpdateBy() == null) {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                entity.setUpdateBy(sysUser.getId());
            }
        }
        
        return super.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Serializable id) {
        if (id == null) {
            throw new PosterTemplateException("模板ID不能为空");
        }
        
        // 检查模板是否存在
        BizPosterTemplate template = this.getById(id);
        if (template == null) {
            throw new PosterTemplateNotFoundException("模板不存在: ID=" + id);
        }
        
        // TODO: 检查是否有用户正在使用该模板
        // 这里可以添加业务逻辑检查是否有用户海报使用了该模板
        
        return super.removeById(id);
    }

    /**
     * 检查模板名称是否已存在
     * @param templateName 模板名称
     * @param excludeId 排除的模板ID（用于更新时检查）
     * @return 是否存在
     */
    private boolean isTemplateNameExists(String templateName, Long excludeId) {
        Integer count = baseMapper.checkTemplateNameExists(templateName, excludeId);
        return count != null && count > 0;
    }

    /**
     * 启用模板
     * @param id 模板ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableTemplate(Long id) {
        return updateTemplateStatus(id, 1);
    }

    /**
     * 禁用模板
     * @param id 模板ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableTemplate(Long id) {
        return updateTemplateStatus(id, 0);
    }

    /**
     * 更新模板状态
     * @param id 模板ID
     * @param status 状态
     * @return 是否成功
     */
    private boolean updateTemplateStatus(Long id, Integer status) {
        if (id == null) {
            throw new PosterTemplateException("模板ID不能为空");
        }
        
        BizPosterTemplate template = this.getById(id);
        if (template == null) {
            throw new PosterTemplateNotFoundException(id);
        }
        
        template.setStatus(status);
        template.setUpdateTime(new Date());
        
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            template.setUpdateBy(sysUser.getId());
        }
        
        return this.updateById(template);
    }

    /**
     * 批量更新模板状态
     * @param ids 模板ID列表
     * @param status 状态
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateStatus(List<Long> ids, Integer status) {
        if (ids == null || ids.isEmpty()) {
            throw new PosterTemplateException("模板ID列表不能为空");
        }
        
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Date updateTime = new Date();
        Integer updateBy = sysUser != null ? sysUser.getId() : null;
        
        int affectedRows = baseMapper.batchUpdateStatus(ids, status, updateTime, updateBy);
        return affectedRows > 0;
    }

    /**
     * 获取启用的模板列表
     * @return 启用的模板列表
     */
    @Override
    public List<BizPosterTemplate> getEnabledTemplates() {
        return baseMapper.selectByStatus(1);
    }

    /**
     * 获取用户可访问的启用模板列表
     * @param accessibleTemplateIds 用户可访问的模板ID列表，空列表表示无限制
     * @return 启用的模板列表
     */
    @Override
    public List<BizPosterTemplate> getEnabledTemplatesForUser(List<Long> accessibleTemplateIds) {
        if (accessibleTemplateIds == null || accessibleTemplateIds.isEmpty()) {
            // 空列表表示管理员或无限制用户，返回所有启用的模板
            return getEnabledTemplates();
        } else {
            // 根据权限过滤模板
            return baseMapper.selectEnabledByIds(accessibleTemplateIds);
        }
    }

    /**
     * 更新模板排序
     * @param id 模板ID
     * @param sortOrder 排序值
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSortOrder(Long id, Integer sortOrder) {
        if (id == null) {
            throw new PosterTemplateException("模板ID不能为空");
        }
        
        if (sortOrder == null || sortOrder < 0) {
            throw new PosterTemplateException("排序值无效");
        }
        
        BizPosterTemplate template = this.getById(id);
        if (template == null) {
            throw new PosterTemplateNotFoundException(id);
        }
        
        template.setSortOrder(sortOrder);
        template.setUpdateTime(new Date());
        
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null) {
            template.setUpdateBy(sysUser.getId());
        }
        
        return this.updateById(template);
    }
}