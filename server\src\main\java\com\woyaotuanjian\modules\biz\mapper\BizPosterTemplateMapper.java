package com.woyaotuanjian.modules.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.woyaotuanjian.modules.biz.entity.BizPosterTemplate;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @Description: 海报模板
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
public interface BizPosterTemplateMapper extends BaseMapper<BizPosterTemplate> {

    /**
     * 批量更新状态
     * @param ids ID列表
     * @param status 状态
     * @param updateTime 更新时间
     * @param updateBy 更新人
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, 
                         @Param("status") Integer status, 
                         @Param("updateTime") Date updateTime, 
                         @Param("updateBy") Integer updateBy);

    /**
     * 根据状态查询模板列表
     * @param status 状态
     * @return 模板列表
     */
    List<BizPosterTemplate> selectByStatus(@Param("status") Integer status);

    /**
     * 检查模板名称是否存在
     * @param templateName 模板名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    Integer checkTemplateNameExists(@Param("templateName") String templateName, 
                                   @Param("excludeId") Long excludeId);

    /**
     * 根据ID列表查询启用的模板
     * @param ids ID列表
     * @return 模板列表
     */
    List<BizPosterTemplate> selectEnabledByIds(@Param("ids") List<Long> ids);

}