package com.woyaotuanjian.modules.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.woyaotuanjian.modules.biz.entity.BizQuickQuote;
import com.alibaba.fastjson.JSONObject;

/**
 * @Description: 快捷报价
 * @Author: System
 * @Date: 2024-12-19
 * @Version: V1.0
 */
public interface IBizQuickQuoteService extends IService<BizQuickQuote> {

    /**
     * 解析粘贴内容并生成报价明细
     * @param content 粘贴的内容
     * @return 解析后的报价明细
     */
    JSONObject parsePastedContent(String content);

    /**
     * 从现有行程导入报价明细
     * @param tripId 行程ID
     * @return 报价明细
     */
    JSONObject importFromTrip(Long tripId);

    /**
     * 从文件导入报价明细
     * @param fileContent 文件内容
     * @return 报价明细
     */
    JSONObject importFromFile(String fileContent);

    /**
     * 处理AI解析的快捷报价数据
     * @param aiResponseJson AI返回的JSON数据
     * @return 处理后的报价明细
     */
    JSONObject processAiQuoteData(String aiResponseJson);

    /**
     * 查询景点列表（带权限控制）
     * @param keyword 搜索关键词
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 景点列表
     */
    JSONObject searchScenicList(String keyword, int pageNo, int pageSize);

    /**
     * 查询酒店列表（带权限控制）
     * @param keyword 搜索关键词
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 酒店列表
     */
    JSONObject searchHotelList(String keyword, int pageNo, int pageSize);

    /**
     * 复制快捷报价
     * @param original 原始报价对象
     * @return 复制后的报价对象
     */
    BizQuickQuote copyQuickQuote(BizQuickQuote original);

} 