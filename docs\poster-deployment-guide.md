# 海报生成功能部署指南

## 部署概览

海报生成功能包含后端API、前端页面、数据库表和菜单配置等多个部分，需要按照以下步骤进行部署。

## 📋 部署清单

### 后端文件
- [x] 数据库表结构
- [x] 实体类 (Entity)
- [x] 数据访问层 (Mapper)
- [x] 业务逻辑层 (Service)
- [x] 控制器层 (Controller)
- [x] 配置文件

### 前端文件
- [x] Vue页面组件
- [x] 路由配置
- [x] API接口配置
- [x] 样式文件

### 依赖库
- [x] Fabric.js (前端画布编辑)
- [x] html2canvas (前端截图)

## 🗄️ 数据库部署

### 1. 执行SQL脚本

按顺序执行以下SQL文件：

```bash
# 1. 创建海报模板表
mysql -u用户名 -p数据库名 < server/sql/biz_poster_template.sql

# 2. 创建用户海报表
mysql -u用户名 -p数据库名 < server/sql/biz_user_poster.sql

# 3. 创建权限配置表（如果存在）
mysql -u用户名 -p数据库名 < server/sql/biz_poster_permission.sql

# 4. 插入菜单权限数据
mysql -u用户名 -p数据库名 < server/sql/poster_menu_permissions.sql

# 5. 执行其他更新脚本
mysql -u用户名 -p数据库名 < server/sql/update.sql
```

### 2. 验证表创建

登录数据库确认以下表已创建：
- `biz_poster_template` - 海报模板表
- `biz_user_poster` - 用户海报表
- `biz_poster_permission` - 权限配置表（可选）

## 🔧 后端部署

### 1. 复制Java文件

将以下文件复制到对应目录：

```
# 实体类
server/src/main/java/com/woyaotuanjian/modules/biz/entity/
├── BizPosterTemplate.java
├── BizUserPoster.java
└── BizPosterPermission.java

# VO类
server/src/main/java/com/woyaotuanjian/modules/biz/entity/vo/
└── TripPosterData.java

# DTO类
server/src/main/java/com/woyaotuanjian/modules/biz/entity/dto/
└── PosterGenerateRequest.java

# Mapper接口
server/src/main/java/com/woyaotuanjian/modules/biz/mapper/
├── BizPosterTemplateMapper.java
├── BizUserPosterMapper.java
└── BizPosterPermissionMapper.java

# Service接口
server/src/main/java/com/woyaotuanjian/modules/biz/service/
├── IBizPosterTemplateService.java
├── IBizUserPosterService.java
├── IBizPosterPermissionService.java
└── PosterGenerationService.java

# Service实现
server/src/main/java/com/woyaotuanjian/modules/biz/service/impl/
├── BizPosterTemplateServiceImpl.java
├── BizUserPosterServiceImpl.java
└── BizPosterPermissionServiceImpl.java

# Controller
server/src/main/java/com/woyaotuanjian/modules/biz/controller/
├── BizPosterTemplateController.java
├── BizUserPosterController.java
└── BizPosterConfigController.java
```

### 2. 复制XML映射文件（如果使用）

```
server/src/main/java/com/woyaotuanjian/modules/biz/xml/
├── BizPosterTemplateMapper.xml
├── BizUserPosterMapper.xml
└── BizPosterPermissionMapper.xml
```

### 3. 重新编译后端

```bash
cd server
mvn clean compile
```

## 🎨 前端部署

### 1. 安装依赖

```bash
cd page
npm install fabric html2canvas
# 或者使用 yarn
yarn add fabric html2canvas
```

### 2. 复制Vue组件

将以下文件复制到对应目录：

```
# 海报模板管理
page/src/views/biz/BizPosterTemplate/
├── BizPosterTemplateList.vue
└── modules/
    ├── PosterTemplateModal.vue
    └── PosterTemplateEditor.vue

# 用户海报管理
page/src/views/biz/BizUserPoster/
├── BizUserPosterList.vue
├── PosterEditor.vue
└── modules/
    ├── TripImportModal.vue
    └── README.md

# 海报配置管理
page/src/views/biz/BizPosterConfig/
├── BizPosterConfigList.vue
└── modules/
    └── PermissionModal.vue

# 通用组件
page/src/components/
├── LoadingIndicator/
│   └── LoadingIndicator.vue
├── OptimizedUpload/
│   └── OptimizedUpload.vue
├── PerformanceMonitor/
│   └── PerformanceMonitor.vue
└── PosterEditor/
    └── LazyComponents.js

# 工具类
page/src/utils/
├── performanceOptimizer.js
└── posterNavigation.js
```

### 3. 配置路由

在 `page/src/config/router.config.js` 中添加海报相关路由：

```javascript
// 在 constantRouterMap 的 children 数组中添加
{
  path: '/biz/posterTemplate/list',
  name: 'biz-posterTemplate-list',
  component: () => import('@/views/biz/BizPosterTemplate/BizPosterTemplateList'),
  meta: { title: '海报模板管理', icon: 'picture' }
},
{
  path: '/biz/userPoster/list',
  name: 'biz-userPoster-list',
  component: () => import('@/views/biz/BizUserPoster/BizUserPosterList'),
  meta: { title: '我的海报', icon: 'picture' }
},
{
  path: '/biz/poster/create',
  name: 'biz-poster-create',
  component: () => import('@/views/biz/BizUserPoster/PosterEditor'),
  meta: { title: '制作海报', icon: 'plus' }
},
{
  path: '/biz/poster/edit/:id',
  name: 'biz-poster-edit',
  component: () => import('@/views/biz/BizUserPoster/PosterEditor'),
  meta: { title: '编辑海报', icon: 'edit' }
}
```

### 4. 重新编译前端

```bash
cd page
npm run build:prod
# 或者开发模式
npm run serve
```

## 📱 菜单配置

### 1. 数据库菜单配置

如果使用数据库管理菜单，需要在系统菜单表中添加以下菜单项：

```sql
-- 海报管理主菜单
INSERT INTO sys_permission (id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) 
VALUES ('poster_main', NULL, '海报管理', '/poster', 'layouts/RouteView', 1, NULL, NULL, 0, NULL, '1', 8, 0, 'picture', 0, 0, 0, '海报生成功能', 'admin', NOW(), NULL, NULL, 0, 0, '1', 0);

-- 模板管理子菜单
INSERT INTO sys_permission (id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) 
VALUES ('poster_template', 'poster_main', '模板管理', '/biz/posterTemplate/list', 'biz/BizPosterTemplate/BizPosterTemplateList', 1, NULL, NULL, 1, 'poster:template:list', '1', 1, 0, 'layout', 1, 0, 0, '海报模板管理', 'admin', NOW(), NULL, NULL, 0, 0, '1', 0);

-- 我的海报子菜单
INSERT INTO sys_permission (id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) 
VALUES ('poster_user', 'poster_main', '我的海报', '/biz/userPoster/list', 'biz/BizUserPoster/BizUserPosterList', 1, NULL, NULL, 1, 'poster:user:list', '1', 2, 0, 'file-image', 1, 0, 0, '用户海报管理', 'admin', NOW(), NULL, NULL, 0, 0, '1', 0);
```

### 2. 权限配置

为不同角色分配相应权限：

```sql
-- 管理员权限（可以管理模板）
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT r.id, 'poster_template' FROM sys_role r WHERE r.role_code = 'admin';

-- 普通用户权限（只能使用海报功能）
INSERT INTO sys_role_permission (role_id, permission_id) 
SELECT r.id, 'poster_user' FROM sys_role r WHERE r.role_code IN ('user', 'b1', 'b2');
```

## 🔧 配置文件修改

### 1. 后端配置

在 `application.yml` 中添加海报相关配置（如果需要）：

```yaml
# 海报生成配置
poster:
  # 图片生成质量
  image:
    quality: 1.0
    format: png
  # 文件存储路径
  storage:
    path: /poster/
  # 权限控制
  permission:
    enabled: true
```

### 2. 前端配置

在前端配置文件中添加API路径（如果需要）：

```javascript
// page/src/api/poster.js
export const posterApi = {
  template: {
    list: '/biz/posterTemplate/list',
    add: '/biz/posterTemplate/add',
    edit: '/biz/posterTemplate/edit',
    delete: '/biz/posterTemplate/delete'
  },
  user: {
    list: '/biz/userPoster/list',
    save: '/biz/userPoster/save',
    generate: '/biz/userPoster/generate',
    tripData: '/biz/userPoster/trip-data'
  }
}
```

## 🧪 部署验证

### 1. 后端验证

启动后端服务，检查以下接口是否正常：

```bash
# 检查模板管理接口
curl -X GET "http://localhost:8077/trip/biz/posterTemplate/listEnabled"

# 检查用户海报接口
curl -X GET "http://localhost:8077/trip/biz/userPoster/list"
```

### 2. 前端验证

访问以下页面确认功能正常：

- 海报模板管理：`http://localhost:3300/#/biz/posterTemplate/list`
- 我的海报：`http://localhost:3300/#/biz/userPoster/list`
- 制作海报：`http://localhost:3300/#/biz/poster/create`

### 3. 功能测试

1. **模板管理测试**（管理员）：
   - 创建新模板
   - 编辑模板内容
   - 设置模板状态

2. **海报制作测试**（普通用户）：
   - 选择模板
   - 编辑海报内容
   - 导入行程数据
   - 生成海报图片

## 🚨 常见问题

### 1. 数据库连接问题
**问题**：表创建失败
**解决**：检查数据库连接配置和权限

### 2. 前端编译问题
**问题**：依赖包安装失败
**解决**：
```bash
# 清理缓存重新安装
rm -rf node_modules package-lock.json
npm install
```

### 3. 路由访问问题
**问题**：页面404错误
**解决**：检查路由配置和组件路径

### 4. 权限问题
**问题**：菜单不显示或无权限访问
**解决**：检查用户角色和权限配置

### 5. 图片生成问题
**问题**：海报生成失败
**解决**：
- 检查Java Graphics2D环境
- 确认图片URL可访问
- 检查文件存储权限

## 📞 技术支持

如果部署过程中遇到问题：

1. **检查日志**：查看后端和前端的错误日志
2. **验证环境**：确认Java、Node.js版本兼容
3. **数据库检查**：确认表结构和数据正确
4. **网络检查**：确认API接口可正常访问

## 🔄 版本升级

后续版本升级时：

1. **备份数据**：备份数据库和配置文件
2. **增量更新**：只更新变更的文件
3. **数据迁移**：执行数据库升级脚本
4. **功能验证**：测试新功能是否正常

---

**注意**：部署前请确保备份现有系统，建议先在测试环境验证功能正常后再部署到生产环境。