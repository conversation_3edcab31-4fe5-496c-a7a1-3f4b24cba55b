package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 海报素材
 * @Author: system
 * @Date: 2024-08-16
 * @Version: V1.0
 */
@Data
@TableName("poster_material")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="poster_material对象", description="海报素材")
public class PosterMaterial implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;
    
    /**素材名称*/
    @ApiModelProperty(value = "素材名称")
    private String name;
    
    /**素材类型：image/svg/mask*/
    @ApiModelProperty(value = "素材类型：image/svg/mask")
    private String type;
    
    /**分类ID*/
    @ApiModelProperty(value = "分类ID")
    private Long categoryId;
    
    /**文件URL（图片素材用）*/
    @ApiModelProperty(value = "文件URL（图片素材用）")
    private String fileUrl;
    
    /**缩略图URL*/
    @ApiModelProperty(value = "缩略图URL")
    private String thumbnailUrl;
    
    /**SVG内容（SVG/遮罩素材用）*/
    @ApiModelProperty(value = "SVG内容（SVG/遮罩素材用）")
    private String svgContent;
    
    /**宽度*/
    @ApiModelProperty(value = "宽度")
    private Integer width;
    
    /**高度*/
    @ApiModelProperty(value = "高度")
    private Integer height;
    
    /**文件大小（字节）*/
    @ApiModelProperty(value = "文件大小（字节）")
    private Long fileSize;
    
    /**标签，逗号分隔*/
    @ApiModelProperty(value = "标签，逗号分隔")
    private String tags;
    
    /**描述*/
    @ApiModelProperty(value = "描述")
    private String description;
    
    /**使用次数*/
    @ApiModelProperty(value = "使用次数")
    private Integer downloadCount;
    
    /**排序*/
    @ApiModelProperty(value = "排序")
    private Integer sortOrder;
    
    /**状态：1启用 0禁用*/
    @ApiModelProperty(value = "状态：1启用 0禁用")
    private Integer status;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}