package com.woyaotuanjian.modules.biz.mcp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 行程详情结果
 * 用于MCP工具返回的行程信息
 * 符合MCP协议规范的响应数据对象
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TripDetailResult {
    
    /**
     * 行程ID
     */
    private Long id;
    
    /**
     * 行程名称
     */
    private String tripName;
    
    /**
     * 行程完整名称
     */
    private String tripFullName;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 天数
     */
    private Integer dayNum;
    
    /**
     * 行程介绍
     */
    private String tripDesc;
    
    /**
     * 特色简介
     */
    private String advantageDesc;
    
    /**
     * 日程安排
     */
    private String schedule;
    
    /**
     * 价格说明
     */
    private String priceRemark;
    
    /**
     * 行程提示
     */
    private String tripTip;
    
    /**
     * 行程标签
     */
    private String tripTag;
    
    /**
     * 检查是否为有效的行程数据
     * @return 如果包含基本必需信息则返回true
     */
    public boolean isValid() {
        return id != null && tripName != null && !tripName.trim().isEmpty();
    }
}