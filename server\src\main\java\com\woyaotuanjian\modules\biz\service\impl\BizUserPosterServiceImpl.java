package com.woyaotuanjian.modules.biz.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.BizTrip;
import com.woyaotuanjian.modules.biz.entity.BizUserPoster;
import com.woyaotuanjian.modules.biz.entity.BizPosterExportRecord;
import com.woyaotuanjian.modules.biz.entity.dto.PosterExportRequest;

import com.woyaotuanjian.modules.biz.entity.vo.TripPosterData;
import com.woyaotuanjian.modules.biz.exception.InvalidPosterDataException;
import com.woyaotuanjian.modules.biz.exception.PosterAccessDeniedException;
import com.woyaotuanjian.modules.biz.mapper.BizUserPosterMapper;
import com.woyaotuanjian.modules.biz.service.IBizTripService;
import com.woyaotuanjian.modules.biz.service.IBizUserPosterService;

import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 用户海报
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Service
@Slf4j
public class BizUserPosterServiceImpl extends ServiceImpl<BizUserPosterMapper, BizUserPoster> implements IBizUserPosterService {

    @Autowired
    private IBizTripService tripService;
    


    @Override
    public TripPosterData extractTripData(Long tripId) {
        if (tripId == null) {
            return null;
        }
        
        try {
            BizTrip trip = tripService.getById(tripId);
            if (trip == null) {
                log.warn("行程不存在: tripId={}", tripId);
                return null;
            }
            
            TripPosterData posterData = new TripPosterData();
            BeanUtils.copyProperties(trip, posterData);
            
            return posterData;
        } catch (Exception e) {
            log.error("提取行程数据失败: tripId={}", tripId, e);
            return null;
        }
    }

    @Override
    public boolean validatePosterData(String posterData) {
        if (posterData == null || posterData.trim().isEmpty()) {
            return false;
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(posterData);
            
            // 验证必要的字段
            if (!jsonObject.containsKey("canvas")) {
                log.warn("海报数据缺少canvas字段");
                return false;
            }
            
            if (!jsonObject.containsKey("objects")) {
                log.warn("海报数据缺少objects字段");
                return false;
            }
            
            JSONObject canvas = jsonObject.getJSONObject("canvas");
            if (!canvas.containsKey("width") || !canvas.containsKey("height")) {
                log.warn("画布数据缺少width或height字段");
                return false;
            }
            
            return true;
        } catch (Exception e) {
            log.error("海报数据JSON格式错误: {}", e.getMessage());
            return false;
        }
    }

    @Override
    public String generateThumbnail(Long posterId) {
        // TODO: 实现缩略图生成逻辑
        // 这里可以调用图片处理服务生成海报的缩略图
        log.info("生成海报缩略图: posterId={}", posterId);
        return null;
    }

    @Override
    public boolean hasAccessPermission(Long posterId, Integer userId) {
        if (posterId == null || userId == null) {
            return false;
        }
        
        BizUserPoster poster = this.getById(posterId);
        if (poster == null) {
            return false;
        }
        
        // 只有创建者可以访问自己的海报
        return userId.equals(poster.getCreateBy());
    }

    @Override
    public boolean save(BizUserPoster entity) {
        // 保存前验证海报数据
        if (!validatePosterData(entity.getPosterData())) {
            throw new InvalidPosterDataException("海报数据格式不正确");
        }
        
        // 设置默认值
        if (entity.getWidth() == null) {
            entity.setWidth(750);
        }
        if (entity.getHeight() == null) {
            entity.setHeight(1334);
        }
        
        // 设置创建信息
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(new Date());
        }
        if (entity.getCreateBy() == null) {
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (sysUser != null) {
                entity.setCreateBy(sysUser.getId());
            }
        }
        
        return super.save(entity);
    }

    @Override
    public boolean updateById(BizUserPoster entity) {
        // 更新前验证海报数据
        if (entity.getPosterData() != null && !validatePosterData(entity.getPosterData())) {
            throw new InvalidPosterDataException("海报数据格式不正确");
        }
        
        // 权限检查
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null && !hasAccessPermission(entity.getId(), sysUser.getId())) {
            throw new PosterAccessDeniedException("无权限修改此海报");
        }
        
        // 设置更新时间
        entity.setUpdateTime(new Date());
        
        return super.updateById(entity);
    }

    @Override
    public boolean removeById(Serializable id) {
        // 权限检查
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (sysUser != null && id instanceof Long && !hasAccessPermission((Long) id, sysUser.getId())) {
            throw new PosterAccessDeniedException("无权限删除此海报");
        }
        
        return super.removeById(id);
    }
    
    @Override
    public void saveExportRecord(BizPosterExportRecord exportRecord) {
        // 这里需要创建对应的Mapper和Service来保存导出记录
        // 由于没有对应的Mapper，暂时使用日志记录
        log.info("保存导出记录: 海报ID={}, 文件名={}, 格式={}, 大小={}KB", 
            exportRecord.getPosterId(), 
            exportRecord.getFileName(), 
            exportRecord.getExportFormat(), 
            exportRecord.getFileSize());
        
        // TODO: 实现真正的数据库保存逻辑
        // exportRecordMapper.insert(exportRecord);
    }
    
    @Override
    public IPage<BizPosterExportRecord> getExportRecords(Integer userId, Long posterId, Page<BizPosterExportRecord> page) {
        // 这里需要创建对应的Mapper来查询导出记录
        // 暂时返回空结果
        log.info("查询导出记录: 用户ID={}, 海报ID={}", userId, posterId);
        
        // TODO: 实现真正的数据库查询逻辑
        // QueryWrapper<BizPosterExportRecord> queryWrapper = new QueryWrapper<>();
        // queryWrapper.eq("create_by", userId);
        // if (posterId != null) {
        //     queryWrapper.eq("poster_id", posterId);
        // }
        // queryWrapper.orderByDesc("create_time");
        // return exportRecordMapper.selectPage(page, queryWrapper);
        
        return page; // 返回空页面
    }
    
    @Override
    public List<String> batchExportPoster(PosterExportRequest request, Integer userId) {
        List<String> results = new ArrayList<>();
        
        try {
            // 检查权限
            if (!hasAccessPermission(request.getPosterId(), userId)) {
                throw new RuntimeException("无权限导出此海报");
            }
            
            // 获取海报数据
            BizUserPoster poster = this.getById(request.getPosterId());
            if (poster == null) {
                throw new RuntimeException("海报不存在");
            }
            
            // 批量导出多种格式
            String[] formats = {"png", "jpg", "pdf"};
            
            for (String format : formats) {
                try {
                    // 批量导出功能已移除，改为提示用户使用前端生成
                    results.add(format.toUpperCase() + ": 请使用前端生成功能");
                    
                    log.info("批量导出请求: 格式={}, 已提示用户使用前端生成", format);
                    
                } catch (Exception e) {
                    log.error("批量导出{}格式失败", format, e);
                    results.add(format.toUpperCase() + ": 导出失败 - " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("批量导出失败", e);
            throw new RuntimeException("批量导出失败: " + e.getMessage());
        }
        
        return results;
    }
}