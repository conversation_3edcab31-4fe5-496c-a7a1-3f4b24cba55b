package com.woyaotuanjian.modules.biz.mcp.exception;

import com.woyaotuanjian.modules.biz.mcp.dto.McpError;

/**
 * MCP异常基类
 */
public class McpException extends RuntimeException {
    
    private final McpError mcpError;
    
    public McpException(McpError mcpError) {
        super(mcpError.getMessage());
        this.mcpError = mcpError;
    }
    
    public McpException(int code, String message) {
        super(message);
        this.mcpError = new McpError(code, message);
    }
    
    public McpException(int code, String message, Object data) {
        super(message);
        this.mcpError = new McpError(code, message, data);
    }
    
    public McpError getMcpError() {
        return mcpError;
    }
}