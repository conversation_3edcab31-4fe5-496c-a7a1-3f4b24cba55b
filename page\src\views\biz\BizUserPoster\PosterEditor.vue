<template>
  <a-card :bordered="false">
    <a-page-header
      title="海报编辑"
      @back="$router.back()"
    />

    <a-row :gutter="16">
      <a-col :span="16">
        <a-form :label-col="{ span: 5 }" :wrapper-col="{ span: 18 }">
          <a-form-item label="海报名称">
            <a-input v-model="poster.posterName" placeholder="请输入海报名称" />
          </a-form-item>

          <a-form-item label="画布尺寸">
            <a-input-group compact>
              <a-input-number v-model="poster.width" :min="100" :max="4000" style="width: 45%" />
              <a-input style="width: 10%; text-align: center" disabled value="×" />
              <a-input-number v-model="poster.height" :min="100" :max="4000" style="width: 45%" />
            </a-input-group>
          </a-form-item>

          <a-form-item label="模板">
            <div>
              <a-tag v-if="template.id" color="blue">{{ template.templateName || '模板 #' + template.id }}</a-tag>
              <a-button type="link" @click="pickTemplate">选择模板</a-button>
            </div>
          </a-form-item>

          <a-divider>变量填充</a-divider>

          <a-row :gutter="16">
            <a-col :span="12" v-for="variable in formVariables" :key="variable.key" style="margin-bottom: 8px;">
              <a-form-item :label="variable.name" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                <a-input v-if="variable.type !== 'image'" v-model="poster.variables[variable.key]" :placeholder="`请输入${variable.name}`" />
                <a-input v-else v-model="poster.variables[variable.key]" :placeholder="`请输入${variable.name}URL`" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>

        <div style="margin-top: 16px;">
          <a-button @click="openTripImport" icon="import">导入行程数据</a-button>
          <a-button type="primary" style="margin-left: 8px" @click="handleSave" :loading="saving" icon="save">保存</a-button>
          <a-button type="default" style="margin-left: 8px" @click="handleGenerateOptimized" :loading="generatingLocal" icon="picture">生成图片</a-button>
          <a-button style="margin-left: 8px" @click="handleExportPDF" :loading="exportingPDF" icon="file-pdf">导出PDF</a-button>
          <a-button style="margin-left: 8px" @click="handleExportSVG" :loading="exportingSVG" icon="file-text">导出SVG</a-button>
        </div>
      </a-col>

      <a-col :span="8">
        <a-card title="预览" :loading="generatingLocal" :body-style="{ padding: '12px' }">
          <div v-if="poster.posterUrl" style="text-align: center;">
            <img :src="poster.posterUrl" :alt="poster.posterName" style="max-width: 100%; border-radius: 4px;" />
          </div>
          <a-empty v-else description="保存并生成后可预览" />
        </a-card>
      </a-col>
    </a-row>

    <trip-import-modal ref="tripImport" @ok="applyTripData" />
  </a-card>
</template>

<script>
import TripImportModal from './modules/TripImportModal'

export default {
  name: 'PosterEditor',
  components: { TripImportModal },
  data() {
    return {
      saving: false,
      generatingLocal: false,
      exportingPDF: false,
      exportingSVG: false,
      poster: {
        id: null,
        posterName: '',
        templateId: null,
        width: 750,
        height: 1334,
        variables: {},
        posterUrl: '',
        thumbnailUrl: ''
      },
      template: {
        id: null,
        templateName: '',
        width: 750,
        height: 1334,
        variables: []
      },
      templateData: null,
      defaultVariables: [
        { key: 'tripName', name: '行程名称', type: 'text' },
        { key: 'price', name: '价格', type: 'text' },
        { key: 'dayNum', name: '天数', type: 'text' },
        { key: 'advantageDesc', name: '特色简介', type: 'text' },
        { key: 'imgUrl', name: '主图URL', type: 'image' }
      ]
    }
  },
  computed: {
    formVariables() {
      return (this.template.variables && this.template.variables.length > 0)
        ? this.template.variables
        : this.defaultVariables
    }
  },
  async created() {
    await this.handleRouteEnter(this.$route)
  },
  // 处理缓存页面（keep-alive）和同组件复用场景
  activated() {
    this.handleRouteEnter(this.$route)
  },
  beforeRouteUpdate(to, from, next) {
    this.handleRouteEnter(to).then(() => next())
  },
  methods: {
    resetToDefaults() {
      this.saving = false
      this.generatingLocal = false
      this.exportingPDF = false
      this.exportingSVG = false
      this.poster = {
        id: null,
        posterName: '',
        templateId: null,
        width: 750,
        height: 1334,
        variables: {},
        posterUrl: '',
        thumbnailUrl: ''
      }
      this.template = {
        id: null,
        templateName: '',
        width: 750,
        height: 1334,
        variables: []
      }
      this.templateData = null
    },
    async handleRouteEnter(route) {
      const q = (route && route.query) || {}
      const id = q.id
      const templateId = q.templateId
      // 每次进入先重置，避免旧数据残留
      this.resetToDefaults()
      if (id) {
        await this.loadPoster(id)
      } else if (templateId) {
        await this.loadTemplate(templateId)
      }
      if (!this.poster.posterName) {
        this.poster.posterName = this.template.templateName || '我的海报'
      }
    },
    async loadPoster(id) {
      try {
        const res = await this.$http.get('/biz/userPoster/queryById', { params: { id } })
        if (res.success && res.result) {
          const p = res.result
          this.poster.id = p.id
          this.poster.posterName = p.posterName
          this.poster.templateId = p.templateId
          this.poster.width = p.width || this.poster.width
          this.poster.height = p.height || this.poster.height
          this.poster.posterUrl = p.posterUrl || ''
          this.poster.thumbnailUrl = p.thumbnailUrl || ''
          // 解析变量
          try {
            const data = typeof p.posterData === 'string' ? JSON.parse(p.posterData) : (p.posterData || {})
            this.poster.variables = data.variables || {}
          } catch (e) {
            this.poster.variables = {}
          }
          // 回显模板信息但不覆盖已保存尺寸
          if (this.poster.templateId) {
            await this.loadTemplate(this.poster.templateId, { applyCanvasSize: false })
          }
        } else {
          this.$message.error(res.message || '加载海报失败')
        }
      } catch (e) {
        this.$message.error('加载海报失败')
      }
    },
    async loadTemplate(templateId, options = { applyCanvasSize: true }) {
      try {
        const res = await this.$http.get('/biz/posterTemplate/queryById', { params: { id: templateId } })
        if (res.success && res.result) {
          const t = res.result
          this.template.id = t.id
          this.template.templateName = t.templateName
          this.template.width = t.width || this.template.width
          this.template.height = t.height || this.template.height
          this.poster.templateId = t.id
          if (options.applyCanvasSize) {
            this.poster.width = this.template.width
            this.poster.height = this.template.height
          }
          // 尝试读取模板变量
          try {
            const data = typeof t.templateData === 'string' ? JSON.parse(t.templateData) : (t.templateData || {})
            this.templateData = data || {}
            this.template.variables = (data && data.variables) ? data.variables : []
            // 若模板定义了画布尺寸，以模板为准
            if (data && data.canvas && options.applyCanvasSize) {
              if (data.canvas.width) this.poster.width = data.canvas.width
              if (data.canvas.height) this.poster.height = data.canvas.height
            }
          } catch (e) {
            this.templateData = null
            this.template.variables = []
          }
        } else {
          this.$message.error(res.message || '加载模板失败')
        }
      } catch (e) {
        this.$message.error('加载模板失败')
      }
    },
    pickTemplate() {
      // 最小闭环：跳转到模板列表，用户可选择后再回到此页
      this.$router.push('/poster/template')
    },
    openTripImport() {
      const vars = this.formVariables.map(v => ({ key: v.key, name: v.name, type: v.type }))
      this.$refs.tripImport.show(vars)
    },
    applyTripData(importData) {
      // 合并导入的变量
      const variables = { ...this.poster.variables }
      Object.keys(importData).forEach(k => {
        if (!k.startsWith('_')) {
          variables[k] = importData[k]
        }
      })
      this.poster.variables = variables
    },
    async handleSave() {
      if (!this.poster.posterName) {
        this.$message.warning('请填写海报名称')
        return
      }
      if (!this.poster.templateId) {
        this.$message.warning('请选择模板')
        return
      }
      if (!this.poster.width || !this.poster.height) {
        this.$message.warning('画布尺寸无效')
        return
      }
      const posterCanvas = (this.templateData && this.templateData.canvas) ? this.templateData.canvas : {
        width: this.poster.width,
        height: this.poster.height,
        backgroundColor: '#ffffff'
      }
      // 使用优化后的变量替换逻辑
      const rawObjects = (this.templateData && Array.isArray(this.templateData.objects)) ? this.templateData.objects : []
      const posterObjects = this.resolveVariables(rawObjects, this.poster.variables)
      const payload = {
        id: this.poster.id,
        posterName: this.poster.posterName,
        templateId: this.poster.templateId,
        width: this.poster.width,
        height: this.poster.height,
        posterData: JSON.stringify({
          canvas: posterCanvas,
          variables: this.poster.variables || {},
          objects: posterObjects
        })
      }
      this.saving = true
      try {
        const res = await this.$http.post('/biz/userPoster/save', payload)
        if (res.success) {
          this.$message.success('保存成功')
          if (!this.poster.id) {
            // 获取新建ID（根据名称取最新一条）
            const listRes = await this.$http.get('/biz/userPoster/list', {
              params: { pageNo: 1, pageSize: 1, posterName: this.poster.posterName }
            })
            if (listRes.success && listRes.result && listRes.result.records && listRes.result.records.length > 0) {
              this.poster.id = listRes.result.records[0].id
            }
          }
        } else {
          this.$message.error(res.message || '保存失败')
        }
      } catch (e) {
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },
    
    // 优化后的变量替换逻辑
    resolveVariables(objects, variables) {
      if (!objects || !Array.isArray(objects)) return []
      
      return objects.map(obj => {
        const resolvedObj = { ...obj }
        const variableKey = obj.variableKey || obj.variable || obj.key
        
        if (variableKey && variables[variableKey] != null) {
          const value = variables[variableKey]
          
          // 文本对象
          if (obj.type === 'text' || obj.type === 'i-text' || obj.type === 'textbox') {
            resolvedObj.text = String(value)
          }
          
          // 图片对象
          if (obj.type === 'image' && typeof value === 'string') {
            resolvedObj.src = value
          }
          
          // SVG对象 - 处理路径、颜色等变量替换
          if (obj.type === 'path' || obj.type === 'group') {
            // 如果有原始SVG内容，进行变量替换
            if (obj.originalSvg && typeof obj.originalSvg === 'string') {
              let svgContent = obj.originalSvg
              
              // 替换颜色变量 {{color}}
              if (typeof value === 'string' && value.startsWith('#')) {
                svgContent = svgContent.replace(/\{\{color\}\}/g, value)
              }
              
              // 替换其他命名变量 {{variableName}}
              Object.keys(variables).forEach(varKey => {
                const varValue = variables[varKey]
                if (varValue != null) {
                  const regex = new RegExp(`\\{\\{${varKey}\\}\\}`, 'g')
                  svgContent = svgContent.replace(regex, String(varValue))
                }
              })
              
              // 更新对象的SVG内容
              if (obj.type === 'path' && svgContent !== obj.originalSvg) {
                // 对于path对象，需要解析SVG并提取path数据
                const pathMatch = svgContent.match(/<path[^>]*d="([^"]*)"/)
                if (pathMatch) {
                  resolvedObj.path = pathMatch[1]
                }
                
                // 提取填充颜色
                const fillMatch = svgContent.match(/fill="([^"]*)"/)
                if (fillMatch) {
                  resolvedObj.fill = fillMatch[1]
                }
                
                // 提取描边颜色
                const strokeMatch = svgContent.match(/stroke="([^"]*)"/)
                if (strokeMatch) {
                  resolvedObj.stroke = strokeMatch[1]
                }
              }
            }
          }
        }
        
        // 递归处理组内对象
        if (obj.type === 'group' && obj.objects && Array.isArray(obj.objects)) {
          resolvedObj.objects = this.resolveVariables(obj.objects, variables)
        }
        
        return resolvedObj
      })
    },

    // 优化后的图片生成 - 使用 Fabric.js 原生导出
    async handleGenerateOptimized() {
      if (!this.poster.templateId) {
        this.$message.warning('请选择模板')
        return
      }

      if (!this.poster.id) {
        await this.handleSave()
        if (!this.poster.id) return
      }

      const posterCanvas = (this.templateData && this.templateData.canvas) ? this.templateData.canvas : {
        width: this.poster.width,
        height: this.poster.height,
        backgroundColor: '#ffffff'
      }

      const rawObjects = (this.templateData && Array.isArray(this.templateData.objects)) ? this.templateData.objects : []
      const objects = this.resolveVariables(rawObjects, this.poster.variables)

      this.generatingLocal = true
      try {
        // 动态加载 fabric
        let mod = null
        try {
          mod = await import('fabric')
        } catch (e) {
          try { mod = require('fabric') } catch (e2) {}
        }
        const fabric = (mod && (mod.fabric || mod.default || mod)) || (typeof window !== 'undefined' ? window.fabric : null)
        if (!fabric) {
          this.$message.error('fabric 加载失败，无法本地生成')
          return
        }

        // 创建离屏 canvas
        const offscreen = document.createElement('canvas')
        offscreen.width = posterCanvas.width || this.poster.width
        offscreen.height = posterCanvas.height || this.poster.height
        const fc = new fabric.Canvas(offscreen, { preserveObjectStacking: true, selection: false })
        fc.backgroundColor = posterCanvas.backgroundColor || '#ffffff'

        // 使用 Fabric.js 原生方法加载对象，包含自定义属性
        await new Promise((resolve, reject) => {
          try {
            fc.loadFromJSON({
              objects,
              // 指定需要保留的自定义属性
              reviver: (property, value) => {
                // 保留变量相关的自定义属性和文本属性
                if (['variableKey', 'originalSvg', 'svgColors', 'charSpacing'].includes(property)) {
                  return value
                }
                return value
              }
            }, () => {
              fc.renderAll()
              resolve()
            })
          } catch (err) {
            reject(err)
          }
        })

        // 使用原生方法导出
        const blob = await new Promise((resolve, reject) => {
          try {
            const el = fc.lowerCanvasEl || offscreen
            el.toBlob(resolve, 'image/png', 1.0)
          } catch (e) {
            reject(e)
          }
        })

        if (!blob) {
          throw new Error('导出失败')
        }

        // 上传图片
        const form = new FormData()
        form.append('file', blob, `${this.poster.posterName || 'poster'}.png`)
        const uploadRes = await this.$http.post('/file/upload', form)
        const url = (uploadRes && (uploadRes.result || (uploadRes.data && uploadRes.data.result))) || ''

        if (!url) {
          this.$message.error('上传失败')
          return
        }

        this.poster.posterUrl = url

        // 保存海报记录
        const payload = {
          id: this.poster.id,
          posterName: this.poster.posterName,
          templateId: this.poster.templateId,
          width: this.poster.width,
          height: this.poster.height,
          posterUrl: url,
          posterData: JSON.stringify({
            canvas: posterCanvas,
            variables: this.poster.variables || {},
            objects
          })
        }
        const saveRes = await this.$http.post('/biz/userPoster/save', payload)
        if (saveRes && saveRes.success) {
          this.$message.success('图片生成成功')
        } else {
          this.$message.warn('图片已生成，但保存记录失败')
        }
      } catch (e) {
        console.error(e)
        this.$message.error('图片生成失败')
      } finally {
        this.generatingLocal = false
      }
    },

    // 新增：导出PDF功能
    async handleExportPDF() {
      if (!this.poster.templateId) {
        this.$message.warning('请选择模板')
        return
      }

      this.exportingPDF = true
      try {
        // 首先生成图片
        if (!this.poster.posterUrl) {
          await this.handleGenerateOptimized()
        }

        if (!this.poster.posterUrl) {
          this.$message.error('请先生成图片')
          return
        }

        // 创建PDF
        const { jsPDF } = await import('jspdf')
        const pdf = new jsPDF({
          orientation: this.poster.width > this.poster.height ? 'landscape' : 'portrait',
          unit: 'px',
          format: [this.poster.width, this.poster.height]
        })

        // 添加图片到PDF
        pdf.addImage(this.poster.posterUrl, 'PNG', 0, 0, this.poster.width, this.poster.height)
        
        // 下载PDF
        pdf.save(`${this.poster.posterName || 'poster'}.pdf`)
        this.$message.success('PDF导出成功')
      } catch (err) {
        console.error('PDF导出失败:', err)
        this.$message.error('PDF导出失败')
      } finally {
        this.exportingPDF = false
      }
    },

    // 新增：导出SVG功能
    async handleExportSVG() {
      if (!this.poster.templateId) {
        this.$message.warning('请选择模板')
        return
      }

      this.exportingSVG = true
      try {
        const posterCanvas = (this.templateData && this.templateData.canvas) ? this.templateData.canvas : {
          width: this.poster.width,
          height: this.poster.height,
          backgroundColor: '#ffffff'
        }

        const rawObjects = (this.templateData && Array.isArray(this.templateData.objects)) ? this.templateData.objects : []
        const objects = this.resolveVariables(rawObjects, this.poster.variables)

        // 动态加载 fabric
        let mod = null
        try {
          mod = await import('fabric')
        } catch (e) {
          try { mod = require('fabric') } catch (e2) {}
        }
        const fabric = (mod && (mod.fabric || mod.default || mod)) || (typeof window !== 'undefined' ? window.fabric : null)
        if (!fabric) {
          this.$message.error('fabric 加载失败')
          return
        }

        // 创建临时canvas用于SVG导出
        const tempCanvas = new fabric.Canvas(document.createElement('canvas'))
        tempCanvas.setWidth(posterCanvas.width || this.poster.width)
        tempCanvas.setHeight(posterCanvas.height || this.poster.height)
        tempCanvas.backgroundColor = posterCanvas.backgroundColor || '#ffffff'

        // 加载对象，包含自定义属性
        await new Promise((resolve) => {
          tempCanvas.loadFromJSON({
            objects,
            // 指定需要保留的自定义属性
            reviver: (property, value) => {
              if (['variableKey', 'originalSvg', 'svgColors', 'charSpacing'].includes(property)) {
                return value
              }
              return value
            }
          }, () => {
            tempCanvas.renderAll()
            resolve()
          })
        })

        // 导出SVG
        const svgString = tempCanvas.toSVG()
        
        // 创建下载链接
        const blob = new Blob([svgString], { type: 'image/svg+xml' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${this.poster.posterName || 'poster'}.svg`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        this.$message.success('SVG导出成功')
      } catch (err) {
        console.error('SVG导出失败:', err)
        this.$message.error('SVG导出失败')
      } finally {
        this.exportingSVG = false
      }
    }
  }
}
</script>

<style scoped>
</style>

