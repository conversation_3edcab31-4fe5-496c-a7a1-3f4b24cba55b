<template>
  <a-modal
    title="行程流量趋势分析"
    :width="900"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleCancel"
    :footer="null"
    :maskClosable="false"
  >
    <div class="trip-traffic-modal">
      <!-- 基本信息 -->
      <div class="trip-info">
        <h3 class="trip-title">
          <a-icon type="line-chart" class="title-icon" />
          {{ tripInfo.tripName }}
          <span class="trip-id">（编号：{{ tripInfo.id }}）</span>
        </h3>
        <div class="trip-stats">
          <a-row :gutter="16">
            <a-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ tripInfo.totalPv || 0 }}</div>
                <div class="stat-label">总PV</div>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ tripInfo.totalUv || 0 }}</div>
                <div class="stat-label">总UV</div>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ tripInfo.totalIp || 0 }}</div>
                <div class="stat-label">总IP</div>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>

      <!-- 流量趋势图 -->
      <div class="modern-card chart-card">
        <div class="card-header">
          <h4 class="card-title">
            <a-icon type="bar-chart" class="title-icon" />
            近30天流量趋势
          </h4>
          <div class="card-extra">
            <a-tooltip title="近30天数据">
              <a-tag color="blue" class="info-tag">
                <a-icon type="info-circle" />
                近30天
              </a-tag>
            </a-tooltip>
          </div>
        </div>
        
        <div class="chart-container">
          <LineChartMultid 
            v-if="!loading && chartData && chartData.length > 0" 
            :dataSource="chartData"
            :fields="['pv', 'uv', 'ip']"
            :height="300"
          />
          <div v-else-if="loading" class="chart-loading">
            <a-spin size="large" />
            <p>数据加载中...</p>
          </div>
          <div v-else class="chart-loading">
            <a-icon type="info-circle" style="font-size: 32px; color: #aaa;" />
            <p>暂无流量数据</p>
          </div>
        </div>
      </div>
      <!-- 帮助说明 -->
      <div class="traffic-help">
        <div>
          <b>数据说明：</b><br>
          <b>PV</b>（点击数）：点击的总次数（5分钟内同一设备的重复点击只算一次））<br>
          <b>UV</b>（设备数）：有多少设备查看（手机和电脑算不同设备）<br>
          <b>IP</b>（IP数）：同一个WIFI下是一个IP，一个手机一般是一个IP<br><br>
          <b>仅统计近一个月内的数据</b><br>
          <b>点击曲线图下面对应颜色的小横线可以关闭开启某个曲线</b><br>
          <b>本页面是最新实时访问数据</b>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { getAction } from '@/api/manage'
import LineChartMultid from '@/components/chart/LineChartMultid'

export default {
  name: 'TripTrafficModal',
  components: {
    LineChartMultid
  },
  data() {
    return {
      visible: false,
      confirmLoading: false,
      tripInfo: {},
      chartData: [],
      loading: false,
      url: {
        getTripTraffic: '/biz/analysis/tripTraffic'
      }
    }
  },
  methods: {
    show(record) {
      this.visible = true
      this.tripInfo = {
        id: record.id,
        tripName: record.tripName,
        tripFullName: record.tripFullName,
        totalPv: record.pv || 0,
        totalUv: record.uv || 0,
        totalIp: record.ip || 0
      }
      this.loadTripTrafficData(record.id)
    },

    loadTripTrafficData(tripId) {
      this.loading = true
      getAction(this.url.getTripTraffic, { tripId: tripId })
        .then((res) => {
          if (res.success) {
            this.chartData = res.result || []
            let totalPv = 0, totalUv = 0, totalIp = 0
            this.chartData.forEach(item => {
              totalPv += item.pv || 0
              totalUv += item.uv || 0
              totalIp += item.ip || 0
            })
            this.tripInfo.totalPv = totalPv
            this.tripInfo.totalUv = totalUv
            this.tripInfo.totalIp = totalIp
          } else {
            this.$message.error(res.message || '获取流量数据失败')
            this.chartData = []
            this.tripInfo.totalPv = 0
            this.tripInfo.totalUv = 0
            this.tripInfo.totalIp = 0
          }
        })
        .catch((error) => {
          console.error('获取流量数据错误:', error)
          this.$message.error('获取流量数据失败')
          this.chartData = []
          this.tripInfo.totalPv = 0
          this.tripInfo.totalUv = 0
          this.tripInfo.totalIp = 0
        })
        .finally(() => {
          this.loading = false
        })
    },

    handleCancel() {
      this.visible = false
      this.tripInfo = {}
      this.chartData = []
      this.loading = false
    }
  }
}
</script>

<style scoped>
.trip-traffic-modal {
  padding: 16px 0;
}

.trip-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.trip-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.title-icon {
  margin-right: 8px;
  color: #177476;
}

.trip-id {
  font-size: 14px;
  color: #8c8c8c;
  font-weight: normal;
}

.trip-stats {
  margin-top: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #177476;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.modern-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #177476 0%, #177476 100%);
  color: white;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.card-extra .info-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.chart-container {
  min-height: 300px;
  padding: 16px;
}

.chart-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #8c8c8c;
}

.chart-loading p {
  margin-top: 16px;
  margin-bottom: 0;
}

.traffic-help {
  margin-top: 24px;
  padding: 12px 16px;
  background: #f6fffa;
  border-left: 4px solid #177476;
  color: #177476;
  font-size: 14px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
</style> 