package com.woyaotuanjian.modules.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.woyaotuanjian.common.system.vo.LoginUser;
import com.woyaotuanjian.modules.biz.entity.PosterMaterial;
import com.woyaotuanjian.modules.biz.mapper.PosterMaterialMapper;
import com.woyaotuanjian.modules.biz.service.FileService;
import com.woyaotuanjian.modules.biz.service.IPosterMaterialService;
import com.woyaotuanjian.modules.biz.util.SysUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * @Description: 海报素材
 * @Author: system
 * @Date: 2024-08-16
 * @Version: V1.0
 */
@Slf4j
@Service
public class PosterMaterialServiceImpl extends ServiceImpl<PosterMaterialMapper, PosterMaterial> implements IPosterMaterialService {

    @Autowired
    private FileService fileService;

    @Override
    public List<PosterMaterial> getByTypeAndCategory(String type, Long categoryId) {
        QueryWrapper<PosterMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        if (type != null && !type.isEmpty()) {
            queryWrapper.eq("type", type);
        }
        if (categoryId != null && categoryId > 0) {
            queryWrapper.eq("category_id", categoryId);
        }
        queryWrapper.orderByAsc("sort_order");
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }

    @Override
    public PosterMaterial uploadImageMaterial(MultipartFile file, String name, Long categoryId, String tags, String description) {
        try {
            // 上传文件到COS（使用去重功能）
            String fileUrl = fileService.uploadFileWithDuplicateCheck(file.getInputStream(), file.getContentType(), "poster/material/");
            
            // 生成缩略图URL（这里简化处理，直接使用原图）
            String thumbnailUrl = fileUrl;
            
            // 创建素材记录
            PosterMaterial material = new PosterMaterial();
            material.setName(name);
            material.setType("image");
            material.setCategoryId(categoryId);
            material.setFileUrl(fileUrl);
            material.setThumbnailUrl(thumbnailUrl);
            material.setFileSize(file.getSize());
            material.setTags(tags);
            material.setDescription(description);
            material.setDownloadCount(0);
            material.setSortOrder(0);
            material.setStatus(1);
            
            // 设置创建信息
            LoginUser currentUser = SysUserUtil.getCurrentUser();
            if (currentUser != null) {
                material.setCreateBy(currentUser.getUsername());
            }
            material.setCreateTime(new Date());
            
            this.save(material);
            return material;
        } catch (Exception e) {
            log.error("上传图片素材失败，文件名: {}, 大小: {} bytes",
                     file.getOriginalFilename(), file.getSize(), e);
            throw new RuntimeException("上传图片素材失败: " + e.getMessage());
        }
    }

    @Override
    public PosterMaterial addSvgMaterial(String name, String svgContent, Long categoryId, String tags, String description) {
        PosterMaterial material = new PosterMaterial();
        material.setName(name);
        material.setType("svg");
        material.setCategoryId(categoryId);
        material.setSvgContent(svgContent);
        material.setTags(tags);
        material.setDescription(description);
        material.setDownloadCount(0);
        material.setSortOrder(0);
        material.setStatus(1);
        
        // 设置创建信息
        LoginUser currentUser = SysUserUtil.getCurrentUser();
        if (currentUser != null) {
            material.setCreateBy(currentUser.getUsername());
        }
        material.setCreateTime(new Date());
        
        this.save(material);
        return material;
    }

    @Override
    public PosterMaterial addMaskMaterial(String name, String svgContent, Long categoryId, String tags, String description) {
        PosterMaterial material = new PosterMaterial();
        material.setName(name);
        material.setType("mask");
        material.setCategoryId(categoryId);
        material.setSvgContent(svgContent);
        material.setTags(tags);
        material.setDescription(description);
        material.setDownloadCount(0);
        material.setSortOrder(0);
        material.setStatus(1);
        
        // 设置创建信息
        LoginUser currentUser = SysUserUtil.getCurrentUser();
        if (currentUser != null) {
            material.setCreateBy(currentUser.getUsername());
        }
        material.setCreateTime(new Date());
        
        this.save(material);
        return material;
    }

    @Override
    public PosterMaterial uploadFontMaterial(MultipartFile file, String name, Long categoryId, String tags, String description) {
        try {
            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isFontFile(originalFilename)) {
                throw new RuntimeException("不支持的字体文件格式，请上传 .ttf、.otf、.woff、.woff2 格式的字体文件");
            }

            // 根据原始文件名确定正确的 contentType
            String contentType = getFontContentType(originalFilename);

            // 上传字体文件到COS（使用去重功能）
            String fileUrl = fileService.uploadFileWithDuplicateCheck(file.getInputStream(), contentType, "poster/fonts/");

            // 创建字体素材记录
            PosterMaterial material = new PosterMaterial();
            material.setName(name);
            material.setType("font");
            material.setCategoryId(categoryId);
            material.setFileUrl(fileUrl);
            material.setFileSize(file.getSize());
            material.setTags(tags);
            material.setDescription(description);
            material.setDownloadCount(0);
            material.setSortOrder(0);
            material.setStatus(1);

            // 设置创建信息
            LoginUser currentUser = SysUserUtil.getCurrentUser();
            if (currentUser != null) {
                material.setCreateBy(currentUser.getUsername());
            }
            material.setCreateTime(new Date());

            this.save(material);
            return material;
        } catch (Exception e) {
            log.error("上传字体素材失败，文件名: {}, 大小: {} bytes",
                     file.getOriginalFilename(), file.getSize(), e);
            throw new RuntimeException("上传字体素材失败: " + e.getMessage());
        }
    }

    /**
     * 验证是否为支持的字体文件格式
     */
    private boolean isFontFile(String filename) {
        String lowerName = filename.toLowerCase();
        return lowerName.endsWith(".ttf") ||
               lowerName.endsWith(".otf") ||
               lowerName.endsWith(".woff") ||
               lowerName.endsWith(".woff2");
    }

    /**
     * 根据文件名确定字体文件的正确 contentType
     */
    private String getFontContentType(String filename) {
        String lowerName = filename.toLowerCase();
        if (lowerName.endsWith(".ttf")) {
            return "font/ttf";
        } else if (lowerName.endsWith(".otf")) {
            return "font/otf";
        } else if (lowerName.endsWith(".woff")) {
            return "font/woff";
        } else if (lowerName.endsWith(".woff2")) {
            return "font/woff2";
        }
        // 默认返回 ttf 类型
        return "font/ttf";
    }

    @Override
    public void increaseDownloadCount(Long id) {
        PosterMaterial material = this.getById(id);
        if (material != null) {
            material.setDownloadCount((material.getDownloadCount() == null ? 0 : material.getDownloadCount()) + 1);
            this.updateById(material);
        }
    }

    @Override
    public List<PosterMaterial> searchByTags(String tags, String type) {
        QueryWrapper<PosterMaterial> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        if (type != null && !type.isEmpty()) {
            queryWrapper.eq("type", type);
        }
        if (tags != null && !tags.isEmpty()) {
            queryWrapper.like("tags", tags);
        }
        queryWrapper.orderByDesc("download_count");
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }
}