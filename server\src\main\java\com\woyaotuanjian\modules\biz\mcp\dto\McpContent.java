package com.woyaotuanjian.modules.biz.mcp.dto;

import lombok.Data;

/**
 * MCP内容对象
 */
@Data
public class McpContent {
    
    /**
     * 内容类型
     */
    private String type;
    
    /**
     * 文本内容
     */
    private String text;
    
    public McpContent() {
    }
    
    public McpContent(String type, String text) {
        this.type = type;
        this.text = text;
    }
    
    /**
     * 创建文本内容
     */
    public static McpContent text(String text) {
        return new McpContent("text", text);
    }
}