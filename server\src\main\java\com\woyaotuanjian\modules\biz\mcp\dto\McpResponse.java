package com.woyaotuanjian.modules.biz.mcp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * MCP响应基础结构
 * 遵循JSON-RPC 2.0规范
 */
@Data
public class McpResponse {
    
    /**
     * JSON-RPC版本，固定为"2.0"
     */
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    /**
     * 响应结果，成功时包含此字段
     */
    private Object result;
    
    /**
     * 错误信息，失败时包含此字段
     */
    private McpError error;
    
    /**
     * 请求ID，与请求中的ID对应
     */
    private String id;
    
    /**
     * 创建成功响应
     */
    public static McpResponse success(String id, Object result) {
        McpResponse response = new McpResponse();
        response.setId(id);
        response.setResult(result);
        return response;
    }
    
    /**
     * 创建错误响应
     */
    public static McpResponse error(String id, McpError error) {
        McpResponse response = new McpResponse();
        response.setId(id);
        response.setError(error);
        return response;
    }
}