-- 海报模板表
CREATE TABLE `biz_poster_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_desc` varchar(500) DEFAULT NULL COMMENT '模板描述',
  `template_data` longtext NOT NULL COMMENT '模板配置数据(JSON)',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `width` int(11) NOT NULL DEFAULT '750' COMMENT '画布宽度',
  `height` int(11) NOT NULL DEFAULT '1334' COMMENT '画布高度',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 1-启用 0-禁用',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `create_by` int(11) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(11) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海报模板表';