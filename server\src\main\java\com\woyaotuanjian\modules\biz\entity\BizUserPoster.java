package com.woyaotuanjian.modules.biz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 用户海报
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Data
@TableName("biz_user_poster")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="biz_user_poster对象", description="用户海报")
public class BizUserPoster implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    private Long id;
    
    /**海报名称*/
    @Excel(name = "海报名称", width = 15)
    @ApiModelProperty(value = "海报名称")
    private String posterName;
    
    /**模板ID*/
    @ApiModelProperty(value = "模板ID")
    private Long templateId;
    
    /**海报配置数据(JSON)*/
    @ApiModelProperty(value = "海报配置数据(JSON)")
    private String posterData;
    
    /**生成的海报图片URL*/
    @ApiModelProperty(value = "生成的海报图片URL")
    private String posterUrl;
    
    /**缩略图URL*/
    @ApiModelProperty(value = "缩略图URL")
    private String thumbnailUrl;
    
    /**画布宽度*/
    @Excel(name = "画布宽度", width = 15)
    @ApiModelProperty(value = "画布宽度")
    private Integer width;
    
    /**画布高度*/
    @Excel(name = "画布高度", width = 15)
    @ApiModelProperty(value = "画布高度")
    private Integer height;
    
    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private Integer createBy;
    
    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    
    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}