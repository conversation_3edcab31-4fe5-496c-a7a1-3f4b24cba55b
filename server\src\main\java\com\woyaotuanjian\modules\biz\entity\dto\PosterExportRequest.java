package com.woyaotuanjian.modules.biz.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * @Description: 海报导出请求DTO
 * @Author: jeecg-boot
 * @Date: 2025-01-13
 * @Version: V1.0
 */
@Data
@ApiModel(value = "PosterExportRequest", description = "海报导出请求")
public class PosterExportRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "海报ID")
    private Long posterId;
    
    @ApiModelProperty(value = "导出格式", example = "png")
    private String format = "png";
    
    @ApiModelProperty(value = "图片质量", example = "1.0")
    private Double quality = 1.0;
    
    @ApiModelProperty(value = "是否高分辨率", example = "false")
    private Boolean highResolution = false;
    
    @ApiModelProperty(value = "是否包含背景", example = "true")
    private Boolean withBackground = true;
    
    @ApiModelProperty(value = "分辨率倍数", example = "1.0")
    private Double multiplier = 1.0;
    
    @ApiModelProperty(value = "导出宽度")
    private Integer width;
    
    @ApiModelProperty(value = "导出高度")
    private Integer height;
    
    @ApiModelProperty(value = "是否自动下载", example = "true")
    private Boolean autoDownload = true;
    
    @ApiModelProperty(value = "导出备注")
    private String remark;
}