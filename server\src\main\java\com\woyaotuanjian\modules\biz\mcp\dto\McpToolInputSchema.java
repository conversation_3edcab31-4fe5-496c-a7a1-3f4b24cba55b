package com.woyaotuanjian.modules.biz.mcp.dto;

import lombok.Data;

import java.util.Map;
import java.util.List;

/**
 * MCP工具输入参数schema定义
 * 遵循JSON Schema规范
 */
@Data
public class McpToolInputSchema {
    
    /**
     * Schema类型，通常为"object"
     */
    private String type;
    
    /**
     * 属性定义
     */
    private Map<String, McpToolProperty> properties;
    
    /**
     * 必需的属性列表
     */
    private List<String> required;
    
    public McpToolInputSchema(String type, Map<String, McpToolProperty> properties, List<String> required) {
        this.type = type;
        this.properties = properties;
        this.required = required;
    }
}