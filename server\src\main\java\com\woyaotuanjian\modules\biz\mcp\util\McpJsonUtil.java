package com.woyaotuanjian.modules.biz.mcp.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.woyaotuanjian.modules.biz.mcp.dto.McpRequest;
import com.woyaotuanjian.modules.biz.mcp.dto.McpInitializeParams;
import com.woyaotuanjian.modules.biz.mcp.exception.McpException;
import com.woyaotuanjian.modules.biz.mcp.dto.McpError;

import java.util.Map;

/**
 * MCP JSON处理工具类
 */
public class McpJsonUtil {
    
    /**
     * 解析MCP请求JSON
     * 
     * @param jsonStr JSON字符串
     * @return MCP请求对象
     * @throws McpException 解析失败时抛出异常
     */
    public static McpRequest parseRequest(String jsonStr) throws McpException {
        try {
            return JSON.parseObject(jsonStr, McpRequest.class);
        } catch (JSONException e) {
            throw new McpException(McpError.parseError());
        }
    }
    
    /**
     * 将对象转换为JSON字符串
     * 
     * @param obj 要转换的对象
     * @return JSON字符串
     */
    public static String toJsonString(Object obj) {
        return JSON.toJSONString(obj);
    }
    
    /**
     * 验证请求基本格式
     * 
     * @param request MCP请求对象
     * @throws McpException 验证失败时抛出异常
     */
    public static void validateRequest(McpRequest request) throws McpException {
        if (request == null) {
            throw new McpException(McpError.invalidRequest());
        }
        
        if (!McpConstants.JSONRPC_VERSION.equals(request.getJsonrpc())) {
            throw new McpException(McpError.invalidRequest());
        }
        
        if (request.getMethod() == null || request.getMethod().trim().isEmpty()) {
            throw new McpException(McpError.invalidRequest());
        }
    }
    
    /**
     * 解析MCP初始化参数
     * 
     * @param params 参数Map
     * @return 初始化参数对象
     * @throws McpException 解析失败时抛出异常
     */
    public static McpInitializeParams parseInitializeParams(Map<String, Object> params) throws McpException {
        try {
            if (params == null) {
                return new McpInitializeParams();
            }
            
            String jsonStr = JSON.toJSONString(params);
            return JSON.parseObject(jsonStr, McpInitializeParams.class);
        } catch (JSONException e) {
            throw new McpException(McpError.invalidParams("Invalid initialize parameters"));
        }
    }
    
    /**
     * 从参数中获取指定类型的值
     * 
     * @param params 参数Map
     * @param key 参数键
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的值
     * @throws McpException 转换失败时抛出异常
     */
    @SuppressWarnings("unchecked")
    public static <T> T getParam(Object params, String key, Class<T> clazz) throws McpException {
        try {
            if (params == null) {
                return null;
            }
            
            Object value;
            if (params instanceof java.util.Map) {
                value = ((java.util.Map<String, Object>) params).get(key);
            } else {
                // 尝试从JSON对象中获取
                String jsonStr = JSON.toJSONString(params);
                com.alibaba.fastjson.JSONObject jsonObj = JSON.parseObject(jsonStr);
                value = jsonObj.get(key);
            }
            
            if (value == null) {
                return null;
            }
            
            if (clazz.isInstance(value)) {
                return clazz.cast(value);
            }
            
            // 尝试类型转换
            if (clazz == Long.class && value instanceof Number) {
                return clazz.cast(((Number) value).longValue());
            }
            
            if (clazz == Integer.class && value instanceof Number) {
                return clazz.cast(((Number) value).intValue());
            }
            
            if (clazz == String.class) {
                return clazz.cast(value.toString());
            }
            
            return JSON.parseObject(JSON.toJSONString(value), clazz);
            
        } catch (Exception e) {
            throw new McpException(McpError.INVALID_PARAMS, "Invalid parameter: " + key);
        }
    }
}