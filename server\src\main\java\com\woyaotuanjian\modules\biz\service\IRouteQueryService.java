package com.woyaotuanjian.modules.biz.service;

import com.woyaotuanjian.modules.biz.dto.RouteQueryRequest;
import com.woyaotuanjian.modules.biz.dto.RouteQueryResponse;

import java.util.List;

/**
 * 路线查询服务接口
 */
public interface IRouteQueryService {
    
    /**
     * 批量查询路线
     * @param requests 路线查询请求列表
     * @return 路线查询结果列表
     */
    List<RouteQueryResponse> batchQueryRoutes(List<RouteQueryRequest> requests);
} 