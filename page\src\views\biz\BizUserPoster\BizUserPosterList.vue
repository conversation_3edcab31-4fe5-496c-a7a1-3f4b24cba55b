<template>
  <a-card :bordered="false">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="24">
          <a-col :md="6" :sm="8">
            <a-form-item label="海报名称">
              <a-input placeholder="请输入海报名称" @keyup.enter="searchQuery" v-model="queryParam.posterName"></a-input>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <a-form-item label="创建时间">
              <a-range-picker 
                v-model="queryParam.createTimeRange"
                format="YYYY-MM-DD"
                style="width: 100%">
              </a-range-picker>
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="8">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              <a-button @click="handleBatchExport" icon="download" style="margin-left: 8px" :disabled="selectedRowKeys.length === 0">批量导出</a-button>
              <a-button type="primary" @click="handleCreate" icon="plus" style="margin-left: 8px">制作海报</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    
    <!-- 海报卡片展示 -->
    <div class="poster-grid">
      <a-row :gutter="[16, 16]">
        <a-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4" v-for="poster in dataSource" :key="poster.id">
          <a-card 
            hoverable
            class="poster-card"
            :class="{ 'selected': selectedRowKeys.includes(poster.id) }"
            :body-style="{ padding: '12px' }">
            
            <!-- 选择框 -->
            <a-checkbox 
              class="poster-checkbox"
              :checked="selectedRowKeys.includes(poster.id)"
              @change="(e) => handleSelectChange(e, poster.id)">
            </a-checkbox>
            
            <div class="poster-thumbnail" @click="handlePreview(poster)">
              <img 
                v-if="poster.posterUrl || poster.thumbnailUrl" 
                :src="poster.posterUrl || poster.thumbnailUrl" 
                :alt="poster.posterName"
                class="thumbnail-image"
                loading="lazy"
                @error="handleImageError($event, poster)"
                @load="handleImageLoaded($event)">
              <div v-else class="no-thumbnail">
                <a-icon type="picture" style="font-size: 48px; color: #ccc;" />
                <p>暂无预览</p>
              </div>
            </div>
            
            <div class="poster-info">
              <h4 class="poster-title" :title="poster.posterName">{{ poster.posterName }}</h4>
              <p class="poster-meta">
                <span>{{ poster.width }}×{{ poster.height }}</span>
                <span>{{ poster.createTime | formatDate }}</span>
              </p>
            </div>
            
            <div class="poster-actions">
              <a-button-group size="small">
                <a-button @click="handleEdit(poster)" icon="edit">编辑</a-button>
                <a-dropdown>
                  <a-button icon="download">
                    下载 <a-icon type="down" />
                  </a-button>
                  <a-menu slot="overlay">
                    <a-menu-item @click="handleDownload(poster, 'png')">
                      <a-icon type="file-image" />PNG格式
                    </a-menu-item>
                    <a-menu-item @click="handleDownload(poster, 'jpg')">
                      <a-icon type="file-image" />JPG格式
                    </a-menu-item>
                    <a-menu-item @click="handleDownload(poster, 'pdf')">
                      <a-icon type="file-pdf" />PDF格式
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
                <a-dropdown>
                  <a-button icon="more">
                    更多 <a-icon type="down" />
                  </a-button>
                  <a-menu slot="overlay">
                    <a-menu-item @click="handleCopy(poster)">
                      <a-icon type="copy" />复制
                    </a-menu-item>
                    <a-menu-item @click="handleRename(poster)">
                      <a-icon type="edit" />重命名
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="confirmDelete(poster)" class="danger-item">
                      <a-icon type="delete" />删除
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </a-button-group>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
    
    <!-- 分页 -->
    <div class="pagination-wrapper" v-if="ipagination.total > 0">
      <a-pagination
        :current="ipagination.current"
        :total="ipagination.total"
        :pageSize="ipagination.pageSize"
        :showSizeChanger="true"
        :showQuickJumper="true"
        :showTotal="(total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="handleTableChange"
        @showSizeChange="handleTableChange">
      </a-pagination>
    </div>
    
    <!-- 空状态 -->
    <a-empty v-if="dataSource.length === 0 && !loading" description="暂无海报数据">
      <a-button type="primary" @click="handleCreate">制作第一个海报</a-button>
    </a-empty>
    
    <!-- 海报预览弹窗 -->
    <a-modal
      title="海报预览"
      :visible="previewVisible"
      :footer="null"
      :width="800"
      @cancel="previewVisible = false">
      <div class="preview-container" v-if="previewPoster">
        <img 
          :src="previewPoster.posterUrl || previewPoster.thumbnailUrl" 
          :alt="previewPoster.posterName"
          class="preview-image">
      </div>
    </a-modal>
    
    <!-- 重命名弹窗 -->
    <a-modal
      title="重命名海报"
      :visible="renameVisible"
      @ok="handleRenameConfirm"
      @cancel="renameVisible = false">
      <a-form :form="renameForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="海报名称">
          <a-input 
            v-decorator="['posterName', { rules: [{ required: true, message: '请输入海报名称' }] }]"
            placeholder="请输入海报名称">
          </a-input>
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 批量导出弹窗 -->
    <a-modal
      title="批量导出海报"
      :visible="batchExportVisible"
      @ok="handleBatchExportConfirm"
      @cancel="batchExportVisible = false"
      :confirmLoading="batchExportLoading"
      okText="开始导出"
      cancelText="取消">
      <a-form layout="vertical">
        <a-form-item label="导出格式">
          <a-radio-group v-model="batchExportFormat">
            <a-radio value="png">PNG</a-radio>
            <a-radio value="jpg">JPG</a-radio>
            <a-radio value="pdf">PDF</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="导出质量" v-if="batchExportFormat !== 'pdf'">
          <a-slider 
            v-model="batchExportQuality" 
            :min="0.1" 
            :max="1" 
            :step="0.1"
            :marks="{ 0.1: '低', 0.5: '中', 1: '高' }">
          </a-slider>
        </a-form-item>
        
        <a-form-item>
          <p>已选择 <strong>{{ selectedRowKeys.length }}</strong> 个海报</p>
          <a-progress 
            v-if="batchExportLoading" 
            :percent="batchExportProgress" 
            status="active" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import moment from 'moment'
// Poster navigation removed for compatibility

export default {
  name: 'BizUserPosterList',
  mixins: [JeecgListMixin],
  data() {
    return {
      description: '用户海报管理页面',
      // 表格列定义（兼容JeecgListMixin）
      columns: [
        {
          dataIndex: 'posterName',
          title: '海报名称'
        },
        {
          dataIndex: 'width',
          title: '宽度'
        },
        {
          dataIndex: 'height',
          title: '高度'
        },
        {
          dataIndex: 'thumbnailUrl',
          title: '缩略图'
        },
        {
          dataIndex: 'posterUrl',
          title: '海报地址'
        },
        {
          dataIndex: 'createTime',
          title: '创建时间'
        }
      ],
      // 查询参数
      queryParam: {
        posterName: '',
        createTimeRange: []
      },
      // 预览相关
      previewVisible: false,
      previewPoster: null,
      // 重命名相关
      renameVisible: false,
      renameForm: this.$form.createForm(this),
      renamePoster: null,
      // 批量导出相关
      batchExportVisible: false,
      batchExportLoading: false,
      batchExportFormat: 'png',
      batchExportQuality: 1.0,
      batchExportProgress: 0,
      // 选择相关
      selectedRowKeys: [],
      url: {
        list: '/biz/userPoster/list',
        delete: '/biz/userPoster/delete',
        deleteBatch: '/biz/userPoster/deleteBatch',
        save: '/biz/userPoster/save',
        copy: '/biz/userPoster/copy'
      }
    }
  },
  filters: {
    formatDate(date) {
      return date ? moment(date).format('MM-DD HH:mm') : ''
    }
  },
  methods: {
    // 重写搜索方法以处理时间范围
    searchQuery() {
      // 处理时间范围查询
      if (this.queryParam.createTimeRange && this.queryParam.createTimeRange.length === 2) {
        this.queryParam.createTime_begin = this.queryParam.createTimeRange[0].format('YYYY-MM-DD')
        this.queryParam.createTime_end = this.queryParam.createTimeRange[1].format('YYYY-MM-DD')
      } else {
        delete this.queryParam.createTime_begin
        delete this.queryParam.createTime_end
      }
      
      this.loadData(1)
    },
    
    handleCreate() {
      // 跳转到海报制作页面
      this.$router.push({ path: '/poster/user/edit', query: { mode: 'add' } })
    },
    
    handleEdit(record) {
      // 跳转到海报编辑页面
      this.$router.push({ path: '/poster/user/edit', query: { id: record.id, mode: 'edit' } })
    },
    
    handlePreview(record) {
      this.previewPoster = record
      this.previewVisible = true
    },
    
    handleDownload(record, format = 'png') {
      if (!record.posterUrl && !record.thumbnailUrl) {
        this.$message.warning('海报尚未生成，无法下载')
        return
      }
      
      const imageUrl = record.posterUrl || record.thumbnailUrl
      
      if (format === 'pdf') {
        this.downloadAsPDF(record, imageUrl)
      } else {
        this.downloadAsImage(record, imageUrl, format)
      }
    },
    
    async downloadAsImage(record, imageUrl, format) {
      try {
        // 获取图片数据
        const response = await fetch(imageUrl)
        const blob = await response.blob()
        
        if (format !== 'png') {
          // 转换格式
          const convertedBlob = await this.convertImageFormat(blob, format)
          this.downloadBlob(convertedBlob, `${record.posterName}.${format}`)
        } else {
          this.downloadBlob(blob, `${record.posterName}.png`)
        }
        
        this.$message.success('下载已开始')
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败')
      }
    },
    
    async downloadAsPDF(record, imageUrl) {
      try {
        const { jsPDF } = await import('jspdf')
        
        // 创建PDF
        const pdf = new jsPDF({
          orientation: record.height > record.width ? 'portrait' : 'landscape',
          unit: 'px',
          format: [record.width, record.height]
        })
        
        // 添加图片到PDF
        pdf.addImage(imageUrl, 'PNG', 0, 0, record.width, record.height)
        
        // 下载PDF
        pdf.save(`${record.posterName}.pdf`)
        
        this.$message.success('PDF下载已开始')
      } catch (error) {
        console.error('PDF生成失败:', error)
        this.$message.error('PDF生成失败')
      }
    },
    
    async convertImageFormat(blob, format) {
      return new Promise((resolve) => {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        const img = new Image()
        
        img.onload = () => {
          canvas.width = img.width
          canvas.height = img.height
          
          // 如果是JPG格式，先填充白色背景
          if (format === 'jpg') {
            ctx.fillStyle = '#ffffff'
            ctx.fillRect(0, 0, canvas.width, canvas.height)
          }
          
          ctx.drawImage(img, 0, 0)
          
          canvas.toBlob(resolve, `image/${format}`, 0.9)
        }
        
        img.src = URL.createObjectURL(blob)
      })
    },
    
    downloadBlob(blob, fileName) {
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    },
    
    handleCopy(record) {
      this.$confirm({
        title: '确认复制',
        content: '确定要复制这个海报吗？',
        onOk: () => {
          this.copyPoster(record.id)
        }
      })
    },
    
    copyPoster(id) {
      this.loading = true
      this.$http.post(this.url.copy, null, {
        params: { id }
      }).then(res => {
        if (res.success) {
          this.$message.success('复制成功')
          this.loadData()
        } else {
          this.$message.error(res.message || '复制失败')
        }
      }).catch(err => {
        this.$message.error('复制失败：' + err.message)
      }).finally(() => {
        this.loading = false
      })
    },
    
    handleRename(record) {
      this.renamePoster = record
      this.renameForm.setFieldsValue({
        posterName: record.posterName
      })
      this.renameVisible = true
    },
    
    handleRenameConfirm() {
      this.renameForm.validateFields((err, values) => {
        if (!err) {
          const updateData = {
            id: this.renamePoster.id,
            posterName: values.posterName
          }
          
          this.$http.post(this.url.save, updateData).then(res => {
            if (res.success) {
              this.$message.success('重命名成功')
              this.renameVisible = false
              this.loadData()
            } else {
              this.$message.error(res.message || '重命名失败')
            }
          }).catch(err => {
            this.$message.error('重命名失败：' + err.message)
          })
        }
      })
    },
    
    confirmDelete(record) {
      this.$confirm({
        title: '确认删除',
        content: '确定要删除这个海报吗？删除后无法恢复。',
        onOk: () => {
          this.handleDelete(record.id)
        }
      })
    },
    
    // 选择相关方法
    handleSelectChange(e, id) {
      if (e.target.checked) {
        this.selectedRowKeys.push(id)
      } else {
        const index = this.selectedRowKeys.indexOf(id)
        if (index > -1) {
          this.selectedRowKeys.splice(index, 1)
        }
      }
    },
    
    // 批量导出
    handleBatchExport() {
      if (this.selectedRowKeys.length === 0) {
        this.$message.warning('请先选择要导出的海报')
        return
      }
      
      this.batchExportVisible = true
      this.batchExportProgress = 0
    },
    
    async handleBatchExportConfirm() {
      this.batchExportLoading = true
      this.batchExportProgress = 0
      
      try {
        const selectedPosters = this.dataSource.filter(poster => 
          this.selectedRowKeys.includes(poster.id)
        )
        
        if (selectedPosters.length === 1) {
          // 单个文件直接下载
          const poster = selectedPosters[0]
          await this.handleDownload(poster, this.batchExportFormat)
          this.batchExportProgress = 100
        } else {
          // 多个文件打包下载
          await this.downloadMultipleAsZip(selectedPosters)
        }
        
        this.$message.success('导出完成')
        this.batchExportVisible = false
        this.selectedRowKeys = []
        
      } catch (error) {
        console.error('批量导出失败:', error)
        this.$message.error('导出失败：' + error.message)
      } finally {
        this.batchExportLoading = false
        this.batchExportProgress = 0
      }
    },
    
    async downloadMultipleAsZip(posters) {
      const JSZip = (await import('jszip')).default
      const zip = new JSZip()
      
      const total = posters.length
      let completed = 0
      
      for (const poster of posters) {
        try {
          const imageUrl = poster.posterUrl || poster.thumbnailUrl
          if (!imageUrl) {
            console.warn(`海报 ${poster.posterName} 没有图片URL，跳过`)
            continue
          }
          
          if (this.batchExportFormat === 'pdf') {
            // PDF格式
            const pdfBlob = await this.generatePDFBlob(poster, imageUrl)
            zip.file(`${poster.posterName}.pdf`, pdfBlob)
          } else {
            // 图片格式
            const response = await fetch(imageUrl)
            const blob = await response.blob()
            
            let finalBlob = blob
            if (this.batchExportFormat !== 'png') {
              finalBlob = await this.convertImageFormat(blob, this.batchExportFormat)
            }
            
            zip.file(`${poster.posterName}.${this.batchExportFormat}`, finalBlob)
          }
          
          completed++
          this.batchExportProgress = Math.round((completed / total) * 80) // 80%用于处理文件
          
        } catch (error) {
          console.error(`处理海报 ${poster.posterName} 失败:`, error)
        }
      }
      
      this.batchExportProgress = 90
      
      // 生成ZIP文件
      const zipBlob = await zip.generateAsync({ type: 'blob' })
      
      this.batchExportProgress = 100
      
      // 下载ZIP文件
      const fileName = `海报批量导出_${moment().format('YYYYMMDD_HHmmss')}.zip`
      this.downloadBlob(zipBlob, fileName)
    },
    
    async generatePDFBlob(poster, imageUrl) {
      const { jsPDF } = await import('jspdf')
      
      const pdf = new jsPDF({
        orientation: poster.height > poster.width ? 'portrait' : 'landscape',
        unit: 'px',
        format: [poster.width, poster.height]
      })
      
      pdf.addImage(imageUrl, 'PNG', 0, 0, poster.width, poster.height)
      
      return pdf.output('blob')
    },
    
    // 处理图片加载错误
    handleImageError(event, poster) {
      console.warn(`海报图片加载失败: ${poster.posterName}`, event)
      // 如果posterUrl失败，尝试使用thumbnailUrl
      if (event.target.src === poster.posterUrl && poster.thumbnailUrl) {
        event.target.src = poster.thumbnailUrl
      } else {
        // 显示默认错误状态
        event.target.style.display = 'none'
        const parentEl = event.target.parentElement
        if (parentEl && !parentEl.querySelector('.image-error')) {
          const errorDiv = document.createElement('div')
          errorDiv.className = 'image-error no-thumbnail'
          errorDiv.innerHTML = '<i class="anticon anticon-picture" style="font-size: 48px; color: #ccc;"></i><p>图片加载失败</p>'
          parentEl.appendChild(errorDiv)
        }
      }
    },
    
    // 处理图片加载成功
    handleImageLoaded(event) {
      // 图片加载成功后可以添加一些动画效果
      event.target.style.opacity = '1'
    }
  }
}
</script>

<style scoped>
.poster-grid {
  margin-top: 16px;
}

.poster-card {
  display: flex;
  flex-direction: column;
  position: relative;
}

.poster-card.selected {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

.poster-checkbox {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
}

.poster-thumbnail {
  aspect-ratio: 3/4;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

.thumbnail-image:hover {
  opacity: 0.9;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.no-thumbnail {
  text-align: center;
  color: #999;
}

.no-thumbnail p {
  margin: 8px 0 0 0;
  font-size: 12px;
}

.poster-info {
  flex: 1;
  padding: 12px 0 8px 0;
}

.poster-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.poster-meta {
  margin: 0;
  font-size: 12px;
  color: #999;
  display: flex;
  justify-content: space-between;
}

.poster-actions {
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

.pagination-wrapper {
  margin-top: 24px;
  text-align: center;
}

.preview-container {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 600px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.danger-item {
  color: #ff4d4f;
}

.danger-item:hover {
  background-color: #fff2f0;
}
</style>