package com.woyaotuanjian.modules.biz.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.woyaotuanjian.common.api.vo.Result;
import com.woyaotuanjian.common.aspect.annotation.AutoLog;
import com.woyaotuanjian.modules.biz.entity.PosterMaterial;
import com.woyaotuanjian.modules.biz.entity.PosterMaterialCategory;
import com.woyaotuanjian.modules.biz.service.IPosterMaterialCategoryService;
import com.woyaotuanjian.modules.biz.service.IPosterMaterialService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description: 海报素材管理
 * @Author: system
 * @Date: 2024-08-16
 * @Version: V1.0
 */
@Api(tags="海报素材管理")
@RestController
@RequestMapping("/biz/posterMaterial")
@Slf4j
public class PosterMaterialController {

    @Autowired
    private IPosterMaterialService posterMaterialService;
    
    @Autowired
    private IPosterMaterialCategoryService categoryService;

    /**
     * 获取分类列表
     */
    @AutoLog(value = "海报素材-获取分类列表")
    @ApiOperation(value="获取分类列表", notes="获取分类列表")
    @GetMapping(value = "/categories")
    public Result<?> getCategories() {
        try {
            List<PosterMaterialCategory> categories = categoryService.getTreeList();
            return Result.ok(categories);
        } catch (Exception e) {
            log.error("获取分类列表失败", e);
            return Result.error("获取分类列表失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询素材列表
     */
    @AutoLog(value = "海报素材-分页查询")
    @ApiOperation(value="分页查询素材列表", notes="分页查询素材列表")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(@RequestParam(name="type", required=false) String type,
                                   @RequestParam(name="categoryId", required=false) Long categoryId,
                                   @RequestParam(name="tags", required=false) String tags,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="20") Integer pageSize) {
        try {
            QueryWrapper<PosterMaterial> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            
            if (type != null && !type.isEmpty()) {
                queryWrapper.eq("type", type);
            }
            if (categoryId != null && categoryId > 0) {
                queryWrapper.eq("category_id", categoryId);
            }
            if (tags != null && !tags.isEmpty()) {
                queryWrapper.like("tags", tags);
            }
            
            queryWrapper.orderByAsc("sort_order");
            queryWrapper.orderByDesc("create_time");
            
            Page<PosterMaterial> page = new Page<>(pageNo, pageSize);
            IPage<PosterMaterial> pageList = posterMaterialService.page(page, queryWrapper);
            
            return Result.ok(pageList);
        } catch (Exception e) {
            log.error("查询素材列表失败", e);
            return Result.error("查询素材列表失败: " + e.getMessage());
        }
    }

    /**
     * 上传图片素材
     */
    @ApiOperation(value="上传图片素材", notes="上传图片素材")
    @PostMapping(value = "/uploadImage")
    public Result<?> uploadImage(@RequestParam("file") MultipartFile file,
                                @RequestParam("name") String name,
                                @RequestParam(value = "categoryId", required = false) Long categoryId,
                                @RequestParam(value = "tags", required = false) String tags,
                                @RequestParam(value = "description", required = false) String description) {
        try {
            if (file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }
            
            PosterMaterial material = posterMaterialService.uploadImageMaterial(file, name, categoryId, tags, description);
            return Result.ok(material);
        } catch (Exception e) {
            log.error("上传图片素材失败", e);
            return Result.error("上传图片素材失败: " + e.getMessage());
        }
    }

    /**
     * 添加SVG素材
     */
    @AutoLog(value = "海报素材-添加SVG素材")
    @ApiOperation(value="添加SVG素材", notes="添加SVG素材")
    @PostMapping(value = "/addSvg")
    public Result<?> addSvg(@RequestBody PosterMaterial material) {
        try {
            if (material.getSvgContent() == null || material.getSvgContent().isEmpty()) {
                return Result.error("SVG内容不能为空");
            }
            
            PosterMaterial result = posterMaterialService.addSvgMaterial(
                material.getName(), 
                material.getSvgContent(), 
                material.getCategoryId(), 
                material.getTags(), 
                material.getDescription()
            );
            return Result.ok(result);
        } catch (Exception e) {
            log.error("添加SVG素材失败", e);
            return Result.error("添加SVG素材失败: " + e.getMessage());
        }
    }

    /**
     * 添加遮罩素材
     */
    @AutoLog(value = "海报素材-添加遮罩素材")
    @ApiOperation(value="添加遮罩素材", notes="添加遮罩素材")
    @PostMapping(value = "/addMask")
    public Result<?> addMask(@RequestBody PosterMaterial material) {
        try {
            if (material.getSvgContent() == null || material.getSvgContent().isEmpty()) {
                return Result.error("遮罩SVG内容不能为空");
            }

            PosterMaterial result = posterMaterialService.addMaskMaterial(
                material.getName(),
                material.getSvgContent(),
                material.getCategoryId(),
                material.getTags(),
                material.getDescription()
            );
            return Result.ok(result);
        } catch (Exception e) {
            log.error("添加遮罩素材失败", e);
            return Result.error("添加遮罩素材失败: " + e.getMessage());
        }
    }

    /**
     * 上传字体素材
     */
    @AutoLog(value = "海报素材-上传字体素材")
    @ApiOperation(value="上传字体素材", notes="上传字体素材")
    @PostMapping(value = "/uploadFont")
    public Result<?> uploadFont(@RequestParam("file") MultipartFile file,
                               @RequestParam("name") String name,
                               @RequestParam(value = "categoryId", required = false) Long categoryId,
                               @RequestParam(value = "tags", required = false) String tags,
                               @RequestParam(value = "description", required = false) String description) {
        try {
            if (file.isEmpty()) {
                return Result.error("请选择要上传的字体文件");
            }

            PosterMaterial material = posterMaterialService.uploadFontMaterial(file, name, categoryId, tags, description);
            return Result.ok(material);
        } catch (Exception e) {
            log.error("上传字体素材失败，文件名: {}, 大小: {} bytes",
                     file.getOriginalFilename(), file.getSize(), e);
            return Result.error("上传字体素材失败: " + e.getMessage());
        }
    }

    /**
     * 删除素材
     */
    @AutoLog(value = "海报素材-删除")
    @ApiOperation(value="删除素材", notes="删除素材")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id", required=true) Long id) {
        try {
            posterMaterialService.removeById(id);
            return Result.ok("删除成功!");
        } catch (Exception e) {
            log.error("删除素材失败", e);
            return Result.error("删除素材失败: " + e.getMessage());
        }
    }

    /**
     * 增加使用次数
     */
    @AutoLog(value = "海报素材-增加使用次数")
    @ApiOperation(value="增加使用次数", notes="增加使用次数")
    @PostMapping(value = "/increaseDownload")
    public Result<?> increaseDownload(@RequestParam(name="id", required=true) Long id) {
        try {
            posterMaterialService.increaseDownloadCount(id);
            return Result.ok("操作成功!");
        } catch (Exception e) {
            log.error("增加使用次数失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 根据标签搜索
     */
    @AutoLog(value = "海报素材-标签搜索")
    @ApiOperation(value="根据标签搜索", notes="根据标签搜索")
    @GetMapping(value = "/searchByTags")
    public Result<?> searchByTags(@RequestParam(name="tags", required=true) String tags,
                                  @RequestParam(name="type", required=false) String type) {
        try {
            List<PosterMaterial> materials = posterMaterialService.searchByTags(tags, type);
            return Result.ok(materials);
        } catch (Exception e) {
            log.error("标签搜索失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }
}