package com.woyaotuanjian.modules.biz.mcp.util;

/**
 * MCP协议常量
 */
public class McpConstants {
    
    /**
     * JSON-RPC版本
     */
    public static final String JSONRPC_VERSION = "2.0";
    
    /**
     * MCP协议版本
     */
    public static final String MCP_PROTOCOL_VERSION = "2025-06-18";
    
    /**
     * 服务器信息
     */
    public static final String SERVER_NAME = "Travel Query MCP Server";
    public static final String SERVER_VERSION = "1.0.0";
    
    /**
     * MCP方法名
     */
    public static final String METHOD_INITIALIZE = "initialize";
    public static final String METHOD_TOOLS_LIST = "tools/list";
    public static final String METHOD_TOOLS_CALL = "tools/call";
    
    /**
     * 工具名称
     */
    public static final String TOOL_GET_TRIP_DETAIL = "get_trip_detail";
    
    /**
     * 内容类型
     */
    public static final String CONTENT_TYPE_TEXT = "text";
    
    /**
     * JSON Schema类型
     */
    public static final String SCHEMA_TYPE_OBJECT = "object";
    public static final String SCHEMA_TYPE_INTEGER = "integer";
    public static final String SCHEMA_TYPE_STRING = "string";
}