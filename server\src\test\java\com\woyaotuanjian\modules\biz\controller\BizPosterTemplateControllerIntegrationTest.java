package com.woyaotuanjian.modules.biz.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.woyaotuanjian.modules.biz.entity.BizPosterTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 海报模板控制器集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class BizPosterTemplateControllerIntegrationTest {

    @Autowired
    private BizPosterTemplateController controller;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void testControllerExists() {
        assertNotNull(controller, "Controller should be autowired");
    }

    @Test
    void testTemplateValidation() {
        // Test template validation
        BizPosterTemplate template = new BizPosterTemplate();
        
        // Test required fields
        template.setTemplateName("测试模板");
        template.setWidth(750);
        template.setHeight(1334);
        
        assertNotNull(template.getTemplateName());
        assertTrue(template.getWidth() > 0);
        assertTrue(template.getHeight() > 0);
    }

    @Test
    void testTemplateDataSerialization() throws Exception {
        // Test JSON serialization/deserialization
        BizPosterTemplate template = new BizPosterTemplate();
        template.setId(1L);
        template.setTemplateName("测试模板");
        template.setTemplateDesc("测试描述");
        template.setTemplateData("{\"canvas\":{\"width\":750,\"height\":1334,\"backgroundColor\":\"#ffffff\"}}");
        template.setWidth(750);
        template.setHeight(1334);
        template.setStatus(1);
        template.setSortOrder(0);

        // Serialize to JSON
        String json = objectMapper.writeValueAsString(template);
        assertNotNull(json);
        assertTrue(json.contains("测试模板"));

        // Deserialize from JSON
        BizPosterTemplate deserialized = objectMapper.readValue(json, BizPosterTemplate.class);
        assertEquals(template.getTemplateName(), deserialized.getTemplateName());
        assertEquals(template.getWidth(), deserialized.getWidth());
        assertEquals(template.getHeight(), deserialized.getHeight());
    }

    @Test
    void testApiEndpointsMapping() {
        // Verify that all required API endpoints are mapped
        // This is a structural test to ensure the controller has all required methods
        
        Class<?> controllerClass = BizPosterTemplateController.class;
        
        // Check that required methods exist
        assertDoesNotThrow(() -> controllerClass.getMethod("queryPageList", 
            BizPosterTemplate.class, Integer.class, Integer.class, javax.servlet.http.HttpServletRequest.class));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("add", BizPosterTemplate.class));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("edit", BizPosterTemplate.class));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("delete", Long.class));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("queryById", String.class));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("listEnabled"));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("copyTemplate", Long.class));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("enableTemplate", Long.class));
        
        assertDoesNotThrow(() -> controllerClass.getMethod("disableTemplate", Long.class));
    }
}