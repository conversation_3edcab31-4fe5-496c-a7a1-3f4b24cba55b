package com.woyaotuanjian.modules.biz.entity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 行程海报数据
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Data
@ApiModel(value = "TripPosterData", description = "行程海报数据")
public class TripPosterData {
    
    @ApiModelProperty(value = "行程ID")
    private Long tripId;
    
    @ApiModelProperty(value = "行程名称")
    private String tripName;
    
    @ApiModelProperty(value = "行程完整名称")
    private String tripFullName;
    
    @ApiModelProperty(value = "旅游天数")
    private Integer dayNum;
    
    @ApiModelProperty(value = "价格")
    private BigDecimal price;
    
    @ApiModelProperty(value = "主图URL")
    private String imgUrl;
    
    @ApiModelProperty(value = "特色简介")
    private String advantageDesc;
    
    @ApiModelProperty(value = "行程标签")
    private String tripTag;
    
    @ApiModelProperty(value = "行程提示")
    private String tripTip;
    
    @ApiModelProperty(value = "作者")
    private String author;
    
    @ApiModelProperty(value = "行程码")
    private String code;
}