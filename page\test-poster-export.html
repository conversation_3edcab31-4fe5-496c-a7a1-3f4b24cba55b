<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海报导出功能测试</title>
    <script src="https://unpkg.com/fabric@6.7.1/dist/fabric.min.js"></script>
    <script src="https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script src="https://unpkg.com/jspdf@3.0.1/dist/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .canvas-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        button {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            transition: all 0.3s;
        }
        button:hover {
            border-color: #1890ff;
            color: #1890ff;
        }
        button.primary {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }
        button.primary:hover {
            background: #40a9ff;
        }
        .export-options {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
        }
        .export-options h3 {
            margin-top: 0;
        }
        .format-options {
            display: flex;
            gap: 15px;
            margin: 10px 0;
        }
        .quality-slider {
            margin: 10px 0;
        }
        input[type="range"] {
            width: 200px;
        }
        .progress {
            margin: 10px 0;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background: #1890ff;
            transition: width 0.3s;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background: #f6f6f6;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>海报导出功能测试</h1>
        
        <div class="controls">
            <button onclick="addText()">添加文本</button>
            <button onclick="addImage()">添加图片</button>
            <button onclick="addRect()">添加矩形</button>
            <button onclick="addCircle()">添加圆形</button>
            <button onclick="clearCanvas()">清空画布</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="posterCanvas" width="750" height="1334" style="border: 1px solid #ddd; background: white;"></canvas>
        </div>
        
        <div class="export-options">
            <h3>导出选项</h3>
            
            <div class="format-options">
                <label><input type="radio" name="format" value="png" checked> PNG</label>
                <label><input type="radio" name="format" value="jpg"> JPG</label>
                <label><input type="radio" name="format" value="pdf"> PDF</label>
            </div>
            
            <div class="quality-slider">
                <label>图片质量: <span id="qualityValue">1.0</span></label><br>
                <input type="range" id="qualitySlider" min="0.1" max="1" step="0.1" value="1" onchange="updateQuality()">
            </div>
            
            <div class="controls">
                <button class="primary" onclick="exportPoster()">导出海报</button>
                <button onclick="testHtml2Canvas()">测试HTML2Canvas</button>
                <button onclick="generateThumbnail()">生成缩略图</button>
            </div>
            
            <div class="progress" id="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let canvas;
        let fabricCanvas;
        
        // 初始化画布
        function initCanvas() {
            canvas = document.getElementById('posterCanvas');
            fabricCanvas = new fabric.Canvas('posterCanvas', {
                width: 750,
                height: 1334,
                backgroundColor: '#ffffff'
            });
            
            log('画布初始化完成');
        }
        
        // 日志函数
        function log(message) {
            const logElement = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${time}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // 更新质量显示
        function updateQuality() {
            const slider = document.getElementById('qualitySlider');
            const value = document.getElementById('qualityValue');
            value.textContent = slider.value;
        }
        
        // 显示进度
        function showProgress(percent) {
            const progress = document.getElementById('progress');
            const progressBar = document.getElementById('progressBar');
            progress.style.display = 'block';
            progressBar.style.width = percent + '%';
            
            if (percent >= 100) {
                setTimeout(() => {
                    progress.style.display = 'none';
                }, 1000);
            }
        }
        
        // 添加文本
        function addText() {
            const text = new fabric.Textbox('测试文本', {
                left: Math.random() * 500 + 100,
                top: Math.random() * 800 + 100,
                width: 200,
                fontSize: 24,
                fill: '#333333',
                fontFamily: 'Arial'
            });
            
            fabricCanvas.add(text);
            fabricCanvas.setActiveObject(text);
            log('添加文本元素');
        }
        
        // 添加图片
        function addImage() {
            const imageUrl = 'data:image/svg+xml;base64,' + btoa(`
                <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
                    <rect width="200" height="150" fill="#e6f7ff" stroke="#1890ff" stroke-width="2"/>
                    <text x="100" y="80" text-anchor="middle" fill="#1890ff" font-size="16">示例图片</text>
                </svg>
            `);
            
            fabric.Image.fromURL(imageUrl, (img) => {
                img.set({
                    left: Math.random() * 400 + 100,
                    top: Math.random() * 800 + 100,
                    scaleX: 0.8,
                    scaleY: 0.8
                });
                
                fabricCanvas.add(img);
                fabricCanvas.setActiveObject(img);
                log('添加图片元素');
            });
        }
        
        // 添加矩形
        function addRect() {
            const rect = new fabric.Rect({
                left: Math.random() * 500 + 100,
                top: Math.random() * 800 + 100,
                width: 150,
                height: 100,
                fill: '#ff7875',
                stroke: '#ff4d4f',
                strokeWidth: 2
            });
            
            fabricCanvas.add(rect);
            fabricCanvas.setActiveObject(rect);
            log('添加矩形元素');
        }
        
        // 添加圆形
        function addCircle() {
            const circle = new fabric.Circle({
                left: Math.random() * 500 + 100,
                top: Math.random() * 800 + 100,
                radius: 60,
                fill: '#95de64',
                stroke: '#52c41a',
                strokeWidth: 2
            });
            
            fabricCanvas.add(circle);
            fabricCanvas.setActiveObject(circle);
            log('添加圆形元素');
        }
        
        // 清空画布
        function clearCanvas() {
            fabricCanvas.clear();
            fabricCanvas.setBackgroundColor('#ffffff', fabricCanvas.renderAll.bind(fabricCanvas));
            log('清空画布');
        }
        
        // 导出海报
        async function exportPoster() {
            try {
                showProgress(10);
                log('开始导出海报...');
                
                const format = document.querySelector('input[name="format"]:checked').value;
                const quality = parseFloat(document.getElementById('qualitySlider').value);
                
                // 取消选择状态
                fabricCanvas.discardActiveObject();
                fabricCanvas.renderAll();
                
                showProgress(30);
                
                let downloadUrl, fileName;
                
                if (format === 'pdf') {
                    const result = await exportToPDF();
                    downloadUrl = result.url;
                    fileName = result.fileName;
                } else {
                    const result = await exportToImage(format, quality);
                    downloadUrl = result.url;
                    fileName = result.fileName;
                }
                
                showProgress(80);
                
                // 下载文件
                downloadFile(downloadUrl, fileName);
                
                showProgress(100);
                log(`导出完成: ${fileName}`);
                
            } catch (error) {
                log('导出失败: ' + error.message);
                console.error('Export failed:', error);
            }
        }
        
        // 导出为图片
        async function exportToImage(format, quality) {
            return new Promise((resolve, reject) => {
                try {
                    const dataURL = fabricCanvas.toDataURL({
                        format: format === 'jpg' ? 'jpeg' : 'png',
                        quality: quality,
                        multiplier: 1
                    });
                    
                    const fileName = `海报_${Date.now()}.${format}`;
                    
                    resolve({
                        url: dataURL,
                        fileName: fileName
                    });
                } catch (error) {
                    reject(error);
                }
            });
        }
        
        // 导出为PDF
        async function exportToPDF() {
            return new Promise((resolve, reject) => {
                try {
                    const dataURL = fabricCanvas.toDataURL({
                        format: 'png',
                        quality: 1.0,
                        multiplier: 1
                    });
                    
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF({
                        orientation: 'portrait',
                        unit: 'px',
                        format: [750, 1334]
                    });
                    
                    pdf.addImage(dataURL, 'PNG', 0, 0, 750, 1334);
                    
                    const pdfBlob = pdf.output('blob');
                    const pdfUrl = URL.createObjectURL(pdfBlob);
                    
                    const fileName = `海报_${Date.now()}.pdf`;
                    
                    resolve({
                        url: pdfUrl,
                        fileName: fileName
                    });
                } catch (error) {
                    reject(error);
                }
            });
        }
        
        // 测试HTML2Canvas
        async function testHtml2Canvas() {
            try {
                log('开始HTML2Canvas测试...');
                showProgress(20);
                
                const canvasElement = document.getElementById('posterCanvas');
                const screenshot = await html2canvas(canvasElement, {
                    backgroundColor: '#ffffff',
                    scale: 1,
                    useCORS: true,
                    allowTaint: false
                });
                
                showProgress(60);
                
                const dataURL = screenshot.toDataURL('image/png', 1.0);
                const fileName = `HTML2Canvas_${Date.now()}.png`;
                
                downloadFile(dataURL, fileName);
                
                showProgress(100);
                log('HTML2Canvas测试完成');
                
            } catch (error) {
                log('HTML2Canvas测试失败: ' + error.message);
                console.error('HTML2Canvas test failed:', error);
            }
        }
        
        // 生成缩略图
        async function generateThumbnail() {
            try {
                log('生成缩略图...');
                showProgress(30);
                
                const dataURL = fabricCanvas.toDataURL({
                    format: 'png',
                    quality: 0.8,
                    multiplier: 0.2 // 缩小到20%
                });
                
                const fileName = `缩略图_${Date.now()}.png`;
                downloadFile(dataURL, fileName);
                
                showProgress(100);
                log('缩略图生成完成');
                
            } catch (error) {
                log('缩略图生成失败: ' + error.message);
                console.error('Thumbnail generation failed:', error);
            }
        }
        
        // 下载文件
        function downloadFile(url, fileName) {
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // 清理blob URL
            if (url.startsWith('blob:')) {
                setTimeout(() => {
                    URL.revokeObjectURL(url);
                }, 1000);
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            initCanvas();
            
            // 添加一些示例内容
            setTimeout(() => {
                addText();
                addRect();
                addCircle();
            }, 500);
        });
    </script>
</body>
</html>