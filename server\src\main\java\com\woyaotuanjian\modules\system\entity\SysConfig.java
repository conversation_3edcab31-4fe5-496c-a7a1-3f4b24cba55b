package com.woyaotuanjian.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 系统配置管理
 * @Author: jeecg-boot
 * @Date: 2025-01-10
 * @Version: V1.0
 */
@Data
@TableName("sys_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="sys_config对象", description="系统配置管理")
public class SysConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**配置键*/
    @ApiModelProperty(value = "配置键")
    private String configKey;

    /**配置值*/
    @ApiModelProperty(value = "配置值")
    private String configValue;

    /**配置名称*/
    @ApiModelProperty(value = "配置名称")
    private String configName;

    /**配置描述*/
    @ApiModelProperty(value = "配置描述")
    private String configDesc;

    /**配置类型*/
    @ApiModelProperty(value = "配置类型")
    private String configType;

    /**是否系统内置 0-否 1-是*/
    @ApiModelProperty(value = "是否系统内置 0-否 1-是")
    private Integer isSystem;

    /**状态 0-禁用 1-启用*/
    @ApiModelProperty(value = "状态 0-禁用 1-启用")
    private Integer status;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private Integer createBy;

    /**创建日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private Integer updateBy;

    /**更新日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
}