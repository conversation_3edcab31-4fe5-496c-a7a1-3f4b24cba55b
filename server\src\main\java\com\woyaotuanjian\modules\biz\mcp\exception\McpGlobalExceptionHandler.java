package com.woyaotuanjian.modules.biz.mcp.exception;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.woyaotuanjian.modules.biz.mcp.dto.McpError;
import com.woyaotuanjian.modules.biz.mcp.dto.McpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;

/**
 * MCP全局异常处理器
 * 统一处理MCP相关的异常并返回标准的MCP错误响应
 */
@RestControllerAdvice(basePackages = "com.woyaotuanjian.modules.biz.mcp.controller")
@Slf4j
public class McpGlobalExceptionHandler {

    /**
     * 处理MCP业务异常
     */
    @ExceptionHandler(McpException.class)
    public ResponseEntity<McpResponse> handleMcpException(McpException e, HttpServletRequest request) {
        String requestId = extractRequestId(request);
        
        // 根据错误类型记录不同级别的日志
        if (e instanceof McpInvalidParamsException) {
            log.warn("MCP参数验证失败: {} [requestId={}]", e.getMessage(), requestId);
        } else if (e instanceof McpMethodNotFoundException) {
            log.warn("MCP方法未找到: {} [requestId={}]", e.getMessage(), requestId);
        } else if (e instanceof McpInternalErrorException) {
            log.error("MCP内部错误: {} [requestId={}]", e.getMessage(), requestId, e);
        } else {
            log.warn("MCP业务异常: {} [requestId={}]", e.getMessage(), requestId);
        }
        
        McpResponse response = McpResponse.error(requestId, e.getMcpError());
        return ResponseEntity.ok(response);
    }

    /**
     * 处理JSON解析异常
     */
    @ExceptionHandler({JsonProcessingException.class, HttpMessageNotReadableException.class})
    public ResponseEntity<McpResponse> handleJsonParseException(Exception e, HttpServletRequest request) {
        String requestId = extractRequestId(request);
        
        log.warn("MCP请求JSON解析失败: {} [requestId={}]", e.getMessage(), requestId);
        
        McpError error = McpError.parseError();
        McpResponse response = McpResponse.error(requestId, error);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理方法参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<McpResponse> handleMethodArgumentTypeMismatchException(
            MethodArgumentTypeMismatchException e, HttpServletRequest request) {
        String requestId = extractRequestId(request);
        
        log.warn("MCP请求参数类型不匹配: parameter={}, value={}, requiredType={} [requestId={}]", 
                e.getName(), e.getValue(), e.getRequiredType().getSimpleName(), requestId);
        
        String message = String.format("参数类型错误: %s 应为 %s 类型", e.getName(), e.getRequiredType().getSimpleName());
        McpError error = McpError.invalidParams(message);
        McpResponse response = McpResponse.error(requestId, error);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理数据库访问异常
     */
    @ExceptionHandler({DataAccessException.class, SQLException.class})
    public ResponseEntity<McpResponse> handleDataAccessException(Exception e, HttpServletRequest request) {
        String requestId = extractRequestId(request);
        
        log.error("MCP数据库访问异常: {} [requestId={}]", e.getMessage(), requestId, e);
        
        // 不向客户端暴露具体的数据库错误信息
        McpError error = McpError.internalError("数据访问异常，请稍后重试");
        McpResponse response = McpResponse.error(requestId, error);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResponseEntity<McpResponse> handleNullPointerException(NullPointerException e, HttpServletRequest request) {
        String requestId = extractRequestId(request);
        
        log.error("MCP空指针异常: {} [requestId={}]", e.getMessage(), requestId, e);
        
        McpError error = McpError.internalError("系统内部错误");
        McpResponse response = McpResponse.error(requestId, error);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<McpResponse> handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
        String requestId = extractRequestId(request);
        
        log.warn("MCP非法参数异常: {} [requestId={}]", e.getMessage(), requestId);
        
        McpError error = McpError.invalidParams(e.getMessage());
        McpResponse response = McpResponse.error(requestId, error);
        return ResponseEntity.ok(response);
    }

    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<McpResponse> handleGenericException(Exception e, HttpServletRequest request) {
        String requestId = extractRequestId(request);
        
        log.error("MCP未处理异常: {} [requestId={}]", e.getMessage(), requestId, e);
        
        McpError error = McpError.internalError("系统内部错误");
        McpResponse response = McpResponse.error(requestId, error);
        return ResponseEntity.ok(response);
    }

    /**
     * 从请求中提取请求ID
     * 尝试从请求体中解析，如果失败则返回null
     */
    private String extractRequestId(HttpServletRequest request) {
        try {
            // 这里可以尝试从请求体中解析ID，但由于异常处理时请求体可能已经被消费
            // 所以返回null，让调用方处理
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}